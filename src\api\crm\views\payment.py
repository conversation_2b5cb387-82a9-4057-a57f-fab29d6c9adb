from core.models import Payment, File
from rest_framework import viewsets, status, filters
from rest_framework.response import Response
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import action
from api.crm.serializers.payment import (
    CrmPaymentBaseSerializer,
    CrmPaymentListItemSerializer,
    CrmPaymentRetrieveSerializer,
    CrmPaymentCreateSerializer,
    CrmPaymentUpdateSerializer,
)
from api.shared.serializers.file import FileSerializer
from api.mixins import AuditMixin, SwaggerTagMixin
from api.crm.services.file import upload_file_to_minio
from api.crm.filters.payment import CrmPaymentFilter
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django_filters.rest_framework import DjangoFilterBackend
from api.paginations import StandardResultsPagination
from api.permissions import IsStaffUser


class CrmPaymentViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    model_class = Payment
    queryset = Payment.objects.filter(deleted=False).order_by("-created_at")
    serializer_class = CrmPaymentBaseSerializer

    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsStaffUser]
    pagination_class = StandardResultsPagination

    filterset_class = CrmPaymentFilter
    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    )
    ordering_fields = ["created_at"]
    search_fields = [
        "order__owner__first_name",
        "order__owner__last_name",
        "order__owner__email",
        "order__owner__phone_number",
    ]

    swagger_tags = ["Payments"]

    def get_serializer(self, *args, **kwargs):
        if self.action == "create":
            return CrmPaymentCreateSerializer(*args, **kwargs)
        elif self.action == "update" or self.action == "partial_update":
            return CrmPaymentUpdateSerializer(*args, **kwargs)
        elif self.action == "retrieve":
            return CrmPaymentRetrieveSerializer(*args, **kwargs)
        elif self.action == "list":
            return CrmPaymentListItemSerializer(*args, **kwargs)
        return CrmPaymentBaseSerializer(*args, **kwargs)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        retrieve_serializer = CrmPaymentRetrieveSerializer(serializer.instance)
        return Response(retrieve_serializer.data, status=status.HTTP_201_CREATED)

    @swagger_auto_schema(
        operation_description="Upload a single voucher file",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["file"],
            properties={
                "file": openapi.Schema(
                    type=openapi.TYPE_FILE, description="The voucher file to upload"
                )
            },
        ),
        responses={
            201: FileSerializer,
            400: openapi.Response(
                description="Bad Request - No file provided or invalid file type",
                examples={"application/json": {"error": "No file has been provided"}},
            ),
        },
    )
    @action(detail=False, methods=["POST"], url_path="upload-voucher")
    def upload_voucher(self, request, *args, **kwargs):
        if "file" not in request.FILES:
            return Response(
                {"error": "No file has been provided"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        file_obj = request.FILES["file"]

        # Validate file type
        allowed_types = ["application/pdf", "image/jpeg", "image/png", "image/jpg"]
        if file_obj.content_type not in allowed_types:
            return Response(
                {
                    "error": (
                        "Invalid file type. Only PDF, JPG, and PNG files are allowed"
                    )
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Validate file size
        max_size = 10 * 1024 * 1024  # 10MB
        if file_obj.size > max_size:
            return Response(
                {"error": "File size too large. Maximum size is 10MB"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            fid, object_name = upload_file_to_minio(file_obj)
            file = File.objects.create(
                fid=fid,
                is_used=False,
                is_private=True,
                name=object_name.split("/")[-1],
                bucket_name="private",
                object_name=object_name,
            )

            data = FileSerializer(file).data
            return Response(data, status=status.HTTP_201_CREATED)

        except Exception as e:
            import logging

            logger = logging.getLogger(__name__)
            logger.error(f"Error uploading voucher: {str(e)}")

            return Response(
                {"error": "Failed to upload voucher. Please try again."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        operation_description="Attach a voucher file to a payment",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["file"],
            properties={
                "file": openapi.Schema(
                    type=openapi.TYPE_FILE,
                    description="The voucher file to attach (PDF, JPG, PNG)",
                )
            },
        ),
        responses={
            200: FileSerializer,
            400: openapi.Response(
                description="Bad Request - No file provided or invalid file type",
                examples={"application/json": {"error": "No file has been provided"}},
            ),
            404: openapi.Response(description="Payment not found"),
        },
    )
    @action(
        detail=True,
        methods=["POST"],
        url_path="attach-voucher",
    )
    def attach_voucher(self, request, *args, **kwargs):
        from django.db import transaction

        payment = self.get_object()

        # Validate file input
        if "file" not in request.FILES:
            return Response(
                {"error": "No file has been provided"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        file_obj = request.FILES["file"]

        # Validate file type (vouchers should be PDF or images)
        allowed_types = [
            "application/pdf",
            "image/jpeg",
            "image/png",
            "image/jpg",
            "image/gif",
            "image/webp",
        ]
        if file_obj.content_type not in allowed_types:
            return Response(
                {
                    "error": (
                        "Invalid file type. Only PDF, JPG, and PNG files are allowed"
                    )
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Validate file size (e.g., max 10MB)
        max_size = 10 * 1024 * 1024  # 10MB
        if file_obj.size > max_size:
            return Response(
                {"error": "File size too large. Maximum size is 10MB"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            with transaction.atomic():
                # Remove existing voucher if present
                old_voucher = payment.voucher
                if old_voucher:
                    payment.voucher = None
                    payment.save()
                    old_voucher.delete()

                # Upload file to MinIO
                fid, object_name = upload_file_to_minio(file_obj)

                # Create File instance
                file_instance = File.objects.create(
                    fid=fid,
                    is_used=True,  # Set to True immediately since it's being attached
                    is_private=True,
                    name=object_name.split("/")[-1],
                    bucket_name="private",
                    object_name=object_name,
                )

                # Attach voucher to payment
                payment.voucher = file_instance
                payment.save()

                return Response(
                    FileSerializer(file_instance).data, status=status.HTTP_200_OK
                )

        except Exception as e:
            # Log the error for debugging
            import logging

            logger = logging.getLogger(__name__)
            logger.error(f"Error attaching voucher to payment {payment.pid}: {str(e)}")

            return Response(
                {"error": "Failed to attach voucher. Please try again."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["DELETE"], url_path="remove-voucher/(?P<fid>[^/.]+)")
    def remove_voucher(self, request, *args, **kwargs):
        fid = kwargs.get("fid")
        file = File.objects.get(fid=fid)
        file.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
