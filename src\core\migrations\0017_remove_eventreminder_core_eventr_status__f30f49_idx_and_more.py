# Generated by Django 5.0.6 on 2025-07-30 23:39

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0016_remove_eventreminder_event_alliance_id_and_more"),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name="eventreminder",
            name="core_eventr_status__f30f49_idx",
        ),
        migrations.RemoveIndex(
            model_name="eventreminder",
            name="core_eventr_status__87bf08_idx",
        ),
        migrations.RemoveField(
            model_name="eventreminder",
            name="email_template",
        ),
        migrations.RemoveField(
            model_name="eventreminder",
            name="scheduled_datetime_email",
        ),
        migrations.RemoveField(
            model_name="eventreminder",
            name="scheduled_datetime_whatsapp",
        ),
        migrations.RemoveField(
            model_name="eventreminder",
            name="variables",
        ),
        migrations.RemoveField(
            model_name="eventreminder",
            name="whatsapp_template",
        ),
        migrations.AddField(
            model_name="eventschedule",
            name="scheduled_datetime_email",
            field=models.DateTimeField(
                blank=True,
                help_text="When to start sending email notifications for this event schedule",
                null=True,
                verbose_name="Email Scheduled DateTime",
            ),
        ),
        migrations.AddField(
            model_name="eventschedule",
            name="scheduled_datetime_whatsapp",
            field=models.DateTimeField(
                blank=True,
                help_text="When to start sending WhatsApp notifications for this event schedule",
                null=True,
                verbose_name="WhatsApp Scheduled DateTime",
            ),
        ),
        migrations.AddField(
            model_name="eventschedule",
            name="whatsapp_template",
            field=models.ForeignKey(
                blank=True,
                help_text="Template for WhatsApp notifications (uses ext_reference as flow ID)",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="event_schedules_whatsapp",
                to="core.template",
                verbose_name="WhatsApp Template",
            ),
        ),
        migrations.AddIndex(
            model_name="eventreminder",
            index=models.Index(
                fields=["status_whatsapp"], name="core_eventr_status__c7ac1b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="eventreminder",
            index=models.Index(
                fields=["status_email"], name="core_eventr_status__ee747b_idx"
            ),
        ),
    ]
