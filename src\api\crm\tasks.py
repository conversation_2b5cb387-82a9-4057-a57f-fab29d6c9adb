from .tasks.classroom import (
    send_classroom_invitation,
    process_classroom_invitation_for_order,
)
from .tasks.event_invitations import (
    send_whatsapp_invitation,
    send_email_invitation,
    schedule_pending_invitations,
    process_new_enrollment_invitations,
    check_tokechat_availability,
    retry_failed_invitations,
)

# Make tasks available at module level
__all__ = [
    "send_classroom_invitation",
    "process_classroom_invitation_for_order",
    "send_whatsapp_invitation",
    "send_email_invitation",
    "schedule_pending_invitations",
    "process_new_enrollment_invitations",
    "check_tokechat_availability",
    "retry_failed_invitations",
]
