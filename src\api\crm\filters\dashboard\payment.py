"""
Payment Dashboard Filters for CRM
Provides filtering capabilities for payment dashboard analytics
"""

from django_filters import rest_framework as filters
from django.db.models import Q
from core.models import Payment, PaymentMethod


class CrmDashboardPaymentFilter(filters.FilterSet):
    """
    Filter for Payment Dashboard
    Supports filtering by date ranges, payment status, currency, and payment methods
    """

    # Date range filters - configurable by type_date parameter
    date_after = filters.DateFilter(method="filter_date_after")
    date_before = filters.DateFilter(method="filter_date_before")

    # Type of date to filter by
    type_date = filters.ChoiceFilter(
        choices=[
            ("created_at", "Created At"),
            ("payment_date", "Payment Date"),
            ("scheduled_payment_date", "Scheduled Payment Date"),
        ],
        method="dummy_method",  # We handle this in date filter methods
        help_text="Type of date to filter by (created_at, payment_date, scheduled_payment_date)",
    )

    # Payment status filter
    status = filters.ChoiceFilter(
        choices=[
            ("all", "All"),
            ("paid", "Paid"),
            ("pending", "Pending"),
        ],
        method="filter_status",
        help_text="Payment status filter",
    )

    # Currency filter
    currency = filters.ChoiceFilter(
        choices=[
            ("pen", "PEN"),
            ("usd", "USD"),
        ],
        field_name="currency",
        lookup_expr="exact",
        help_text="Currency filter",
    )

    # Payment method filter - comma separated list of payment method IDs
    payment_method = filters.CharFilter(
        method="filter_payment_methods",
        help_text="Comma-separated list of payment method IDs",
    )

    # Offerings filter - comma separated list of offering IDs
    offerings = filters.CharFilter(
        method="filter_offerings",
        help_text="Comma-separated list of offering IDs (filters payments from orders containing these offerings)",
    )

    class Meta:
        model = Payment
        fields = [
            "date_after",
            "date_before",
            "type_date",
            "status",
            "currency",
            "payment_method",
            "offerings",
        ]

    def dummy_method(self, queryset, name, value):
        """Dummy method for type_date - handled in date filter methods"""
        return queryset

    def get_date_field(self):
        """Get the date field to filter by based on type_date parameter"""
        type_date = self.data.get("type_date", "created_at")
        valid_types = ["created_at", "payment_date", "scheduled_payment_date"]
        return type_date if type_date in valid_types else "created_at"

    def filter_date_after(self, queryset, name, value):
        """Filter by date after using the specified date type"""
        if not value:
            return queryset

        date_field = self.get_date_field()
        filter_kwargs = {f"{date_field}__gte": value}
        return queryset.filter(**filter_kwargs)

    def filter_date_before(self, queryset, name, value):
        """Filter by date before using the specified date type"""
        if not value:
            return queryset

        date_field = self.get_date_field()
        filter_kwargs = {f"{date_field}__lte": value}
        return queryset.filter(**filter_kwargs)

    def filter_status(self, queryset, name, value):
        """Filter by payment status"""
        if not value or value == "all":
            return queryset

        if value == "paid":
            return queryset.filter(is_paid=True)
        elif value == "pending":
            return queryset.filter(is_paid=False)

        return queryset

    def filter_payment_methods(self, queryset, name, value):
        """Filter by multiple payment methods (comma-separated)"""
        if not value:
            return queryset

        method_ids = [mid.strip() for mid in value.split(",") if mid.strip()]
        if method_ids:
            return queryset.filter(payment_method__pmid__in=method_ids)
        return queryset

    def filter_offerings(self, queryset, name, value):
        """
        Filter by multiple offerings (comma-separated)
        Filters payments that belong to orders containing the specified offerings
        """
        if not value:
            return queryset

        offering_ids = [oid.strip() for oid in value.split(",") if oid.strip()]
        if offering_ids:
            # Filter payments where the order contains items with the specified offerings
            return queryset.filter(
                order__items__offering__oid__in=offering_ids
            ).distinct()
        return queryset
