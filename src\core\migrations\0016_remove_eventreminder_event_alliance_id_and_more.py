# Generated by Django 5.0.6 on 2025-07-25 21:37

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0015_payment_is_lost"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="eventreminder",
            name="event_alliance_id",
        ),
        migrations.RemoveField(
            model_name="eventreminder",
            name="event_name",
        ),
        migrations.RemoveField(
            model_name="eventreminder",
            name="reminder_type",
        ),
        migrations.RemoveField(
            model_name="eventreminder",
            name="send_at",
        ),
        migrations.RemoveField(
            model_name="eventreminder",
            name="status",
        ),
        migrations.RemoveField(
            model_name="eventreminder",
            name="template_id",
        ),
        migrations.AddField(
            model_name="eventreminder",
            name="email_template",
            field=models.ForeignKey(
                blank=True,
                help_text="Template for email invitation",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="email_reminders",
                to="core.template",
                verbose_name="Email Template",
            ),
        ),
        migrations.Add<PERSON>ield(
            model_name="eventreminder",
            name="enrollment",
            field=models.ForeignKey(
                blank=True,
                help_text="The enrollment this reminder is associated with",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="event_reminders",
                to="core.eventscheduleenrollment",
                verbose_name="Event Schedule Enrollment",
            ),
        ),
        migrations.AddField(
            model_name="eventreminder",
            name="last_error_email",
            field=models.TextField(
                blank=True,
                help_text="Last error message for email invitation",
                null=True,
                verbose_name="Email Last Error",
            ),
        ),
        migrations.AddField(
            model_name="eventreminder",
            name="last_error_whatsapp",
            field=models.TextField(
                blank=True,
                help_text="Last error message for WhatsApp invitation",
                null=True,
                verbose_name="WhatsApp Last Error",
            ),
        ),
        migrations.AddField(
            model_name="eventreminder",
            name="retry_count_email",
            field=models.PositiveIntegerField(
                default=0,
                help_text="Number of retry attempts for email invitation",
                verbose_name="Email Retry Count",
            ),
        ),
        migrations.AddField(
            model_name="eventreminder",
            name="retry_count_whatsapp",
            field=models.PositiveIntegerField(
                default=0,
                help_text="Number of retry attempts for WhatsApp invitation",
                verbose_name="WhatsApp Retry Count",
            ),
        ),
        migrations.AddField(
            model_name="eventreminder",
            name="scheduled_datetime_email",
            field=models.DateTimeField(
                blank=True,
                help_text="When to send the email invitation",
                null=True,
                verbose_name="Email Scheduled DateTime",
            ),
        ),
        migrations.AddField(
            model_name="eventreminder",
            name="scheduled_datetime_whatsapp",
            field=models.DateTimeField(
                blank=True,
                help_text="When to send the WhatsApp invitation",
                null=True,
                verbose_name="WhatsApp Scheduled DateTime",
            ),
        ),
        migrations.AddField(
            model_name="eventreminder",
            name="sent_at_email",
            field=models.DateTimeField(
                blank=True,
                help_text="When the email invitation was actually sent",
                null=True,
                verbose_name="Email Sent At",
            ),
        ),
        migrations.AddField(
            model_name="eventreminder",
            name="sent_at_whatsapp",
            field=models.DateTimeField(
                blank=True,
                help_text="When the WhatsApp invitation was actually sent",
                null=True,
                verbose_name="WhatsApp Sent At",
            ),
        ),
        migrations.AddField(
            model_name="eventreminder",
            name="status_email",
            field=models.CharField(
                choices=[
                    ("PENDING", "Pending"),
                    ("SENT", "Sent"),
                    ("FAILED", "Failed"),
                    ("RETRYING", "Retrying"),
                    ("CANCELLED", "Cancelled"),
                ],
                default="PENDING",
                max_length=10,
                verbose_name="Email Status",
            ),
        ),
        migrations.AddField(
            model_name="eventreminder",
            name="status_whatsapp",
            field=models.CharField(
                choices=[
                    ("PENDING", "Pending"),
                    ("SENT", "Sent"),
                    ("FAILED", "Failed"),
                    ("RETRYING", "Retrying"),
                    ("CANCELLED", "Cancelled"),
                ],
                default="PENDING",
                max_length=10,
                verbose_name="WhatsApp Status",
            ),
        ),
        migrations.AddField(
            model_name="eventreminder",
            name="whatsapp_template",
            field=models.ForeignKey(
                blank=True,
                help_text="Template for WhatsApp invitation (uses ext_reference as flow ID)",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="whatsapp_reminders",
                to="core.template",
                verbose_name="WhatsApp Template",
            ),
        ),
        migrations.AddField(
            model_name="template",
            name="ext_reference",
            field=models.CharField(
                blank=True,
                null=True,
                verbose_name="External Reference for integrations, ej: flow ID's",
            ),
        ),
        migrations.AlterField(
            model_name="eventreminder",
            name="variables",
            field=models.JSONField(
                blank=True,
                help_text="Variables to be used in templates",
                null=True,
                verbose_name="Template Variables",
            ),
        ),
        migrations.AlterField(
            model_name="template",
            name="body_text",
            field=models.TextField(
                blank=True, max_length=1024, null=True, verbose_name="Body Text"
            ),
        ),
        migrations.AddIndex(
            model_name="eventreminder",
            index=models.Index(
                fields=["status_whatsapp", "scheduled_datetime_whatsapp"],
                name="core_eventr_status__f30f49_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="eventreminder",
            index=models.Index(
                fields=["status_email", "scheduled_datetime_email"],
                name="core_eventr_status__87bf08_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="eventreminder",
            index=models.Index(
                fields=["enrollment"], name="core_eventr_enrollm_1abda8_idx"
            ),
        ),
    ]
