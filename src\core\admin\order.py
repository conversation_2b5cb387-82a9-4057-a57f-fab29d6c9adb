from django.contrib import admin
from core.models import Order, OrderItem


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    readonly_fields = [
        "created_at",
        "updated_at",
        "deleted_at",
        "deleted_by",
    ]

    list_display = [
        "owner",
    ]
    list_filter = ("deleted", "stage")
    search_fields = ("oid", "owner__first_name", "owner__last_name", "owner__email")


@admin.register(OrderItem)
class OrderItemAdmin(admin.ModelAdmin):
    readonly_fields = [
        "created_at",
        "updated_at",
        "deleted_at",
        "deleted_by",
    ]

    list_display = [
        "order",
        "offering",
        "unit_price",
        "quantity",
    ]
    list_filter = ("deleted",)
    search_fields = ("order",)
