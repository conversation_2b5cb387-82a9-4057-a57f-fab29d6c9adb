"""
Payment Dashboard Views for CRM
Provides analytics endpoints for payment dashboard
"""

from django.db.models import Q, Sum, Case, When, DecimalField, F, Count, Value
from django.utils import timezone
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from decimal import Decimal
from core.models import Payment, PaymentMethod, Offering
from api.mixins import AuditMixin, SwaggerTagMixin
from services.cache.redis import CacheManager
from api.crm.filters.dashboard.payment import CrmDashboardPaymentFilter
from api.crm.serializers.dashboard.payment import (
    CrmDashboardPaymentSerializer,
    CrmDashboardPaymentFilterOptionsSerializer,
    CrmDashboardPaymentHistoricalSerializer,
)
from api.crm._utils.dashboard import (
    DashboardUtils,
    CurrencyConverter,
)
from datetime import timedelta
from dateutil.relativedelta import relativedelta
from django.conf import settings


class CrmDashboardPaymentViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.GenericViewSet,
):
    """
    ViewSet for Payment Dashboard Analytics
    Provides various endpoints for payment dashboard statistics and charts
    """

    model_class = Payment
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]
    filterset_class = CrmDashboardPaymentFilter
    swagger_tags = ["CRM Dashboard"]
    serializer_class = CrmDashboardPaymentSerializer
    currency_converter = CurrencyConverter()

    def get_serializer_class(self):
        if self.action == "invalidate_cache":
            return None
        return super().get_serializer_class()

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.cache_manager = CacheManager("crm_dashboard_payment")
        self.cache_timeout = 60 * 5  # 5 minutes

    def initial(self, request, *args, **kwargs):
        """
        Check for force_refresh parameter before any endpoint execution
        """
        super().initial(request, *args, **kwargs)

        # Force refresh cache if requested
        if request.GET.get("force_refresh", "").lower() == "true" and not getattr(
            self, "_cache_invalidated", False
        ):
            self.cache_manager.delete_all()
            self._cache_invalidated = True

    def get_queryset(self):
        """
        Get base queryset for payments (non-deleted payments only)
        """
        return Payment.objects.filter(deleted=False)

    def get_filtered_queryset(self):
        """
        Get filtered queryset based on request filters
        """
        queryset = self.get_queryset()
        filterset = self.filterset_class(self.request.GET, queryset=queryset)
        return filterset.qs if filterset.is_valid() else queryset

    def get_cache_key_params(self):
        """
        Get parameters for cache key generation
        """
        return {
            "date_after": self.request.GET.get("date_after", ""),
            "date_before": self.request.GET.get("date_before", ""),
            "type_date": self.request.GET.get("type_date", "created_at"),
            "status": self.request.GET.get("status", "all"),
            "currency": self.request.GET.get("currency", ""),
            "payment_method": self.request.GET.get("payment_method", ""),
            "offerings": self.request.GET.get("offerings", ""),
        }

    def get_cache_key_params_excluding_dates(self):
        """
        Get parameters for cache key generation excluding date filters
        """
        return {
            "status": self.request.GET.get("status", "all"),
            "currency": self.request.GET.get("currency", ""),
            "payment_method": self.request.GET.get("payment_method", ""),
            "offerings": self.request.GET.get("offerings", ""),
            "period": self.request.GET.get("period", "weekly"),
        }

    # ==== Utilities ====

    def _get_report_dates(self):
        """Get current and previous period dates"""
        return DashboardUtils.get_report_dates(self.request)

    def _get_queryset_excluding_filters(self, exclude_fields):
        """Get queryset excluding specific filters"""
        return DashboardUtils.get_queryset_excluding_filters(
            self.get_queryset(), self.filterset_class, self.request, exclude_fields
        )

    def _calculate_amount_totals(self, payments_queryset):
        """
        Calculate total amounts in PEN and USD with conversion
        """
        # Get conversion rate once
        conversion_rate = CurrencyConverter.get_conversion_rate()

        # Aggregate amounts by currency
        amounts_aggregation = payments_queryset.aggregate(
            total_amount_usd=Sum(
                Case(
                    When(currency=Payment.USD_CURRENCY, then=F("amount")),
                    default=Value(0),
                    output_field=DecimalField(max_digits=15, decimal_places=2),
                )
            ),
            total_amount_pen=Sum(
                Case(
                    When(currency=Payment.PEN_CURRENCY, then=F("amount")),
                    default=Value(0),
                    output_field=DecimalField(max_digits=15, decimal_places=2),
                )
            ),
        )

        # Calculate totals
        total_amount_usd = amounts_aggregation["total_amount_usd"] or Decimal("0.00")
        total_amount_pen = amounts_aggregation["total_amount_pen"] or Decimal("0.00")

        # Convert USD to PEN and calculate total
        usd_in_pen = total_amount_usd * conversion_rate
        total_amount = total_amount_pen + usd_in_pen

        return {
            "total_amount_usd": total_amount_usd,
            "total_amount_pen": total_amount_pen,
            "total_amount": total_amount,
        }

    # ==== CALCULATION FUNCTIONS ====

    def calculate_payment_summary_stats(self):
        """
        Calculate payment summary statistics
        """
        filtered_queryset = self.get_filtered_queryset()

        # 1. Total registered amount (all payments)
        total_registered = self._calculate_amount_totals(filtered_queryset)

        # 2. Total debt paid amount (paid debts - not first_payment)
        debt_paid_queryset = filtered_queryset.filter(
            is_paid=True, is_first_payment=False
        )
        total_debt_paid = self._calculate_amount_totals(debt_paid_queryset)

        # 3. Total pending debt amount (pending debts - not first_payment)
        debt_pending_queryset = filtered_queryset.filter(
            is_paid=False, is_first_payment=False
        )
        total_pending_debt = self._calculate_amount_totals(debt_pending_queryset)

        # 4. Total paid first payment amount
        first_payment_paid_queryset = filtered_queryset.filter(
            is_paid=True, is_first_payment=True
        )
        total_paid_first_payment = self._calculate_amount_totals(
            first_payment_paid_queryset
        )

        # 5. Total pending first payment amount
        first_payment_pending_queryset = filtered_queryset.filter(
            is_paid=False, is_first_payment=True
        )
        total_pending_first_payment = self._calculate_amount_totals(
            first_payment_pending_queryset
        )

        return {
            "total_registered_amount": {
                "pen": round(total_registered["total_amount_pen"], 2),
                "usd": round(total_registered["total_amount_usd"], 2),
                "total": round(total_registered["total_amount"], 2),
            },
            "total_debt_paid_amount": {
                "pen": round(total_debt_paid["total_amount_pen"], 2),
                "usd": round(total_debt_paid["total_amount_usd"], 2),
                "total": round(total_debt_paid["total_amount"], 2),
            },
            "total_pending_debt_amount": {
                "pen": round(total_pending_debt["total_amount_pen"], 2),
                "usd": round(total_pending_debt["total_amount_usd"], 2),
                "total": round(total_pending_debt["total_amount"], 2),
            },
            "total_paid_first_payment_amount": {
                "pen": round(total_paid_first_payment["total_amount_pen"], 2),
                "usd": round(total_paid_first_payment["total_amount_usd"], 2),
                "total": round(total_paid_first_payment["total_amount"], 2),
            },
            "total_pending_first_payment_amount": {
                "pen": round(total_pending_first_payment["total_amount_pen"], 2),
                "usd": round(total_pending_first_payment["total_amount_usd"], 2),
                "total": round(total_pending_first_payment["total_amount"], 2),
            },
        }

    def calculate_collection_performance_metrics(self):
        """
        Calculate collection performance metrics
        """
        filtered_queryset = self.get_filtered_queryset()

        # 1. Debt collection success rate (debts that are not first_payment and not lost)
        debt_collection_queryset = filtered_queryset.filter(
            is_first_payment=False, is_lost=False
        )

        total_debts = debt_collection_queryset.count()
        successful_debts = debt_collection_queryset.filter(is_paid=True).count()

        debt_collection_rate = (
            (successful_debts / total_debts * 100) if total_debts > 0 else 0
        )

        # 2. Average days to pay (for paid debts)
        paid_debts = debt_collection_queryset.filter(
            is_paid=True,
            payment_date__isnull=False,
            scheduled_payment_date__isnull=False,
        )

        # Calculate average days manually since Django doesn't handle timedelta well in aggregates
        total_days = 0
        count = 0

        for payment in paid_debts:
            if payment.payment_date and payment.scheduled_payment_date:
                diff = payment.payment_date - payment.scheduled_payment_date
                total_days += diff.days
                count += 1

        average_payment_days = (
            Decimal(str(total_days / count)) if count > 0 else Decimal("0.00")
        )

        # 3. First payment conversion rate
        first_payments_queryset = filtered_queryset.filter(is_first_payment=True)
        total_first_payments = first_payments_queryset.count()
        paid_first_payments = first_payments_queryset.filter(is_paid=True).count()

        first_payment_conversion_rate = (
            (paid_first_payments / total_first_payments * 100)
            if total_first_payments > 0
            else 0
        )

        # 4. Recovery rate (scheduled payments paid after due date)
        recovery_queryset = filtered_queryset.filter(
            is_paid=True,
            is_first_payment=False,
            payment_date__isnull=False,
            scheduled_payment_date__isnull=False,
        )

        # Count payments made after scheduled date
        late_paid = recovery_queryset.filter(
            payment_date__gt=F("scheduled_payment_date")
        ).count()

        total_scheduled = recovery_queryset.count()
        recovery_rate = (
            (late_paid / total_scheduled * 100) if total_scheduled > 0 else 0
        )

        return {
            "debt_collection_rate": {
                "percentage": round(debt_collection_rate, 2),
                "successful": successful_debts,
                "total": total_debts,
            },
            "average_payment_days": round(average_payment_days, 2),
            "payment_conversion_rate": {
                "percentage": round(first_payment_conversion_rate, 2),
                "paid": paid_first_payments,
                "total": total_first_payments,
            },
            "recovery_rate": {
                "percentage": round(recovery_rate, 2),
                "late_paid": late_paid,
                "total_scheduled": total_scheduled,
            },
        }

    def calculate_historical_payments(self):
        """
        Calculate historical payment data (accumulative and discrete) by period
        Ignores date filters from the original request
        """
        # Get period parameter
        period = self.request.GET.get("period", "weekly")
        valid_periods = ["daily", "weekly", "monthly", "yearly"]
        if period not in valid_periods:
            period = "weekly"

        # Get filtered queryset excluding date filters
        filtered_queryset = self._get_queryset_excluding_filters(
            ["date_after", "date_before", "type_date"]
        )

        # Only consider paid payments
        paid_payments = filtered_queryset.filter(is_paid=True)

        # Get conversion rate
        conversion_rate = CurrencyConverter.get_conversion_rate()

        # Calculate periods based on the period type
        current_date = timezone.now().date()

        if period == "daily":
            # Last 7 days
            periods_count = 7
            period_data = []

            for i in range(periods_count - 1, -1, -1):
                target_date = current_date - timedelta(days=i)

                period_data.append(
                    {
                        "start_date": target_date,
                        "end_date": target_date,
                        "label": target_date.strftime("%A, %B %d, %Y"),
                        "short_label": target_date.strftime("%m/%d"),
                    }
                )

        elif period == "weekly":
            # Last 12 weeks
            periods_count = 12
            period_data = []

            for i in range(periods_count - 1, -1, -1):
                start_date = current_date - timedelta(weeks=i + 1)
                end_date = current_date - timedelta(weeks=i)

                # Adjust to start on Monday and end on Sunday
                start_date = start_date - timedelta(days=start_date.weekday())
                end_date = start_date + timedelta(days=6)

                period_data.append(
                    {
                        "start_date": start_date,
                        "end_date": end_date,
                        "label": f"Week {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}",
                        "short_label": f"W{start_date.strftime('%m/%d')}",
                    }
                )

        elif period == "monthly":
            # Last 12 months
            periods_count = 12
            period_data = []

            for i in range(periods_count - 1, -1, -1):
                target_date = current_date - relativedelta(months=i)
                start_date = target_date.replace(day=1)
                end_date = (start_date + relativedelta(months=1)) - timedelta(days=1)

                period_data.append(
                    {
                        "start_date": start_date,
                        "end_date": end_date,
                        "label": start_date.strftime("%B %Y"),
                        "short_label": start_date.strftime("%b %Y"),
                    }
                )

        else:  # yearly
            # Last 5 years
            periods_count = 5
            period_data = []

            for i in range(periods_count - 1, -1, -1):
                year = current_date.year - i
                start_date = timezone.datetime(year, 1, 1).date()
                end_date = timezone.datetime(year, 12, 31).date()

                period_data.append(
                    {
                        "start_date": start_date,
                        "end_date": end_date,
                        "label": str(year),
                        "short_label": str(year),
                    }
                )

        # Calculate amounts for each period
        historical_data = []
        cumulative_usd = Decimal("0.00")
        cumulative_pen = Decimal("0.00")
        cumulative_total = Decimal("0.00")

        for period_info in period_data:
            # Filter payments for this period using payment_date
            period_payments = paid_payments.filter(
                payment_date__gte=period_info["start_date"],
                payment_date__lte=period_info["end_date"],
                payment_date__isnull=False,
            )

            # Calculate amounts for this period
            period_totals = self._calculate_amount_totals(period_payments)

            discrete_usd = period_totals["total_amount_usd"]
            discrete_pen = period_totals["total_amount_pen"]
            discrete_total = period_totals["total_amount"]

            # Update cumulative totals
            cumulative_usd += discrete_usd
            cumulative_pen += discrete_pen
            cumulative_total += discrete_total

            historical_data.append(
                {
                    "period": period_info["label"],
                    "short_period": period_info["short_label"],
                    "start_date": period_info["start_date"].isoformat(),
                    "end_date": period_info["end_date"].isoformat(),
                    "discrete": {
                        "usd": round(discrete_usd, 2),
                        "pen": round(discrete_pen, 2),
                        "total": round(discrete_total, 2),
                    },
                    "cumulative": {
                        "usd": round(cumulative_usd, 2),
                        "pen": round(cumulative_pen, 2),
                        "total": round(cumulative_total, 2),
                    },
                }
            )

        return {
            "period_type": period,
            "total_periods": len(historical_data),
            "currency_exchange_rate": conversion_rate,
            "data": historical_data,
        }

    def calculate_historical_payment_methods(self):
        """
        Calculate historical payment method data (accumulative and discrete) by period
        Shows evolution of payments by payment method over time
        Ignores date filters from the original request
        """
        # Get period parameter
        period = self.request.GET.get("period", "weekly")
        valid_periods = ["daily", "weekly", "monthly", "yearly"]
        if period not in valid_periods:
            period = "weekly"

        # Get filtered queryset excluding date filters
        filtered_queryset = self._get_queryset_excluding_filters(
            ["date_after", "date_before", "type_date"]
        )

        # Only consider paid payments
        paid_payments = filtered_queryset.filter(is_paid=True)

        # Get conversion rate
        conversion_rate = CurrencyConverter.get_conversion_rate()

        # Get all payment methods that have been used
        payment_methods = PaymentMethod.objects.filter(
            payments__in=paid_payments, deleted=False
        ).distinct()

        # Calculate periods based on the period type (reuse logic from historical_payments)
        current_date = timezone.now().date()

        if period == "daily":
            periods_count = 7
            period_data = []
            for i in range(periods_count - 1, -1, -1):
                target_date = current_date - timedelta(days=i)
                period_data.append(
                    {
                        "start_date": target_date,
                        "end_date": target_date,
                        "label": target_date.strftime("%A, %B %d, %Y"),
                        "short_label": target_date.strftime("%m/%d"),
                    }
                )
        elif period == "weekly":
            periods_count = 12
            period_data = []
            for i in range(periods_count - 1, -1, -1):
                start_date = current_date - timedelta(weeks=i + 1)
                end_date = current_date - timedelta(weeks=i)
                start_date = start_date - timedelta(days=start_date.weekday())
                end_date = start_date + timedelta(days=6)
                period_data.append(
                    {
                        "start_date": start_date,
                        "end_date": end_date,
                        "label": f"Week {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}",
                        "short_label": f"W{start_date.strftime('%m/%d')}",
                    }
                )
        elif period == "monthly":
            periods_count = 12
            period_data = []
            for i in range(periods_count - 1, -1, -1):
                target_date = current_date - relativedelta(months=i)
                start_date = target_date.replace(day=1)
                end_date = (start_date + relativedelta(months=1)) - timedelta(days=1)
                period_data.append(
                    {
                        "start_date": start_date,
                        "end_date": end_date,
                        "label": start_date.strftime("%B %Y"),
                        "short_label": start_date.strftime("%b %Y"),
                    }
                )
        else:  # yearly
            periods_count = 5
            period_data = []
            for i in range(periods_count - 1, -1, -1):
                year = current_date.year - i
                start_date = timezone.datetime(year, 1, 1).date()
                end_date = timezone.datetime(year, 12, 31).date()
                period_data.append(
                    {
                        "start_date": start_date,
                        "end_date": end_date,
                        "label": str(year),
                        "short_label": str(year),
                    }
                )

        # Calculate amounts for each period and payment method
        historical_data = []
        payment_method_cumulatives = {}

        # Initialize cumulative tracking for each payment method
        for method in payment_methods:
            payment_method_cumulatives[method.pmid] = {
                "usd": Decimal("0.00"),
                "pen": Decimal("0.00"),
                "total": Decimal("0.00"),
            }

        for period_info in period_data:
            period_methods_data = []

            for method in payment_methods:
                # Filter payments for this period and payment method
                period_method_payments = paid_payments.filter(
                    payment_date__gte=period_info["start_date"],
                    payment_date__lte=period_info["end_date"],
                    payment_date__isnull=False,
                    payment_method=method,
                )

                # Calculate amounts for this period and method
                method_totals = self._calculate_amount_totals(period_method_payments)

                discrete_usd = method_totals["total_amount_usd"]
                discrete_pen = method_totals["total_amount_pen"]
                discrete_total = method_totals["total_amount"]

                # Update cumulative totals for this method
                payment_method_cumulatives[method.pmid]["usd"] += discrete_usd
                payment_method_cumulatives[method.pmid]["pen"] += discrete_pen
                payment_method_cumulatives[method.pmid]["total"] += discrete_total

                period_methods_data.append(
                    {
                        "payment_method": {
                            "pmid": str(method.pmid),
                            "name": method.name,
                        },
                        "discrete": {
                            "usd": round(discrete_usd, 2),
                            "pen": round(discrete_pen, 2),
                            "total": round(discrete_total, 2),
                        },
                        "cumulative": {
                            "usd": round(
                                payment_method_cumulatives[method.pmid]["usd"], 2
                            ),
                            "pen": round(
                                payment_method_cumulatives[method.pmid]["pen"], 2
                            ),
                            "total": round(
                                payment_method_cumulatives[method.pmid]["total"], 2
                            ),
                        },
                    }
                )

            historical_data.append(
                {
                    "period": period_info["label"],
                    "short_period": period_info["short_label"],
                    "start_date": period_info["start_date"].isoformat(),
                    "end_date": period_info["end_date"].isoformat(),
                    "payment_methods": period_methods_data,
                }
            )

        return {
            "period_type": period,
            "total_periods": len(historical_data),
            "currency_exchange_rate": conversion_rate,
            "data": historical_data,
        }

    def calculate_payment_methods_distribution(self):
        """
        Calculate distribution of payments by payment method
        Respects general filters (not periods)
        """
        filtered_queryset = self.get_filtered_queryset().filter(is_paid=True)

        # Get total count for percentage calculation
        total_payments = filtered_queryset.count()

        if total_payments == 0:
            return {"total_payments": 0, "data": []}

        # Group by payment method and count
        method_distribution = []

        # Get all payment methods used in filtered payments
        methods_with_counts = (
            filtered_queryset.values("payment_method__pmid", "payment_method__name")
            .annotate(count=Count("pid"))
            .order_by("-count")
        )

        # Handle payments without payment method (null)
        null_method_count = filtered_queryset.filter(
            payment_method__isnull=True
        ).count()
        if null_method_count > 0:
            methods_with_counts = list(methods_with_counts) + [
                {
                    "payment_method__pmid": None,
                    "payment_method__name": "No Payment Method",
                    "count": null_method_count,
                }
            ]

        for method_data in methods_with_counts:
            count = method_data["count"]
            percentage = (count / total_payments * 100) if total_payments > 0 else 0

            method_distribution.append(
                {
                    "payment_method": {
                        "pmid": (
                            str(method_data["payment_method__pmid"])
                            if method_data["payment_method__pmid"]
                            else None
                        ),
                        "name": method_data["payment_method__name"]
                        or "No Payment Method",
                    },
                    "count": count,
                    "percentage": round(percentage, 2),
                }
            )

        return {"total_payments": total_payments, "data": method_distribution}

    def calculate_currency_distribution(self):
        """
        Calculate distribution of payments by currency
        Respects general filters (not periods)
        """
        filtered_queryset = self.get_filtered_queryset().filter(is_paid=True)

        # Get total count for percentage calculation
        total_payments = filtered_queryset.count()

        if total_payments == 0:
            return {"total_payments": 0, "data": []}

        # Group by currency and count
        currency_counts = (
            filtered_queryset.values("currency")
            .annotate(count=Count("pid"))
            .order_by("-count")
        )

        currency_distribution = []

        for currency_data in currency_counts:
            count = currency_data["count"]
            percentage = (count / total_payments * 100) if total_payments > 0 else 0

            currency_label = {
                Payment.PEN_CURRENCY: "PEN",
                Payment.USD_CURRENCY: "USD",
            }.get(currency_data["currency"], currency_data["currency"].upper())

            currency_distribution.append(
                {
                    "currency": {
                        "code": currency_data["currency"],
                        "label": currency_label,
                    },
                    "count": count,
                    "percentage": round(percentage, 2),
                }
            )

        return {"total_payments": total_payments, "data": currency_distribution}

    def get_last_payments(self):
        """
        Get the last 15 payments that were made
        Respects general filters (not periods)
        Orders by payment_date descending
        """
        from api.crm.serializers.payment import CrmPaymentListItemSerializer

        filtered_queryset = (
            self.get_filtered_queryset()
            .filter(is_paid=True, payment_date__isnull=False)
            .order_by("-payment_date")[:15]
        )

        # Serialize the payments
        serializer = CrmPaymentListItemSerializer(filtered_queryset, many=True)

        return {"total_returned": len(filtered_queryset), "data": serializer.data}

    def get_filter_options(self):
        """
        Get available filter options for the frontend
        """
        # Payment methods
        payment_methods = PaymentMethod.objects.filter(deleted=False)

        # Offerings
        offerings = Offering.objects.filter(deleted=False)

        # Currency options
        currencies = [
            {"value": "pen", "label": "PEN"},
            {"value": "usd", "label": "USD"},
        ]

        # Status options
        status_options = [
            {"value": "all", "label": "All"},
            {"value": "paid", "label": "Paid"},
            {"value": "pending", "label": "Pending"},
        ]

        # Type date options
        type_date_options = [
            {"value": "created_at", "label": "Created At"},
            {"value": "payment_date", "label": "Payment Date"},
            {"value": "scheduled_payment_date", "label": "Scheduled Payment Date"},
        ]

        return {
            "payment_methods": payment_methods,
            "offerings": offerings,
            "currencies": currencies,
            "status_options": status_options,
            "type_date_options": type_date_options,
        }

    # ==== DASHBOARD ENDPOINTS ====

    @action(detail=False, methods=["GET"], url_path="summary")
    def summary(self, request):
        """
        Get payment dashboard summary statistics
        """
        cache_key = f"summary_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data is not None:
            return Response(cached_data)

        try:
            # Calculate summary statistics
            summary_stats = self.calculate_payment_summary_stats()
            performance_metrics = self.calculate_collection_performance_metrics()

            # Combine all data
            data = {
                **summary_stats,
                **performance_metrics,
                "currency_exchange_rate": CurrencyConverter.get_conversion_rate(),
                "filters_applied": self.get_cache_key_params(),
            }

            # Cache the result
            self.cache_manager.set(cache_key, data, timeout=self.cache_timeout)

            return Response(data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating payment summary: {str(e)}"}, status=500
            )

    @action(detail=False, methods=["GET"], url_path="filter-options")
    def filter_options(self, request):
        """
        Get available filter options for the payment dashboard
        """
        cache_key = "filter_options"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data is not None:
            return Response(cached_data)

        try:
            data = self.get_filter_options()

            # Cache for longer since this data doesn't change frequently
            self.cache_manager.set(cache_key, data, timeout=60 * 30)  # 30 minutes

            serializer = CrmDashboardPaymentFilterOptionsSerializer(data)
            return Response(serializer.data)

        except Exception as e:
            return Response(
                {"error": f"Error getting filter options: {str(e)}"}, status=500
            )

    @action(detail=False, methods=["GET"], url_path="historical-paids")
    def historical_paids(self, request):
        """
        Get historical payment data (accumulative and discrete) by period
        Ignores date filters and focuses on paid payments only

        Query Parameters:
        - period: weekly, monthly, yearly (default: weekly)
        - Other filters (offerings, payment_method, currency, status) are applied
        """
        cache_key = (
            f"historical_paids_{hash(str(self.get_cache_key_params_excluding_dates()))}"
        )
        cached_data = self.cache_manager.get(cache_key)

        if cached_data is not None:
            return Response(cached_data)

        try:
            # Calculate historical payment data
            data = self.calculate_historical_payments()

            # Add filter information
            data["filters_applied"] = self.get_cache_key_params_excluding_dates()

            # Cache the result
            self.cache_manager.set(cache_key, data, timeout=self.cache_timeout)

            return Response(data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating historical payments: {str(e)}"},
                status=500,
            )

    @action(detail=False, methods=["GET"], url_path="historical-payment-methods")
    def historical_payment_methods(self, request):
        """
        Get historical payment method data (accumulative and discrete) by period
        Shows evolution of payments by payment method over time
        Ignores date filters and focuses on paid payments only

        Query Parameters:
        - period: daily, weekly, monthly, yearly (default: weekly)
        - Other filters (offerings, payment_method, currency, status) are applied
        """
        cache_key = f"historical_payment_methods_{hash(str(self.get_cache_key_params_excluding_dates()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data is not None:
            return Response(cached_data)

        try:
            # Calculate historical payment method data
            data = self.calculate_historical_payment_methods()

            # Add filter information
            data["filters_applied"] = self.get_cache_key_params_excluding_dates()

            # Cache the result
            self.cache_manager.set(cache_key, data, timeout=self.cache_timeout)

            return Response(data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating historical payment methods: {str(e)}"},
                status=500,
            )

    @action(detail=False, methods=["GET"], url_path="payment-methods-distribution")
    def payment_methods_distribution(self, request):
        """
        Get distribution of payments by payment method
        Respects general filters and shows count and percentage for each method
        """
        cache_key = (
            f"payment_methods_distribution_{hash(str(self.get_cache_key_params()))}"
        )
        cached_data = self.cache_manager.get(cache_key)

        if cached_data is not None:
            return Response(cached_data)

        try:
            # Calculate payment methods distribution
            data = self.calculate_payment_methods_distribution()

            # Add filter information
            data["filters_applied"] = self.get_cache_key_params()

            # Cache the result
            self.cache_manager.set(cache_key, data, timeout=self.cache_timeout)

            return Response(data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating payment methods distribution: {str(e)}"},
                status=500,
            )

    @action(detail=False, methods=["GET"], url_path="currency-distribution")
    def currency_distribution(self, request):
        """
        Get distribution of payments by currency
        Respects general filters and shows count and percentage for each currency
        """
        cache_key = f"currency_distribution_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data is not None:
            return Response(cached_data)

        try:
            # Calculate currency distribution
            data = self.calculate_currency_distribution()

            # Add filter information
            data["filters_applied"] = self.get_cache_key_params()

            # Cache the result
            self.cache_manager.set(cache_key, data, timeout=self.cache_timeout)

            return Response(data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating currency distribution: {str(e)}"},
                status=500,
            )

    @action(detail=False, methods=["GET"], url_path="last-payments")
    def last_payments(self, request):
        """
        Get the last 15 payments that were made
        Respects general filters and orders by payment_date descending
        """
        cache_key = f"last_payments_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data is not None:
            return Response(cached_data)

        try:
            # Get last payments
            data = self.get_last_payments()

            # Add filter information
            data["filters_applied"] = self.get_cache_key_params()

            # Cache the result (shorter timeout for recent data)
            self.cache_manager.set(cache_key, data, timeout=60 * 2)  # 2 minutes

            return Response(data)

        except Exception as e:
            return Response(
                {"error": f"Error getting last payments: {str(e)}"},
                status=500,
            )

    # ==== INVALIDATE CACHE ENDPOINT ====
    @action(detail=False, methods=["POST"], url_path="invalidate-cache")
    def invalidate_cache(self, request):
        """
        Invalidate all cached data for payment dashboard
        """
        try:
            self.cache_manager.delete_all()
            return Response(
                {"message": "Payment dashboard cache invalidated successfully"}
            )
        except Exception as e:
            return Response(
                {"error": f"Error invalidating cache: {str(e)}"}, status=500
            )
