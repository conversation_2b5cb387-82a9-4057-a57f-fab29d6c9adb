import uuid
from django.db import models
from django.contrib.auth.models import AbstractUser
from core.models.base import AuditBaseModel
from django.utils.translation import gettext_lazy as _


class User(AbstractUser, AuditBaseModel):
    """
    User model. This model would be used to store user information, but in CRM
    this would be used to store the Contact information.
    """

    STUDENT_OCUPATION = "student"
    EMPLOYEE_OCUPATION = "employee"
    INDEPENDENT_OCUPATION = "independent"

    OCUPATION_CHOICES = (
        (STUDENT_OCUPATION, _("Student")),
        (EMPLOYEE_OCUPATION, _("Employee")),
        (INDEPENDENT_OCUPATION, _("Independent")),
    )

    uid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    email = models.EmailField(
        null=True,
        blank=True,
        unique=True,
        max_length=128,
        help_text=_("Email address"),
    )

    phone_number = models.Char<PERSON><PERSON>(
        max_length=64,
        blank=True,
        null=True,
        unique=True,
        help_text=_("Phone number & country code"),
    )
    id_number = models.CharField(
        max_length=40,
        blank=True,
        null=True,
        help_text=_("ID number or passport number"),
    )
    profile_photo = models.ForeignKey(
        "core.File",
        on_delete=models.SET_NULL,
        related_name="users",
        blank=True,
        null=True,
        verbose_name=_("Profile Photo"),
    )

    ocupation = models.CharField(
        max_length=52,
        blank=True,
        null=True,
        choices=OCUPATION_CHOICES,
        help_text=_("Ocupation"),
    )
    company = models.CharField(
        max_length=128,
        blank=True,
        null=True,
        help_text=_("Company"),
    )
    role = models.CharField(
        max_length=128,
        blank=True,
        null=True,
        help_text=_("Role"),
    )
    educational_institution = models.ForeignKey(
        "core.EducationalInstitution",
        on_delete=models.SET_NULL,
        related_name="students",
        blank=True,
        null=True,
        verbose_name=_("EducationalInstitution"),
    )
    city = models.CharField(
        max_length=128,
        blank=True,
        null=True,
        help_text=_("City"),
    )
    country = models.CharField(
        max_length=128,
        blank=True,
        null=True,
        help_text=_("Country"),
    )
    major = models.ForeignKey(
        "core.Major",
        on_delete=models.SET_NULL,
        related_name="students",
        blank=True,
        null=True,
        verbose_name=_("Major"),
    )
    term = models.ForeignKey(
        "core.Term",
        on_delete=models.SET_NULL,
        related_name="students",
        blank=True,
        null=True,
        verbose_name=_("Term"),
    )

    google_contact_id = models.CharField(
        max_length=32,
        blank=True,
        null=True,
        help_text=_("Google Contact ID"),
    )
    last_google_sync = models.DateTimeField(
        blank=True,
        null=True,
        help_text=_("Last Google Sync"),
    )

    def __str__(self):
        if not self.first_name and not self.last_name:
            return self.username
        return self.first_name + " " + self.last_name


class Major(AuditBaseModel):
    """
    Major model. This model would be used to store major information.
    """

    mid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    name = models.CharField(
        max_length=128,
        help_text=_("Name"),
    )


class Term(AuditBaseModel):
    """ "
    Term model. This model would be used to store term information.
    """

    tid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    name = models.CharField(
        max_length=32,
        help_text=_("Name"),
    )
