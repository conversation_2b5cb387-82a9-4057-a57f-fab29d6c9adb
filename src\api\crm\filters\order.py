from django_filters import rest_framework as filters
from core.models import Order
from api.crm.utils import PhoneNumberUtils
from django.db.models import Q, Value, CharField
from django.db.models.functions import Concat


class CrmOrderFilter(filters.FilterSet):

    search = filters.CharFilter(
        method="filter_search",
        help_text="Filter by order owner's first name, last name, email, or phone number.",
    )

    stages = filters.CharFilter(
        method="filter_stages",
        help_text="Comma-separated list of order stages (prospect,interested,to_pay,sold,lost)",
    )

    is_international = filters.BooleanFilter(
        field_name="is_international",
        help_text="Filter by international status (true/false).",
    )

    has_full_scholarship = filters.BooleanFilter(
        field_name="has_full_scholarship",
        help_text="Filter by full scholarship status (true/false).",
    )

    offerings = filters.CharFilter(
        method="filter_by_offerings",
        help_text="Filter orders that contain any of the specified offering IDs. "
        "Accept comma-separated UUIDs.",
    )

    start_date = filters.DateFilter(
        method="filter_by_date_range",
        help_text="Start date for filtering (YYYY-MM-DD format). Must be used with end_date and filter_date_by.",
    )

    end_date = filters.DateFilter(
        method="filter_by_date_range",
        help_text="End date for filtering (YYYY-MM-DD format). Must be used with start_date and filter_date_by.",
    )

    filter_date_by = filters.CharFilter(
        method="filter_by_date_range",
        help_text="Comma-separated list of date fields to filter by. "
        "Available options: created_at,interested_at,prospect_at,to_pay_at,sold_at,lost_at",
    )

    def filter_stages(self, queryset, name, value):
        """
        Filter by multiple products/offerings (comma-separated)
        """
        if not value:
            return queryset

        stages = [stage.strip() for stage in value.split(",") if stage.strip()]
        if stages:
            return queryset.filter(stage__in=stages)
        return queryset

    def filter_by_offerings(self, queryset, name, value):
        """
        Filter orders that contain any of the specified offerings.
        The value should be a comma-separated list of offering UUIDs.
        """
        if not value:
            return queryset

        # Split the comma-separated values and clean them
        offering_ids = [oid.strip() for oid in value.split(",") if oid.strip()]

        if not offering_ids:
            return queryset

        # Filter orders that have items with any of the specified offerings
        return queryset.filter(
            items__offering__oid__in=offering_ids, items__deleted=False
        ).distinct()

    def filter_search(self, queryset, name, value):
        """
        Filter by order owner's first name, last name, email, or phone number.
        """
        if not value:
            return queryset

        # Check if the search value looks like a phone number
        if PhoneNumberUtils.is_phone_number(value):
            # Use only the cleaned phone number for search
            value = PhoneNumberUtils.normalize_phone_number(value)["cleaned"]
            # filter only for phone number
            return queryset.filter(owner__phone_number__icontains=value)

        queryset_with_full_name = queryset.annotate(
            full_name=Concat(
                "owner__first_name",
                Value(" "),
                "owner__last_name",
                output_field=CharField(),
            )
        )

        return queryset_with_full_name.filter(
            Q(owner__first_name__icontains=value)
            | Q(owner__last_name__icontains=value)
            | Q(owner__email__icontains=value)
            | Q(owner__phone_number__icontains=value)
            | Q(full_name__icontains=value)  # Search in concatenated full name
        )

    def filter_by_date_range(self, queryset, name, value):
        """
        Filter orders by date range for specific date fields.
        Requires start_date, end_date, and filter_date_by parameters.

        Example: ?start_date=2025-07-01&end_date=2025-07-31&filter_date_by=created_at,interested_at
        """
        # Get all filter parameters from the request
        params = self.request.GET
        start_date = params.get("start_date")
        end_date = params.get("end_date")
        filter_date_by = params.get("filter_date_by")

        # All three parameters are required for date filtering
        if not all([start_date, end_date, filter_date_by]):
            missing = []
            if not start_date:
                missing.append("start_date")
            if not end_date:
                missing.append("end_date")
            if not filter_date_by:
                missing.append("filter_date_by")
            print(f"Date filter skipped - missing parameters: {missing}")
            return queryset

        # Parse the date fields to filter by
        date_fields = [
            field.strip() for field in filter_date_by.split(",") if field.strip()
        ]

        # Valid Django model field names (no mapping needed now)
        valid_field_names = {
            "created_at",
            "interested_at",
            "prospect_at",
            "to_pay_at",
            "sold_at",
            "lost_at",
        }

        # Validate field names
        valid_fields = []
        invalid_fields = []
        for field in date_fields:
            if field in valid_field_names:
                valid_fields.append(field)
            else:
                invalid_fields.append(field)

        if not valid_fields:
            return queryset

        # Build the filter condition using OR logic
        date_filter = Q()
        for field in valid_fields:
            # Create date range filter for each field
            field_filter = Q(
                **{
                    f"{field}__date__gte": start_date,
                    f"{field}__date__lte": end_date,
                }
            )
            # Combine with OR logic (orders matching ANY of the date fields)
            date_filter |= field_filter

        filtered_queryset = queryset.filter(date_filter)

        return filtered_queryset

    class Meta:
        model = Order
        fields = [
            "owner",
            "sales_agent",
            "stages",
            "is_international",
            "has_full_scholarship",
            "offerings",
            "start_date",
            "end_date",
            "filter_date_by",
        ]
