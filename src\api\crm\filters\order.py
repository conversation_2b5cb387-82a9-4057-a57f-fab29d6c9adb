from django_filters import rest_framework as filters
from core.models import Order


class CrmOrderFilter(filters.FilterSet):
    stage = filters.MultipleChoiceFilter(
        choices=Order.STAGE_CHOICES,
        field_name="stage",
        help_text="Filter by order stage. Can accept multiple values.",
    )

    is_international = filters.BooleanFilter(
        field_name="is_international",
        help_text="Filter by international status (true/false).",
    )

    has_full_scholarship = filters.BooleanFilter(
        field_name="has_full_scholarship",
        help_text="Filter by full scholarship status (true/false).",
    )

    offerings = filters.CharFilter(
        method="filter_by_offerings",
        help_text="Filter orders that contain any of the specified offering IDs. "
        "Accept comma-separated UUIDs.",
    )

    def filter_by_offerings(self, queryset, name, value):
        """
        Filter orders that contain any of the specified offerings.
        The value should be a comma-separated list of offering UUIDs.
        """
        if not value:
            return queryset

        # Split the comma-separated values and clean them
        offering_ids = [oid.strip() for oid in value.split(",") if oid.strip()]

        if not offering_ids:
            return queryset

        # Filter orders that have items with any of the specified offerings
        return queryset.filter(items__offering__oid__in=offering_ids).distinct()

    class Meta:
        model = Order
        fields = [
            "owner",
            "sales_agent",
            "stage",
            "is_international",
            "has_full_scholarship",
            "offerings",
        ]
