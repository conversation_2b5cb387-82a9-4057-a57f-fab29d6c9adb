from rest_framework import permissions


class IsSuperUser(permissions.BasePermission):
    def has_permission(self, request, view):
        # Check if the user is authenticated
        if not request.user.is_authenticated:
            return False

        # Check if the user is a superadmin
        return request.user.is_superuser


class IsStaffUser(permissions.BasePermission):
    def has_permission(self, request, view):
        return bool(request.user and request.user.is_staff)


class IsInDevelopmentTeam(permissions.BasePermission):
    """
    Custom permission to only allow members of the development team.
    """

    def has_permission(self, request, view):
        # Check if the user is authenticated
        if not request.user.is_authenticated:
            return False

        # Check if the user is in the development team
        return request.user.groups.filter(name="development").exists()


class IsOwnerOrReadOnly(permissions.BasePermission):
    """
    Custom permission to only allow owners of an object to edit it.
    Assumes the model instance has a `created_by` field.
    """

    def has_object_permission(self, request, view, obj):
        # Read permissions are allowed to any request,
        # so we'll always allow GET, HEAD or OPTIONS requests.
        if request.method in permissions.SAFE_METHODS:
            return True

        # Write permissions are only allowed to the owner of the object.
        return hasattr(obj, 'created_by') and obj.created_by == request.user


class CanModifyActivity(permissions.BasePermission):
    """
    Permission to allow modification/deletion of activities only by the creator
    """

    def has_object_permission(self, request, view, obj):
        # Allow read access to all authenticated users
        if request.method in permissions.SAFE_METHODS:
            return True

        # For write operations, check if user is the creator
        if hasattr(obj, 'created_by') and obj.created_by:
            return obj.created_by == request.user

        # If no created_by field or it's None, deny access
        return False


class CanDeleteOrder(permissions.BasePermission):
    """
    Permission to control order deletion
    Requires specific user permission: 'crm.delete_order'
    """

    def has_permission(self, request, view):
        if request.method == 'DELETE':
            return request.user.has_perm('crm.delete_order')
        return True

    def has_object_permission(self, request, view, obj):
        if request.method == 'DELETE':
            return request.user.has_perm('crm.delete_order')
        return True


class CanDeleteOrderItem(permissions.BasePermission):
    """
    Permission to control order item deletion
    Requires specific user permission: 'crm.delete_orderitem'
    """

    def has_permission(self, request, view):
        if request.method == 'DELETE':
            return request.user.has_perm('crm.delete_orderitem')
        return True

    def has_object_permission(self, request, view, obj):
        if request.method == 'DELETE':
            return request.user.has_perm('crm.delete_orderitem')
        return True


class CanModifySoldOrder(permissions.BasePermission):
    """
    Permission to control modification of sold orders
    Requires specific user permission: 'crm.modify_sold_order'
    """

    def has_object_permission(self, request, view, obj):
        # Allow read access
        if request.method in permissions.SAFE_METHODS:
            return True

        # For sold orders, require special permission
        if hasattr(obj, 'stage') and obj.stage == 'sold':
            return request.user.has_perm('crm.modify_sold_order')

        return True


class CanManageUsers(permissions.BasePermission):
    """
    Permission to control CRUD operations on non-staff users
    Requires specific user permission: 'core.manage_users'
    """

    def has_permission(self, request, view):
        # For any CRUD operation on users, check permission
        if request.method in ['POST', 'PUT', 'PATCH', 'DELETE']:
            return request.user.has_perm('core.manage_users')
        return True

    def has_object_permission(self, request, view, obj):
        # Allow read access
        if request.method in permissions.SAFE_METHODS:
            return True

        # For non-staff users, require special permission
        if hasattr(obj, 'is_staff') and not obj.is_staff:
            return request.user.has_perm('core.manage_users')

        return True
