"""
Filters for Template (WhatsApp message) model
"""

import django_filters
from django.db.models import Q
from core.models import Template


class CrmTemplateFilter(django_filters.FilterSet):
    """Filter for Template with user search capabilities"""

    # Template name search
    name = django_filters.CharFilter(
        label="Template Name",
        lookup_expr="icontains",
    )

    class Meta:
        model = Template
        fields = ["name"]
