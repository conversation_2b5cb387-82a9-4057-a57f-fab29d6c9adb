import uuid
from .base import AuditBaseModel
from django.db import models


class Activity(AuditBaseModel):
    COMPLETED_STATUS = "completed"
    PENDING_STATUS = "pending"
    IN_PROGRESS_STATUS = "in_progress"

    STATUS_CHOICES = [
        (COMPLETED_STATUS, "Completed"),
        (PENDING_STATUS, "Pending"),
        (IN_PROGRESS_STATUS, "In Progress"),
    ]

    aid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )

    title = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name="Title",
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name="Description",
        help_text="Description of the activity. It will be markdown formatted.",
    )

    deadline = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name="Deadline",
        help_text="Deadline for the activity",
    )

    responsible = models.ForeignKey(
        "core.User",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="activities_responsible",
        verbose_name="Responsible",
    )

    status = models.CharField(
        max_length=50,
        choices=STATUS_CHOICES,
        default=PENDING_STATUS,
        verbose_name="Status",
    )
    order = models.ForeignKey(
        "core.Order",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="activities",
        verbose_name="Order",
    )

    class Meta:
        verbose_name = "Activity"
        verbose_name_plural = "Activities"
        permissions = [
            ("modify_own_activity", "Can modify own activities"),
        ]

    def __str__(self):
        return self.title or f"Activity {self.aid}"
