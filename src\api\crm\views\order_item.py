from core.models import OrderItem
from rest_framework import viewsets, status, filters
from rest_framework.response import Response
from rest_framework import status
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import action
from api.crm.serializers.order_item import (
    CrmOrderItemBaseSerializer,
    CrmOrderItemListSerializer,
    CrmOrderItemRetrieveSerializer,
    CrmOrderItemCreateSerializer,
    CrmOrderItemUpdateSerializer,
)
from api.crm.serializers.order_item import CrmSendClassroomInvitationSerializer
from api.mixins import AuditMixin, SwaggerTagMixin
from django_filters.rest_framework import DjangoFilterBackend
from api.paginations import StandardResultsPagination
from api.permissions import IsStaffUser
from rest_framework import serializers


class CrmOrderItemViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    model_class = OrderItem
    queryset = OrderItem.objects.filter(deleted=False).order_by("-created_at")
    serializer_class = CrmOrderItemBaseSerializer

    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsStaffUser]
    pagination_class = StandardResultsPagination

    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    )
    ordering_fields = ["created_at", "quantity", "effective_total_price"]
    filterset_fields = [
        "order",
        "offering",
        "order__stage",
        "order__is_international",
    ]
    search_fields = [
        "order__owner__first_name",
        "order__owner__last_name",
        "order__owner__email",
        "order__owner__phone_number",
        "offering__name",
    ]

    swagger_tags = ["Order Items"]

    def get_serializer(self, *args, **kwargs):
        """Return appropriate serializer based on action"""
        if self.action == "create":
            return CrmOrderItemCreateSerializer(*args, **kwargs)
        elif self.action in ["update", "partial_update"]:
            return CrmOrderItemUpdateSerializer(*args, **kwargs)
        elif self.action == "retrieve":
            return CrmOrderItemRetrieveSerializer(*args, **kwargs)
        elif self.action == "list":
            return CrmOrderItemListSerializer(*args, **kwargs)
        elif self.action == "send-classroom-invitation":
            return CrmSendClassroomInvitationSerializer(*args, **kwargs)
        return CrmOrderItemBaseSerializer(*args, **kwargs)

    def create(self, request, *args, **kwargs):
        """Create a new order item or restore a deleted one"""
        try:
            data = request.data.copy()
            serializer = self.get_serializer(data=data)

            # Check if we need to restore a deleted item
            order_id = data.get("order")
            offering_id = data.get("offering")

            if order_id and offering_id:
                # Look for a deleted item with the same combination
                deleted_item = OrderItem.objects.filter(
                    order__oid=order_id, offering__oid=offering_id, deleted=True
                ).first()

                if deleted_item:
                    # Restore the deleted item instead of creating new
                    deleted_item.deleted = False
                    deleted_item.deleted_at = None
                    deleted_item.deleted_by = None
                    deleted_item.quantity = data.get("quantity", 1)
                    deleted_item.custom_amount = data.get("custom_amount", None)
                    deleted_item.save()

                    # Return detailed view of restored item
                    retrieve_serializer = CrmOrderItemRetrieveSerializer(deleted_item)
                    return Response(
                        retrieve_serializer.data, status=status.HTTP_201_CREATED
                    )

                # No deleted item found, proceed with normal creation
                serializer.is_valid(raise_exception=True)
                self.perform_create(serializer)

                # Return detailed view of created item
                retrieve_serializer = CrmOrderItemRetrieveSerializer(
                    serializer.instance
                )
                return Response(
                    retrieve_serializer.data, status=status.HTTP_201_CREATED
                )
        except serializers.ValidationError as e:
            return Response(
                "No se pudo agregar el producto a la orden. Verifica que los datos sean correctos y que no exista este producto en la orden.",
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as error:
            return Response(
                "Error inesperado al crear un producto",
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def update(self, request, *args, **kwargs):
        """Update an existing order item"""
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        # Return detailed view of updated item
        retrieve_serializer = CrmOrderItemRetrieveSerializer(serializer.instance)
        return Response(retrieve_serializer.data)

    def destroy(self, request, *args, **kwargs):
        """Soft delete an order item if it is not sold"""
        instance = self.get_object()

        # Check if order is in a state that allows item deletion
        if instance.order.stage == instance.order.SOLD_STAGE:
            return Response(
                {"error": "No se pueden eliminar elementos de órdenes vendidas"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Perform soft delete
        self.perform_destroy(instance)
        return Response(status=status.HTTP_204_NO_CONTENT)

    def get_queryset(self):
        """Override to add additional filtering or prefetching"""
        queryset = super().get_queryset()

        # Prefetch related objects for better performance
        queryset = queryset.select_related("order", "offering")

        # Add any additional filtering based on query parameters
        order_id = self.request.query_params.get("order_id", None)
        if order_id:
            queryset = queryset.filter(order__oid=order_id)

        return queryset

    @action(
        detail=True,
        methods=["post"],
        url_path="send-classroom-invitation",
        permission_classes=[IsAuthenticated & IsStaffUser],
    )
    def send_classroom_invitation(self, request, pk=None):
        """
        Envía una invitación de Google Classroom para este OrderItem específico.
        """
        try:
            order_item = self.get_object()

            # Usar el serializer para validar y enviar la invitación
            serializer = CrmSendClassroomInvitationSerializer(
                instance=order_item, data={}
            )
            serializer.is_valid(raise_exception=True)

            # Enviar la invitación
            result = serializer.send_invitation()

            if result["success"]:
                return Response(result, status=status.HTTP_200_OK)
            else:
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

        except serializers.ValidationError as e:
            return Response(
                {"error": "Validación fallida", "details": e.detail},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            return Response(
                {"error": f"Error inesperado: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
