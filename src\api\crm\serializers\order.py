from rest_framework import serializers
from core.models import Order, OrderItem, User
import re
from api.crm.tasks.classroom import process_classroom_invitation_for_order


class CrmUserSerializer(serializers.ModelSerializer):
    full_name = serializers.SerializerMethodField(
        source="get_full_name",
        read_only=True,
    )

    def get_full_name(self, obj):
        return obj.get_full_name() if obj.get_full_name() != "" else obj.username

    class Meta:
        model = User
        fields = (
            "uid",
            "first_name",
            "last_name",
            "full_name",
            "phone_number",
            "email",
        )


class CrmOrderItemSerializer(serializers.ModelSerializer):
    key = serializers.UUIDField(source="id")
    oiid = serializers.UUIDField(source="id")
    offering = serializers.SerializerMethodField()

    def get_offering(self, obj):
        from api.crm.serializers.offering import CrmOfferingSerializer

        return CrmOfferingSerializer(obj.offering).data

    class Meta:
        model = OrderItem
        fields = (
            "oiid",
            "key",
            "offering",
            "quantity",
            "base_price",
            "foreign_base_price",
            "discount",
            "unit_price",
            "foreign_unit_price",
            "effective_unit_price",
            "total_price",
            "foreign_total_price",
            "effective_total_price",
            "has_custom_amount",
            "custom_amount",
            "custom_amount_price",
            "ext_invitation_status",
            "created_at",
            "updated_at",
        )


class CrmOrderSerializer(serializers.ModelSerializer):
    owner = CrmUserSerializer(read_only=True)
    sales_agent = CrmUserSerializer(read_only=True)
    order_items = serializers.SerializerMethodField()

    def get_order_items(self, obj):
        return CrmOrderItemSerializer(obj.items, many=True).data

    class Meta:
        model = Order
        fields = (
            "oid",
            "owner",
            "stage",
            "order_items",
            "prospect_at",
            "interested_at",
            "to_pay_at",
            "sold_at",
            "lost_at",
            "total",
            "is_international",
            "has_full_scholarship",
            "sales_agent",
            "created_at",
            "updated_at",
        )


class CrmRetrieveOrderSerializer(serializers.ModelSerializer):
    owner = CrmUserSerializer(read_only=True)
    sales_agent = CrmUserSerializer(read_only=True)
    stages_dates = serializers.SerializerMethodField()

    order_items = serializers.SerializerMethodField()
    benefits = serializers.SerializerMethodField()
    lead_sources = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = (
            "oid",
            "owner",
            "sales_agent",
            "stage",
            "stages_dates",
            "order_items",
            "benefits",
            "lead_sources",
            "is_international",
            "has_full_scholarship",
            "created_at",
            "updated_at",
            "total",
        )

    def get_order_items(self, obj):
        items = obj.items.filter(deleted=False)
        return CrmOrderItemSerializer(items, many=True).data

    def get_benefits(self, obj):
        from api.crm.serializers.benefit import CrmBenefitSerializer

        return CrmBenefitSerializer(obj.benefits.all(), many=True).data

    def get_lead_sources(self, obj):
        # Import here to avoid circular imports
        from api.crm.serializers.lead_source import CrmLeadSourceSerializer

        return CrmLeadSourceSerializer(obj.lead_sources.all(), many=True).data

    def get_stages_dates(self, obj):
        stages = [
            {"stage": Order.PROSPECT_STAGE, "date": obj.prospect_at},
            {"stage": Order.INTERESTED_STAGE, "date": obj.interested_at},
            {"stage": Order.TO_PAY_STAGE, "date": obj.to_pay_at},
            {"stage": Order.SOLD_STAGE, "date": obj.sold_at},
            {"stage": Order.LOST_STAGE, "date": obj.lost_at},
        ]

        return stages


class CrmCreateOrderSerializer(serializers.ModelSerializer):
    owner = serializers.UUIDField(write_only=True)
    products = serializers.ListField(child=serializers.UUIDField(), write_only=True)
    benefits = serializers.ListField(
        child=serializers.UUIDField(), write_only=True, required=False
    )
    lead_sources = serializers.ListField(
        child=serializers.UUIDField(), write_only=True, required=False
    )

    class Meta:
        model = Order
        fields = [
            "oid",
            "owner",
            "products",
            "benefits",
            "lead_sources",
            "stage",
            "sales_agent",
        ]

        extra_kwargs = {
            "oid": {"read_only": True},
        }

    def validate(self, attrs):
        """
        Validación completa de la orden
        - Evitar offerings duplicados en diferentes órdenes para el mismo contacto
        """
        owner_id = attrs.get("owner")
        product_ids = attrs.get("products", [])

        errors = {}

        if product_ids:
            duplicate_errors = self._validate_duplicate_products(owner_id, product_ids)
            if duplicate_errors:
                errors.update(duplicate_errors)

        if errors:
            raise serializers.ValidationError(errors)

        return attrs

    def _validate_duplicate_products(self, owner_id, product_ids):
        """
        Valida que los productos no estén duplicados para el owner
        Retorna errores en formato array
        """
        from core.models import OrderItem, Offering

        # Buscar productos que ya tiene el owner en otras órdenes
        existing_items = (
            OrderItem.objects.filter(
                order__owner_id=owner_id,
                offering_id__in=product_ids,
                deleted=False,
                order__deleted=False,
            )
            .select_related("offering", "order")
            .values("offering_id", "offering__name", "order__oid")
        )

        if not existing_items:
            return {}

        # Crear lista de productos con saltos de línea
        product_names = [item["offering__name"] for item in existing_items]
        products_list = "\n- ".join(set(product_names))

        general_message = (
            f"Los siguientes productos ya están registrados para este contacto:\n"
            f"- {products_list}\n\n"
            f"Un contacto no puede tener el mismo producto en múltiples órdenes."
        )

        return {
            "duplicated_products": [general_message],
        }

    def _handle_sold_stage_creation(self, order):
        """
        Ejecuta las acciones necesarias cuando una orden se crea directamente como SOLD
        """
        try:
            # 1. Persistir precios actuales
            for item in order.items.filter(deleted=False):
                item.persist_current_price()
            # 2. Enviar invitaciones de Google Classroom
            process_classroom_invitation_for_order.delay(str(order.oid))
        except Exception as e:
            # print(f"Error en acciones post-venta para orden {order.oid}: {e}")
            pass

    def create(self, validated_data):
        # Extraer datos que no son directamente para Order
        owner_id = validated_data.pop("owner")
        product_ids = validated_data.pop("products")
        benefit_ids = validated_data.pop("benefits", [])
        lead_source_ids = validated_data.pop("lead_sources", [])

        # Obtener las fechas basadas en el stage y completar estados anteriores
        stage = validated_data.get("stage", Order.PROSPECT_STAGE)
        self._set_funnel_stage_dates(validated_data, stage)

        if "sales_agent" not in validated_data or not validated_data["sales_agent"]:
            request = self.context.get("request")
            if request and hasattr(request, "user"):
                validated_data["sales_agent"] = request.user

        # Crear la orden
        order = Order.objects.create(owner_id=owner_id, **validated_data)

        # Crear los OrderItems
        for product_id in product_ids:
            OrderItem.objects.create(order=order, offering_id=product_id)

        # Añadir los benefits directamente a la relación ManyToMany
        if benefit_ids:
            order.benefits.add(*benefit_ids)

        # Añadir los lead_sources directamente a la relación ManyToMany
        if lead_source_ids:
            order.lead_sources.add(*lead_source_ids)

        # Si se crea directamente como SOLD, ejecutar acciones post-venta
        if order.stage == Order.SOLD_STAGE:
            self._handle_sold_stage_creation(order)

        return order

    def _set_funnel_stage_dates(self, validated_data, stage):
        """
        Completa automáticamente las fechas de los estados anteriores del embudo
        cuando se crea una orden en un estado específico.

        Orden del embudo:
        1. Prospecto (prospect)
        2. Interesado (interested)
        3. Por pagar (to_pay)
        4. Vendido (sold)
        5. Perdido (lost)
        """
        from django.utils import timezone

        # Definir el orden del embudo
        funnel_stages = [
            Order.PROSPECT_STAGE,
            Order.INTERESTED_STAGE,
            Order.TO_PAY_STAGE,
            Order.SOLD_STAGE,
            Order.LOST_STAGE,
        ]

        # Obtener la posición del stage actual en el embudo
        try:
            current_stage_index = funnel_stages.index(stage)
        except ValueError:
            # Si el stage no está en el embudo, solo establecer la fecha del stage actual
            stage_date_field = f"{stage}_at"
            if hasattr(Order, stage_date_field):
                validated_data[stage_date_field] = timezone.now()
            return

        # Establecer la fecha actual para el stage y todos los anteriores
        current_time = timezone.now()

        # Completar fechas de estados anteriores y el actual
        for i in range(current_stage_index + 1):
            stage_name = funnel_stages[i]
            stage_date_field = f"{stage_name}_at"

            # Solo establecer la fecha si no está ya definida en validated_data
            if stage_date_field not in validated_data and hasattr(
                Order, stage_date_field
            ):
                validated_data[stage_date_field] = current_time


class CrmUpdateOrderSerializer(serializers.ModelSerializer):
    """
    Serializador para actualizar una orden existente.
    Permite actualizar los productos, beneficios y fuentes de leads.
    Implementa validación campo por campo para mayor claridad y mantenibilidad.
    """

    benefits = serializers.ListField(
        child=serializers.UUIDField(), write_only=True, required=False
    )
    lead_sources = serializers.ListField(
        child=serializers.UUIDField(), write_only=True, required=False
    )
    sales_agent = serializers.UUIDField(required=False, allow_null=True)

    class Meta:
        model = Order
        fields = [
            "stage",
            "benefits",
            "lead_sources",
            "sales_agent",
            "prospect_at",
            "interested_at",
            "to_pay_at",
            "sold_at",
            "is_international",
            "has_full_scholarship",
            "owner",
        ]

    def validate_stage(self, value):
        """Validación específica para el campo stage."""
        valid_stages = dict(Order.STAGE_CHOICES).keys()
        if value not in valid_stages:
            raise serializers.ValidationError(
                f"Stage debe ser uno de: {', '.join(valid_stages)}"
            )

        # Validaciones adicionales cuando se cambia a "sold"
        if value == Order.SOLD_STAGE:
            self._validate_sold_stage_requirements()

        return value

    def _validate_sold_stage_requirements(self):
        """
        Valida los requisitos necesarios para cambiar una orden al stage 'sold'.
        Verifica que el owner tenga email de Gmail válido, nombre y apellido.
        """
        instance = getattr(self, "instance", None)
        if not instance:
            return

        owner = instance.owner
        errors = {}

        # Validar que tenga nombre y apellido
        if not owner.first_name or not owner.first_name.strip():
            errors["owner_first_name"] = "El propietario de la orden debe tener nombre."

        if not owner.last_name or not owner.last_name.strip():
            errors["owner_last_name"] = (
                "El propietario de la orden debe tener apellido."
            )

        # Validar que tenga email de Gmail válido
        if not owner.email:
            errors["owner_email"] = (
                "El propietario de la orden debe tener un correo electrónico."
            )
        else:
            gmail_pattern = r"^[a-zA-Z0-9._%+-]+@gmail\.com$"
            if not re.match(gmail_pattern, owner.email.lower()):
                errors["owner_email"] = (
                    "El propietario de la orden debe tener un correo de Gmail válido (@gmail.com)."
                )

        if errors:
            raise serializers.ValidationError(errors)

    def validate_sales_agent(self, value):
        """Validación específica para el campo sales_agent."""
        if value:
            from core.models import User

            try:
                return User.objects.get(uid=value)
            except User.DoesNotExist:
                raise serializers.ValidationError(
                    "El agente de ventas especificado no existe"
                )
        return value

    def validate_benefits(self, value):
        """Validación específica para beneficios."""
        if value:
            from core.models import Benefit

            for benefit_id in value:
                try:
                    Benefit.objects.get(pk=benefit_id)
                except Benefit.DoesNotExist:
                    raise serializers.ValidationError(
                        f"Beneficio con ID {benefit_id} no existe"
                    )
        return value

    def validate_lead_sources(self, value):
        """Validación específica para fuentes de leads."""
        if value:
            from core.models import LeadSource

            for source_id in value:
                try:
                    LeadSource.objects.get(pk=source_id)
                except LeadSource.DoesNotExist:
                    raise serializers.ValidationError(
                        f"Fuente de lead con ID {source_id} no existe"
                    )
        return value

    def _update_stage_date(self, instance, stage):
        """
        Actualiza la fecha correspondiente al cambio de stage y completa
        automáticamente las fechas de los estados anteriores del embudo
        que no tengan timestamp, respetando las fechas existentes.
        """
        if not stage or stage == instance.stage:
            return {}

        from django.utils import timezone

        # Definir el orden del embudo
        funnel_stages = [
            Order.PROSPECT_STAGE,
            Order.INTERESTED_STAGE,
            Order.TO_PAY_STAGE,
            Order.SOLD_STAGE,
            Order.LOST_STAGE,
        ]

        # Obtener la posición del stage actual en el embudo
        try:
            current_stage_index = funnel_stages.index(stage)
        except ValueError:
            # Si el stage no está en el embudo, solo establecer la fecha del stage actual
            stage_date_field = f"{stage}_at"
            if hasattr(Order, stage_date_field):
                return {stage_date_field: timezone.now()}
            return {}

        # Preparar las fechas a actualizar
        updates = {}
        current_time = timezone.now()

        # Completar fechas de estados anteriores y el actual
        for i in range(current_stage_index + 1):
            stage_name = funnel_stages[i]
            stage_date_field = f"{stage_name}_at"

            # Solo establecer la fecha si no existe ya en la instancia
            if hasattr(Order, stage_date_field):
                current_value = getattr(instance, stage_date_field, None)
                if current_value is None:
                    updates[stage_date_field] = current_time

        return updates

    def _update_benefits(self, instance, benefit_ids):
        """Actualiza los beneficios de la orden."""
        if benefit_ids is not None:
            instance.benefits.clear()
            instance.benefits.add(*benefit_ids)

    def _update_lead_sources(self, instance, lead_source_ids):
        """Actualiza las fuentes de leads de la orden."""
        if lead_source_ids is not None:
            instance.lead_sources.clear()
            instance.lead_sources.add(*lead_source_ids)

    def _persist_prices_on_sold(self, instance, old_stage, new_stage):
        """Persiste los precios cuando la orden cambia a estado sold."""
        if old_stage != Order.SOLD_STAGE and new_stage == Order.SOLD_STAGE:
            for item in instance.items.filter(deleted=False):
                item.persist_current_price()

    def _send_classroom_invitations(self, instance, old_stage, new_stage):
        """
        Envía invitaciones de Google Classroom cuando la orden cambia a estado sold.
        """
        if old_stage != Order.SOLD_STAGE and new_stage == Order.SOLD_STAGE:
            try:
                # Enviar la tarea de forma asíncrona
                process_classroom_invitation_for_order.delay(str(instance.oid))
            except Exception as e:
                pass

    def update(self, instance, validated_data):
        # Extraer datos que no son directamente para Order
        benefit_ids = validated_data.pop("benefits", None)
        lead_source_ids = validated_data.pop("lead_sources", None)

        # Capturar el estado anterior para detectar cambios
        old_stage = instance.stage

        # Actualizar la fecha correspondiente al stage si cambia
        stage = validated_data.get("stage")
        validated_data.update(self._update_stage_date(instance, stage))

        # Actualizar la instancia con los datos validados
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Persistir precios si la orden cambia a estado sold
        new_stage = validated_data.get("stage", old_stage)
        self._persist_prices_on_sold(instance, old_stage, new_stage)

        # Enviar invitaciones de Google Classroom si la orden cambia a sold
        self._send_classroom_invitations(instance, old_stage, new_stage)

        # Actualizar relaciones
        self._update_benefits(instance, benefit_ids)
        self._update_lead_sources(instance, lead_source_ids)

        return instance


class CrmOrderItemCreateSerializer(serializers.ModelSerializer):
    offering = serializers.UUIDField(write_only=True)
    order = serializers.UUIDField(read_only=True)

    class Meta:
        model = OrderItem
        fields = ["offering", "order"]
