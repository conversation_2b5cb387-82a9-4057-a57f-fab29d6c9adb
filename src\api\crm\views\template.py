from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status
from core.models import Template
from api.crm.serializers.template import (
    CrmCreateTemplateSerializer,
    CrmTemplateSerializer,
    CrmUpdateTemplateSerializer,
    CrmSendTemplateSerializer,
    CrmFetchTemplateStatusSerializer,
)
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from api.mixins import AuditMixin, SwaggerTagMixin
from api.crm.filters.template import CrmTemplateFilter


class CrmTemplateViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    model_class = Template
    queryset = Template.objects.filter(deleted=False).order_by("created_at")
    serializer_class = CrmTemplateSerializer
    # authentication_classes = [TokenAuthentication]
    # permission_classes = [IsAuthenticated & IsAdminUser]
    filterset_class = CrmTemplateFilter
    swagger_tags = ["Templates"]

    def get_serializer_class(self):
        if self.action == "create":
            return CrmCreateTemplateSerializer
        if self.action in ["update", "partial_update"]:
            return CrmUpdateTemplateSerializer
        return super().get_serializer_class()

    def retrieve(self, request, *args, **kwargs):
        # Fetch and update status for the specific template before returning details
        instance = self.get_object()
        # CrmFetchTemplateStatusSerializer().fetch_meta_template_status(instance)

        return super().retrieve(request, *args, **kwargs)

    @action(detail=False, methods=["get"], url_path="approved")
    def list_approved(self, request):
        queryset = self.get_queryset().filter(status=Template.APPROVED)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=["get"])
    def refresh_meta_status(self, request, pk=None):
        # Manual refresh endpoint
        instance = self.get_object()
        try:
            new_status = CrmFetchTemplateStatusSerializer().fetch_meta_template_status(
                instance
            )
            return Response({"status": new_status}, status=status.HTTP_200_OK)
        except ValidationError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=["post"], url_path="send-to-meta")
    def send_to_meta(self, request, pk=None):
        """
        Acción personalizada para enviar una plantilla a Meta.
        """
        template = self.get_object()
        serializer = CrmSendTemplateSerializer(template)

        try:
            serializer.send_to_meta(template)
            return Response(
                {"detail": "Plantilla enviada a Meta con éxito."},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"detail": f"{str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    def destroy(self, request, *args, **kwargs):
        """
        Marca una plantilla como eliminada en lugar de eliminarla físicamente.
        """
        instance = self.get_object()
        instance.deleted = True
        instance.save()

        return Response(
            {"detail": "La plantilla se marcó como eliminada."},
            status=status.HTTP_204_NO_CONTENT,
        )
