from rest_framework import serializers
from core.models import (
    EventSchedule,
    EventScheduleEnrollment,
    Event,
    File,
    Instructor,
    Partnership,
    User,
)
from .event import CrmEventInstructorSerializer
from api.shared.serializers.file import FileSerializer
from api.crm.services.file import upload_file_to_minio


class CrmEventSchedulePartnershipsSerializer(serializers.ModelSerializer):
    """Serializer for partnerships related to event schedules"""

    key = serializers.CharField(source="pid", read_only=True)
    institution = serializers.CharField(source="institution.name", read_only=True)

    class Meta:
        model = Partnership
        fields = [
            "key",
            "pid",
            "name",
            "institution",
            "created_at",
            "updated_at",
        ]


class CrmEventScheduleEventSerializer(serializers.ModelSerializer):
    """Serializer for event related to event schedules"""

    key = serializers.CharField(source="eid", read_only=True)

    class Meta:
        model = Event
        fields = [
            "key",
            "eid",
            "name",
            "created_at",
            "updated_at",
        ]


# Base: para listados
class CrmEventScheduleBaseSerializer(serializers.ModelSerializer):
    """Base serializer with common fields for all event schedule operations"""

    key = serializers.Char<PERSON>ield(source="esid", read_only=True)
    price = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        required=False,
        allow_null=True,
    )

    class Meta:
        model = EventSchedule
        fields = [
            "key",
            "esid",
            "event",
            "name",
            "description",
            "start_date",
            "end_date",
            "stage",
            "modality",
            "location",
            "instructor",
            "is_general",
            "price",
            "thumbnail",
            "cover_image",
            "created_at",
            "updated_at",
        ]

        extra_kwargs = {
            "esid": {"read_only": True},
            "created_at": {"read_only": True},
            "updated_at": {"read_only": True},
        }


class CrmEventScheduleListItemSerializer(CrmEventScheduleBaseSerializer):
    """Serializer for listing event schedules"""

    instructor = CrmEventInstructorSerializer(read_only=True)
    event = CrmEventScheduleEventSerializer(read_only=True)
    partnerships = CrmEventSchedulePartnershipsSerializer(many=True, read_only=True)

    class Meta(CrmEventScheduleBaseSerializer.Meta):
        fields = CrmEventScheduleBaseSerializer.Meta.fields + [
            "partnerships",
        ]


# Retrieve: para detalle
class CrmEventScheduleRetrieveSerializer(CrmEventScheduleBaseSerializer):
    """Serializer for retrieving event schedule details with nested relationships"""

    event = serializers.StringRelatedField(read_only=True)
    instructor = CrmEventInstructorSerializer(read_only=True)
    thumbnail = FileSerializer(read_only=True)
    cover_image = FileSerializer(read_only=True)

    partnerships = serializers.SerializerMethodField()
    enrollment_url = serializers.SerializerMethodField()
    partnership_enrollment_urls = serializers.SerializerMethodField()

    def get_partnerships(self, obj):
        """Get the partnerships related to the event schedule"""
        partnerships = obj.partnerships.all()
        return CrmEventSchedulePartnershipsSerializer(partnerships, many=True).data

    def get_enrollment_url(self, obj):
        """Get the enrollment URL for the event schedule"""
        from django.conf import settings

        return f"{settings.APP_HOST}/e/{obj.short_esid}/r"

    def get_partnership_enrollment_urls(self, obj):
        """Get enrollment URLs for each partnership"""
        from django.conf import settings

        partnerships = obj.partnerships.all()
        if not partnerships.exists():
            return []

        partnership_urls = []
        for partnership in partnerships:
            institution_name = (
                partnership.institution.name if partnership.institution else None
            )
            enrollment_url = (
                f"{settings.APP_HOST}/e/{obj.short_esid}/r"
                f"?ps={partnership.short_pid}"
            )

            partnership_urls.append(
                {
                    "pid": partnership.pid,
                    "name": partnership.name,
                    "institution": institution_name,
                    "enrollment_url": enrollment_url,
                }
            )

        return partnership_urls

    class Meta(CrmEventScheduleBaseSerializer.Meta):
        fields = CrmEventScheduleBaseSerializer.Meta.fields + [
            "event",
            "instructor",
            "thumbnail",
            "cover_image",
            "partnerships",
            "enrollment_url",
            "partnership_enrollment_urls",
            "ext_event_link",
        ]


# Create: para creación
class CrmEventScheduleCreateSerializer(CrmEventScheduleBaseSerializer):
    """Serializer for creating new event schedules"""

    event = serializers.UUIDField(required=True)
    name = serializers.CharField(
        max_length=255,
        required=False,
        allow_blank=True,
    )
    description = serializers.CharField(
        required=False,
        allow_blank=True,
    )
    start_date = serializers.DateTimeField(
        required=True,
    )
    end_date = serializers.DateTimeField(
        required=True,
    )
    stage = serializers.CharField(
        max_length=24,
        required=False,
    )
    modality = serializers.CharField(max_length=24, required=False)
    location = serializers.CharField(
        max_length=255,
        required=False,
        allow_null=True,
    )
    instructor = serializers.UUIDField(
        required=False,
        allow_null=True,
    )
    price = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    thumbnail = serializers.UUIDField(
        required=False,
        allow_null=True,
    )
    cover_image = serializers.UUIDField(
        required=False,
        allow_null=True,
    )

    partnerships = serializers.PrimaryKeyRelatedField(
        queryset=Partnership.objects.filter(deleted=False),
        many=True,
        required=False,
        allow_empty=True,
    )

    class Meta(CrmEventScheduleBaseSerializer.Meta):
        fields = CrmEventScheduleBaseSerializer.Meta.fields + [
            "partnerships",
        ]

    def validate_event(self, value):
        try:
            event = Event.objects.get(eid=value, deleted=False)
            return event
        except Event.DoesNotExist:
            raise serializers.ValidationError(f"Event with ID {value} does not exist")

    def validate_stage(self, value):
        valid_stages = [choice[0] for choice in Event.STAGE_CHOICES]
        if value not in valid_stages:
            raise serializers.ValidationError(
                f"Invalid stage. Must be one of: {', '.join(valid_stages)}"
            )
        return value

    def validate_modality(self, value):
        valid_modalities = [choice[0] for choice in Event.MODALITY_CHOICES]
        if value not in valid_modalities:
            raise serializers.ValidationError(
                f"Invalid modality. Must be one of: {', '.join(valid_modalities)}"
            )
        return value

    def validate_price(self, value):
        if value < 0:
            raise serializers.ValidationError("Price cannot be negative")
        return value

    def validate_instructor(self, value):
        if value:
            try:
                instructor = Instructor.objects.get(iid=value, deleted=False)
                return instructor
            except Instructor.DoesNotExist:
                raise serializers.ValidationError(
                    f"Instructor with ID {value} does not exist"
                )
        return None

    def validate_thumbnail(self, value):
        if value:
            try:
                thumbnail = File.objects.get(fid=value, deleted=False)
                if thumbnail:
                    thumbnail.is_used = True
                    thumbnail.save()
                return thumbnail
            except File.DoesNotExist:
                raise serializers.ValidationError(
                    f"Thumbnail with ID {value} does not exist"
                )
        return None

    def validate_cover_image(self, value):
        if value:
            try:
                cover_image = File.objects.get(fid=value, deleted=False)
                if cover_image:
                    cover_image.is_used = True
                    cover_image.save()
                return cover_image
            except File.DoesNotExist:
                raise serializers.ValidationError(
                    f"Cover image with ID {value} does not exist"
                )
        return None

    def validate_partnerships(self, value):
        # The PrimaryKeyRelatedField with many=True already validates existence
        # and returns the actual Partnership objects, so we just return them
        return value

    def validate(self, data):
        """
        Validate that end_date is after start_date and fill missing fields
        from the associated event
        """
        if "start_date" in data and "end_date" in data:
            if data["start_date"] >= data["end_date"]:
                raise serializers.ValidationError(
                    {"end_date": "End date must be after start date"}
                )

        # Get the event instance
        event = data.get("event")

        # Fill missing fields with event values if they're not provided
        if not data.get("name", ""):
            data["name"] = event.name

        if not data.get("description", ""):
            data["description"] = event.description

        if "stage" not in data:
            data["stage"] = event.stage

        if "modality" not in data:
            data["modality"] = event.modality

        if "location" not in data and event.location:
            data["location"] = event.location

        if "instructor" not in data and event.instructor:
            data["instructor"] = event.instructor

        if "price" not in data:
            data["price"] = event.price

        if "thumbnail" not in data and event.thumbnail:
            data["thumbnail"] = event.thumbnail

        if "cover_image" not in data and event.cover_image:
            data["cover_image"] = event.cover_image

        return data

    def create(self, validated_data):
        partnerships = validated_data.pop("partnerships", [])

        # Use transaction to ensure atomicity
        from django.db import transaction

        with transaction.atomic():
            # Create event schedule
            event_schedule = super().create(validated_data)

            # Add partnerships using the ManyToManyField
            if partnerships:
                event_schedule.partnerships.add(*partnerships)

        return event_schedule


# Update: para actualización
class CrmEventScheduleUpdateSerializer(CrmEventScheduleBaseSerializer):
    """Serializer for updating existing event schedules"""

    event = serializers.PrimaryKeyRelatedField(
        queryset=Event.objects.filter(deleted=False), required=False
    )
    name = serializers.CharField(max_length=255, required=False)
    description = serializers.CharField(required=False, allow_blank=True)
    start_date = serializers.DateTimeField(required=False)
    end_date = serializers.DateTimeField(required=False)
    stage = serializers.CharField(max_length=24, required=False)
    modality = serializers.CharField(max_length=24, required=False)
    location = serializers.CharField(max_length=255, required=False, allow_null=True)
    instructor = serializers.PrimaryKeyRelatedField(
        queryset=Instructor.objects.filter(deleted=False),
        required=False,
        allow_null=True,
    )
    price = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    thumbnail = serializers.PrimaryKeyRelatedField(
        queryset=File.objects.filter(deleted=False), required=False, allow_null=True
    )
    cover_image = serializers.PrimaryKeyRelatedField(
        queryset=File.objects.filter(deleted=False), required=False, allow_null=True
    )

    partnerships = serializers.PrimaryKeyRelatedField(
        queryset=Partnership.objects.filter(deleted=False),
        many=True,
        required=False,
        allow_empty=True,
    )

    class Meta(CrmEventScheduleBaseSerializer.Meta):
        fields = CrmEventScheduleBaseSerializer.Meta.fields + [
            "partnerships",
        ]

    def validate(self, data):
        """
        Validate that end_date is after start_date
        """
        if "start_date" in data and "end_date" in data:
            if data["start_date"] >= data["end_date"]:
                raise serializers.ValidationError(
                    {"end_date": "End date must be after start date"}
                )

        # If only one date is provided, check against the instance
        instance = self.instance
        if instance:
            if "start_date" in data and "end_date" not in data:
                if data["start_date"] >= instance.end_date:
                    raise serializers.ValidationError(
                        {"start_date": "Start date must be before the current end date"}
                    )

            if "end_date" in data and "start_date" not in data:
                if instance.start_date >= data["end_date"]:
                    raise serializers.ValidationError(
                        {"end_date": "End date must be after the current start date"}
                    )

        return data

    def update(self, instance, validated_data):
        partnerships = validated_data.pop("partnerships", None)

        # Actualizar campos normales
        instance = super().update(instance, validated_data)

        # Si se proporcionaron partnerships, actualizar la relación
        if partnerships is not None:
            # Utilizar set() para reemplazar todas las relaciones existentes
            instance.partnerships.set(partnerships)

        return instance


class CrmEventScheduleEnrollSerializer(serializers.Serializer):
    """Serializer for enrolling in an event schedule"""

    first_name = serializers.CharField(max_length=128)
    last_name = serializers.CharField(max_length=128)
    phone_number = serializers.CharField(max_length=32)
    email = serializers.EmailField()
    ocupation = serializers.CharField(max_length=64)

    def validate_ocupation(self, value):
        if value not in User.OCUPATION_CHOICES:
            raise serializers.ValidationError(
                f"Invalid occupation. Choose from: {User.OCUPATION_CHOICES}"
            )
        return value


class CrmEventScheduleEnrollmentUserSerializer(serializers.ModelSerializer):
    """Serializer for user data in enrollments"""

    full_name = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            "uid",
            "first_name",
            "last_name",
            "full_name",
            "email",
            "phone_number",
        ]

    def get_full_name(self, obj):
        return obj.get_full_name() if obj.get_full_name() != "" else "Sin nombre"


class CrmEventScheduleEnrollmentSerializer(serializers.ModelSerializer):
    """Serializer for listing event schedule enrollments"""

    user = CrmEventScheduleEnrollmentUserSerializer(read_only=True)
    partnership = CrmEventSchedulePartnershipsSerializer(read_only=True)
    full_name = serializers.SerializerMethodField()

    class Meta:
        model = EventScheduleEnrollment
        fields = [
            "id",
            "user",
            "first_name",
            "last_name",
            "full_name",
            "email",
            "phone_number",
            "occupation",
            "major",
            "term",
            "university",
            "interests",
            "diffusion_channel",
            "has_contact",
            "needs_conciliation",
            "already_lead",
            "partnership",
            "created_at",
            "updated_at",
        ]

        extra_kwargs = {
            "created_at": {"read_only": True},
            "updated_at": {"read_only": True},
        }

    def get_full_name(self, obj):
        """Get full name from user or enrollment data"""
        if obj.user:
            return (
                obj.user.get_full_name()
                if obj.user.get_full_name() != ""
                else "Sin nombre"
            )
        elif obj.first_name or obj.last_name:
            return f"{obj.first_name or ''} {obj.last_name or ''}".strip()
        return "Sin nombre"


class CrmEventScheduleFileSerializer(serializers.ModelSerializer):
    file = serializers.FileField(required=True, write_only=True)

    def validate_file(self, value):
        if not value.name.endswith((".jpg", ".jpeg", ".png", ".webp")):
            raise serializers.ValidationError(
                "Invalid file format. Only Images are allowed."
            )
        return value

    def create(self, validated_data):
        file_obj = validated_data.pop("file")
        fid, object_name = upload_file_to_minio(file_obj, bucket_name="public")
        validated_data["is_used"] = False
        validated_data["is_private"] = False
        validated_data["name"] = object_name.split("/")[-1]
        validated_data["bucket_name"] = "public"
        validated_data["object_name"] = object_name
        validated_data["fid"] = fid

        return super().create(validated_data)

    class Meta:
        model = File
        fields = ["file"]
        extra_kwargs = {
            "file": {"required": True},
        }
