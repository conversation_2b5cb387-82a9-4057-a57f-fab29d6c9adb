--
-- PostgreSQL database dump
--

-- Dumped from database version 15.8 (Debian 15.8-1.pgdg120+1)
-- Dumped by pg_dump version 15.8 (Debian 15.8-1.pgdg120+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: auth_group; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.auth_group (
    id integer NOT NULL,
    name character varying(150) NOT NULL
);


ALTER TABLE public.auth_group OWNER TO "portals";

--
-- Name: auth_group_id_seq; Type: SEQUENCE; Schema: public; Owner: ceu-admin
--

ALTER TABLE public.auth_group ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.auth_group_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: auth_group_permissions; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.auth_group_permissions (
    id bigint NOT NULL,
    group_id integer NOT NULL,
    permission_id integer NOT NULL
);


ALTER TABLE public.auth_group_permissions OWNER TO "portals";

--
-- Name: auth_group_permissions_id_seq; Type: SEQUENCE; Schema: public; Owner: ceu-admin
--

ALTER TABLE public.auth_group_permissions ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.auth_group_permissions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: auth_permission; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.auth_permission (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    content_type_id integer NOT NULL,
    codename character varying(100) NOT NULL
);


ALTER TABLE public.auth_permission OWNER TO "portals";

--
-- Name: auth_permission_id_seq; Type: SEQUENCE; Schema: public; Owner: ceu-admin
--

ALTER TABLE public.auth_permission ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.auth_permission_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: authtoken_token; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.authtoken_token (
    key character varying(40) NOT NULL,
    created timestamp with time zone NOT NULL,
    user_id uuid NOT NULL
);


ALTER TABLE public.authtoken_token OWNER TO "portals";

--
-- Name: broadcast_message; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.broadcast_message (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    mid uuid NOT NULL,
    offering_id integer,
    offering_name character varying(100),
    pipeline_state character varying(15) NOT NULL,
    variables jsonb,
    send_at timestamp with time zone NOT NULL,
    status character varying(10) NOT NULL,
    deleted_by_id uuid,
    template_id_id uuid NOT NULL
);


ALTER TABLE public.broadcast_message OWNER TO "portals";

--
-- Name: core_attachment; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_attachment (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    aid uuid NOT NULL,
    deleted_by_id uuid,
    instructor_id uuid NOT NULL,
    offering_id uuid NOT NULL
);


ALTER TABLE public.core_attachment OWNER TO "portals";

--
-- Name: core_benefit; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_benefit (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    bid uuid NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    deleted_by_id uuid
);


ALTER TABLE public.core_benefit OWNER TO "portals";

--
-- Name: core_blogcategory; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_blogcategory (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    bcid uuid NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    deleted_by_id uuid,
    parent_id uuid,
    slug character varying(255) NOT NULL
);


ALTER TABLE public.core_blogcategory OWNER TO "portals";

--
-- Name: core_blogpost; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_blogpost (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    bid uuid NOT NULL,
    slug character varying(255) NOT NULL,
    title character varying(255) NOT NULL,
    summary text NOT NULL,
    content jsonb,
    reading_time integer,
    status character varying(10) NOT NULL,
    published_at timestamp with time zone,
    featured boolean NOT NULL,
    featured_order integer,
    meta_title character varying(255),
    meta_description text,
    meta_keywords character varying(255),
    view_count integer NOT NULL,
    cover_image_id uuid,
    created_by_id uuid,
    deleted_by_id uuid,
    thumbnail_id uuid,
    CONSTRAINT core_blogpost_featured_order_check CHECK ((featured_order >= 0)),
    CONSTRAINT core_blogpost_reading_time_check CHECK ((reading_time >= 0)),
    CONSTRAINT core_blogpost_view_count_check CHECK ((view_count >= 0))
);


ALTER TABLE public.core_blogpost OWNER TO "portals";

--
-- Name: core_blogpost_authors; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_blogpost_authors (
    id bigint NOT NULL,
    blogpost_id uuid NOT NULL,
    instructor_id uuid NOT NULL
);


ALTER TABLE public.core_blogpost_authors OWNER TO "portals";

--
-- Name: core_blogpost_authors_id_seq; Type: SEQUENCE; Schema: public; Owner: ceu-admin
--

ALTER TABLE public.core_blogpost_authors ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.core_blogpost_authors_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: core_blogpost_categories; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_blogpost_categories (
    id bigint NOT NULL,
    blogpost_id uuid NOT NULL,
    blogcategory_id uuid NOT NULL
);


ALTER TABLE public.core_blogpost_categories OWNER TO "portals";

--
-- Name: core_blogpost_categories_id_seq; Type: SEQUENCE; Schema: public; Owner: ceu-admin
--

ALTER TABLE public.core_blogpost_categories ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.core_blogpost_categories_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: core_blogpost_tags; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_blogpost_tags (
    id bigint NOT NULL,
    blogpost_id uuid NOT NULL,
    blogtag_id uuid NOT NULL
);


ALTER TABLE public.core_blogpost_tags OWNER TO "portals";

--
-- Name: core_blogpost_tags_id_seq; Type: SEQUENCE; Schema: public; Owner: ceu-admin
--

ALTER TABLE public.core_blogpost_tags ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.core_blogpost_tags_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: core_blogtag; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_blogtag (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    btid uuid NOT NULL,
    name character varying(255) NOT NULL,
    slug character varying(255) NOT NULL,
    badge_color character varying(7),
    description text,
    deleted_by_id uuid
);


ALTER TABLE public.core_blogtag OWNER TO "portals";

--
-- Name: core_educationalinstitution; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_educationalinstitution (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    eiid uuid NOT NULL,
    name character varying(128) NOT NULL,
    country character varying(64) NOT NULL,
    region character varying(128),
    city character varying(128),
    acronym character varying(32),
    institution_type character varying(64),
    deleted_by_id uuid
);


ALTER TABLE public.core_educationalinstitution OWNER TO "portals";

--
-- Name: core_enrollment; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_enrollment (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    eid uuid NOT NULL,
    enrollment_type character varying(24) NOT NULL,
    deleted_by_id uuid,
    offering_id uuid NOT NULL,
    order_id uuid,
    user_id uuid NOT NULL
);


ALTER TABLE public.core_enrollment OWNER TO "portals";

--
-- Name: core_event; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_event (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    name character varying(255) NOT NULL,
    description text NOT NULL,
    modality character varying(24) NOT NULL,
    type character varying(24) NOT NULL,
    price numeric(10,2) NOT NULL,
    deleted_by_id uuid,
    thumbnail_id uuid,
    instructor_id uuid,
    offering_id uuid,
    cover_image_id uuid,
    eid uuid NOT NULL,
    location character varying(255)
);


ALTER TABLE public.core_event OWNER TO "portals";

--
-- Name: core_eventreminder; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_eventreminder (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    rid uuid NOT NULL,
    reminder_type character varying(2) NOT NULL,
    event_alliance_id integer NOT NULL,
    event_name character varying(255) NOT NULL,
    variables jsonb,
    send_at timestamp with time zone NOT NULL,
    status character varying(10) NOT NULL,
    deleted_by_id uuid,
    template_id_id uuid NOT NULL
);


ALTER TABLE public.core_eventreminder OWNER TO "portals";

--
-- Name: core_eventschedule; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_eventschedule (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    esid uuid NOT NULL,
    is_general boolean NOT NULL,
    start_date timestamp with time zone NOT NULL,
    end_date timestamp with time zone NOT NULL,
    name character varying(255) NOT NULL,
    description text NOT NULL,
    stage character varying(24) NOT NULL,
    modality character varying(24) NOT NULL,
    location character varying(255),
    price numeric(10,2) NOT NULL,
    agenda jsonb,
    ext_event_id character varying(64),
    ext_event_link character varying(255),
    cover_image_id uuid,
    deleted_by_id uuid,
    event_id uuid NOT NULL,
    instructor_id uuid,
    thumbnail_id uuid,
    ext_event_reference character varying(255)
);


ALTER TABLE public.core_eventschedule OWNER TO "portals";

--
-- Name: core_eventschedule_partnerships; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_eventschedule_partnerships (
    id bigint NOT NULL,
    eventschedule_id uuid NOT NULL,
    partnership_id uuid NOT NULL
);


ALTER TABLE public.core_eventschedule_partnerships OWNER TO "portals";

--
-- Name: core_eventschedule_partnerships_id_seq; Type: SEQUENCE; Schema: public; Owner: ceu-admin
--

ALTER TABLE public.core_eventschedule_partnerships ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.core_eventschedule_partnerships_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: core_eventscheduleenrollment; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_eventscheduleenrollment (
    id bigint NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    interests jsonb NOT NULL,
    diffusion_channel character varying(255),
    has_contact boolean NOT NULL,
    deleted_by_id uuid,
    event_schedule_id uuid NOT NULL,
    user_id uuid NOT NULL
);


ALTER TABLE public.core_eventscheduleenrollment OWNER TO "portals";

--
-- Name: core_eventscheduleenrollment_id_seq; Type: SEQUENCE; Schema: public; Owner: ceu-admin
--

ALTER TABLE public.core_eventscheduleenrollment ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.core_eventscheduleenrollment_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: core_file; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_file (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    fid uuid NOT NULL,
    name character varying(255) NOT NULL,
    description text NOT NULL,
    bucket_name character varying(255) NOT NULL,
    object_name character varying(255) NOT NULL,
    is_private boolean NOT NULL,
    width integer,
    height integer,
    deleted_by_id uuid,
    is_used boolean NOT NULL
);


ALTER TABLE public.core_file OWNER TO "portals";

--
-- Name: core_instructor; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_instructor (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    "order" integer,
    iid uuid NOT NULL,
    full_name character varying(255) NOT NULL,
    biography text NOT NULL,
    title character varying(255) NOT NULL,
    highlighted_info character varying(255) NOT NULL,
    facebook_url character varying(200) NOT NULL,
    linkedin_url character varying(200) NOT NULL,
    instagram_url character varying(200) NOT NULL,
    status character varying(10) NOT NULL,
    deleted_by_id uuid,
    profile_photo_id uuid,
    user_id uuid
);


ALTER TABLE public.core_instructor OWNER TO "portals";

--
-- Name: core_leadsource; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_leadsource (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    lsid uuid NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    deleted_by_id uuid
);


ALTER TABLE public.core_leadsource OWNER TO "portals";

--
-- Name: core_major; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_major (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    mid uuid NOT NULL,
    name character varying(128) NOT NULL,
    deleted_by_id uuid
);


ALTER TABLE public.core_major OWNER TO "portals";

--
-- Name: core_modulecourse; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_modulecourse (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    mcid uuid NOT NULL,
    title character varying(255) NOT NULL,
    deleted_by_id uuid,
    module_id uuid NOT NULL
);


ALTER TABLE public.core_modulecourse OWNER TO "portals";

--
-- Name: core_offering; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_offering (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    oid uuid NOT NULL,
    slug character varying(255) NOT NULL,
    name character varying(255) NOT NULL,
    start_date date NOT NULL,
    end_date date NOT NULL,
    description text NOT NULL,
    duration character varying(50) NOT NULL,
    frequency character varying(50) NOT NULL,
    hours integer NOT NULL,
    schedule character varying(255) NOT NULL,
    modality character varying(20) NOT NULL,
    type character varying(40) NOT NULL,
    stage character varying(20) NOT NULL,
    format character varying(20) NOT NULL,
    base_price numeric(10,2) NOT NULL,
    discount numeric(10,2) NOT NULL,
    deleted_by_id uuid,
    thumbnail_id uuid,
    objectives jsonb NOT NULL,
    foreign_base_price numeric(10,2) NOT NULL,
    code_name character varying(128),
    ext_reference character varying(255),
    "order" integer,
    long_name character varying(128),
    CONSTRAINT core_offering_hours_54b0582a_check CHECK ((hours >= 0))
);


ALTER TABLE public.core_offering OWNER TO "portals";

--
-- Name: core_offeringmodule; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_offeringmodule (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    omid uuid NOT NULL,
    title character varying(255) NOT NULL,
    deleted_by_id uuid,
    offering_id uuid NOT NULL
);


ALTER TABLE public.core_offeringmodule OWNER TO "portals";

--
-- Name: core_order; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_order (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    oid uuid NOT NULL,
    deleted_by_id uuid,
    owner_id uuid NOT NULL,
    agreed_total numeric(10,2),
    has_full_scholarship boolean NOT NULL,
    interested_at timestamp with time zone,
    is_international boolean NOT NULL,
    lost_at timestamp with time zone,
    prospect_at timestamp with time zone,
    sales_agent_id uuid,
    stage character varying(50) NOT NULL,
    to_pay_at timestamp with time zone,
    sold_at timestamp with time zone
);


ALTER TABLE public.core_order OWNER TO "portals";

--
-- Name: core_order_benefits; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_order_benefits (
    id bigint NOT NULL,
    order_id uuid NOT NULL,
    benefit_id uuid NOT NULL
);


ALTER TABLE public.core_order_benefits OWNER TO "portals";

--
-- Name: core_order_benefits_id_seq; Type: SEQUENCE; Schema: public; Owner: ceu-admin
--

ALTER TABLE public.core_order_benefits ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.core_order_benefits_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: core_order_lead_sources; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_order_lead_sources (
    id bigint NOT NULL,
    order_id uuid NOT NULL,
    leadsource_id uuid NOT NULL
);


ALTER TABLE public.core_order_lead_sources OWNER TO "portals";

--
-- Name: core_order_lead_sources_id_seq; Type: SEQUENCE; Schema: public; Owner: ceu-admin
--

ALTER TABLE public.core_order_lead_sources ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.core_order_lead_sources_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: core_orderitem; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_orderitem (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    quantity integer NOT NULL,
    deleted_by_id uuid,
    offering_id uuid NOT NULL,
    order_id uuid NOT NULL,
    custom_amount numeric(10,2),
    id bigint NOT NULL,
    CONSTRAINT core_orderitem_quantity_check CHECK ((quantity >= 0))
);


ALTER TABLE public.core_orderitem OWNER TO "portals";

--
-- Name: core_orderitem_id_seq; Type: SEQUENCE; Schema: public; Owner: ceu-admin
--

ALTER TABLE public.core_orderitem ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.core_orderitem_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: core_partnership; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_partnership (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    pid uuid NOT NULL,
    name character varying(128) NOT NULL,
    description text,
    deleted_by_id uuid,
    institution_id uuid NOT NULL,
    delegate_id uuid
);


ALTER TABLE public.core_partnership OWNER TO "portals";

--
-- Name: core_payment; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_payment (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    pid uuid NOT NULL,
    is_paid boolean NOT NULL,
    amount numeric(10,2) NOT NULL,
    payment_date timestamp with time zone NOT NULL,
    ext_payment_id character varying(64),
    currency character varying(3) NOT NULL,
    deleted_by_id uuid,
    order_id uuid,
    voucher_id uuid,
    payment_method_id uuid,
    is_first_payment boolean NOT NULL,
    is_refund boolean NOT NULL,
    observations text,
    scheduled_payment_date timestamp with time zone
);


ALTER TABLE public.core_payment OWNER TO "portals";

--
-- Name: core_paymentmethod; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_paymentmethod (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    pmid uuid NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    deleted_by_id uuid
);


ALTER TABLE public.core_paymentmethod OWNER TO "portals";

--
-- Name: core_session; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_session (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    sid uuid NOT NULL,
    title character varying(255) NOT NULL,
    description text,
    deleted_by_id uuid,
    topic_id uuid NOT NULL
);


ALTER TABLE public.core_session OWNER TO "portals";

--
-- Name: core_sessionresource; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_sessionresource (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    rid uuid NOT NULL,
    title character varying(255) NOT NULL,
    resource_type character varying(20) NOT NULL,
    description text,
    deleted_by_id uuid,
    file_id uuid,
    session_id uuid NOT NULL
);


ALTER TABLE public.core_sessionresource OWNER TO "portals";

--
-- Name: core_template; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_template (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    tid uuid NOT NULL,
    name character varying(255) NOT NULL,
    status character varying(100) NOT NULL,
    header_image_meta_url text,
    body_text text NOT NULL,
    positional_params_example jsonb,
    buttons jsonb,
    deleted_by_id uuid,
    header_image_id uuid
);


ALTER TABLE public.core_template OWNER TO "portals";

--
-- Name: core_term; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_term (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    tid uuid NOT NULL,
    name character varying(32) NOT NULL,
    deleted_by_id uuid
);


ALTER TABLE public.core_term OWNER TO "portals";

--
-- Name: core_testimonial; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_testimonial (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    "order" integer,
    tid uuid NOT NULL,
    author_name character varying(255) NOT NULL,
    author_title character varying(255) NOT NULL,
    content text NOT NULL,
    status character varying(12) NOT NULL,
    author_photo_id uuid,
    deleted_by_id uuid
);


ALTER TABLE public.core_testimonial OWNER TO "portals";

--
-- Name: core_topic; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_topic (
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    tid uuid NOT NULL,
    title character varying(255) NOT NULL,
    course_id uuid NOT NULL,
    deleted_by_id uuid
);


ALTER TABLE public.core_topic OWNER TO "portals";

--
-- Name: core_user; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_user (
    password character varying(128) NOT NULL,
    last_login timestamp with time zone,
    is_superuser boolean NOT NULL,
    username character varying(150) NOT NULL,
    first_name character varying(150) NOT NULL,
    last_name character varying(150) NOT NULL,
    is_staff boolean NOT NULL,
    is_active boolean NOT NULL,
    date_joined timestamp with time zone NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted boolean NOT NULL,
    deleted_at timestamp with time zone,
    uid uuid NOT NULL,
    email character varying(128),
    phone_number character varying(40),
    id_number character varying(40),
    deleted_by_id uuid,
    profile_photo_id uuid,
    company character varying(128),
    google_contact_id character varying(32),
    last_google_sync timestamp with time zone,
    ocupation character varying(52),
    role character varying(128),
    educational_institution_id uuid,
    major_id uuid,
    term_id uuid,
    city character varying(128),
    country character varying(128)
);


ALTER TABLE public.core_user OWNER TO "portals";

--
-- Name: core_user_groups; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_user_groups (
    id bigint NOT NULL,
    user_id uuid NOT NULL,
    group_id integer NOT NULL
);


ALTER TABLE public.core_user_groups OWNER TO "portals";

--
-- Name: core_user_groups_id_seq; Type: SEQUENCE; Schema: public; Owner: ceu-admin
--

ALTER TABLE public.core_user_groups ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.core_user_groups_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: core_user_user_permissions; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.core_user_user_permissions (
    id bigint NOT NULL,
    user_id uuid NOT NULL,
    permission_id integer NOT NULL
);


ALTER TABLE public.core_user_user_permissions OWNER TO "portals";

--
-- Name: core_user_user_permissions_id_seq; Type: SEQUENCE; Schema: public; Owner: ceu-admin
--

ALTER TABLE public.core_user_user_permissions ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.core_user_user_permissions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: django_admin_log; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.django_admin_log (
    id integer NOT NULL,
    action_time timestamp with time zone NOT NULL,
    object_id text,
    object_repr character varying(200) NOT NULL,
    action_flag smallint NOT NULL,
    change_message text NOT NULL,
    content_type_id integer,
    user_id uuid NOT NULL,
    CONSTRAINT django_admin_log_action_flag_check CHECK ((action_flag >= 0))
);


ALTER TABLE public.django_admin_log OWNER TO "portals";

--
-- Name: django_admin_log_id_seq; Type: SEQUENCE; Schema: public; Owner: ceu-admin
--

ALTER TABLE public.django_admin_log ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.django_admin_log_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: django_celery_results_chordcounter; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.django_celery_results_chordcounter (
    id integer NOT NULL,
    group_id character varying(255) NOT NULL,
    sub_tasks text NOT NULL,
    count integer NOT NULL,
    CONSTRAINT django_celery_results_chordcounter_count_check CHECK ((count >= 0))
);


ALTER TABLE public.django_celery_results_chordcounter OWNER TO "portals";

--
-- Name: django_celery_results_chordcounter_id_seq; Type: SEQUENCE; Schema: public; Owner: ceu-admin
--

ALTER TABLE public.django_celery_results_chordcounter ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.django_celery_results_chordcounter_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: django_celery_results_groupresult; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.django_celery_results_groupresult (
    id integer NOT NULL,
    group_id character varying(255) NOT NULL,
    date_created timestamp with time zone NOT NULL,
    date_done timestamp with time zone NOT NULL,
    content_type character varying(128) NOT NULL,
    content_encoding character varying(64) NOT NULL,
    result text
);


ALTER TABLE public.django_celery_results_groupresult OWNER TO "portals";

--
-- Name: django_celery_results_groupresult_id_seq; Type: SEQUENCE; Schema: public; Owner: ceu-admin
--

ALTER TABLE public.django_celery_results_groupresult ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.django_celery_results_groupresult_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: django_celery_results_taskresult; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.django_celery_results_taskresult (
    id integer NOT NULL,
    task_id character varying(255) NOT NULL,
    status character varying(50) NOT NULL,
    content_type character varying(128) NOT NULL,
    content_encoding character varying(64) NOT NULL,
    result text,
    date_done timestamp with time zone NOT NULL,
    traceback text,
    meta text,
    task_args text,
    task_kwargs text,
    task_name character varying(255),
    worker character varying(100),
    date_created timestamp with time zone NOT NULL,
    periodic_task_name character varying(255)
);


ALTER TABLE public.django_celery_results_taskresult OWNER TO "portals";

--
-- Name: django_celery_results_taskresult_id_seq; Type: SEQUENCE; Schema: public; Owner: ceu-admin
--

ALTER TABLE public.django_celery_results_taskresult ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.django_celery_results_taskresult_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: django_content_type; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.django_content_type (
    id integer NOT NULL,
    app_label character varying(100) NOT NULL,
    model character varying(100) NOT NULL
);


ALTER TABLE public.django_content_type OWNER TO "portals";

--
-- Name: django_content_type_id_seq; Type: SEQUENCE; Schema: public; Owner: ceu-admin
--

ALTER TABLE public.django_content_type ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.django_content_type_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: django_migrations; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.django_migrations (
    id bigint NOT NULL,
    app character varying(255) NOT NULL,
    name character varying(255) NOT NULL,
    applied timestamp with time zone NOT NULL
);


ALTER TABLE public.django_migrations OWNER TO "portals";

--
-- Name: django_migrations_id_seq; Type: SEQUENCE; Schema: public; Owner: ceu-admin
--

ALTER TABLE public.django_migrations ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.django_migrations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: django_session; Type: TABLE; Schema: public; Owner: ceu-admin
--

CREATE TABLE public.django_session (
    session_key character varying(40) NOT NULL,
    session_data text NOT NULL,
    expire_date timestamp with time zone NOT NULL
);


ALTER TABLE public.django_session OWNER TO "portals";

--
-- Data for Name: auth_group; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.auth_group (id, name) FROM stdin;
1	students
\.


--
-- Data for Name: auth_group_permissions; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.auth_group_permissions (id, group_id, permission_id) FROM stdin;
\.


--
-- Data for Name: auth_permission; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.auth_permission (id, name, content_type_id, codename) FROM stdin;
1	Can add log entry	1	add_logentry
2	Can change log entry	1	change_logentry
3	Can delete log entry	1	delete_logentry
4	Can view log entry	1	view_logentry
5	Can add permission	2	add_permission
6	Can change permission	2	change_permission
7	Can delete permission	2	delete_permission
8	Can view permission	2	view_permission
9	Can add group	3	add_group
10	Can change group	3	change_group
11	Can delete group	3	delete_group
12	Can view group	3	view_group
13	Can add content type	4	add_contenttype
14	Can change content type	4	change_contenttype
15	Can delete content type	4	delete_contenttype
16	Can view content type	4	view_contenttype
17	Can add session	5	add_session
18	Can change session	5	change_session
19	Can delete session	5	delete_session
20	Can view session	5	view_session
21	Can add Token	6	add_token
22	Can change Token	6	change_token
23	Can delete Token	6	delete_token
24	Can view Token	6	view_token
25	Can add Token	7	add_tokenproxy
26	Can change Token	7	change_tokenproxy
27	Can delete Token	7	delete_tokenproxy
28	Can view Token	7	view_tokenproxy
29	Can add user	8	add_user
30	Can change user	8	change_user
31	Can delete user	8	delete_user
32	Can view user	8	view_user
33	Can add blog category	9	add_blogcategory
34	Can change blog category	9	change_blogcategory
35	Can delete blog category	9	delete_blogcategory
36	Can view blog category	9	view_blogcategory
37	Can add Blog	10	add_blog
38	Can change Blog	10	change_blog
39	Can delete Blog	10	delete_blog
40	Can view Blog	10	view_blog
41	Can add File	11	add_file
42	Can change File	11	change_file
43	Can delete File	11	delete_file
44	Can view File	11	view_file
45	Can add instructor	12	add_instructor
46	Can change instructor	12	change_instructor
47	Can delete instructor	12	delete_instructor
48	Can view instructor	12	view_instructor
49	Can add Academic Offering	13	add_offering
50	Can change Academic Offering	13	change_offering
51	Can delete Academic Offering	13	delete_offering
52	Can view Academic Offering	13	view_offering
53	Can add Event	14	add_event
54	Can change Event	14	change_event
55	Can delete Event	14	delete_event
56	Can view Event	14	view_event
57	Can add Order	15	add_order
58	Can change Order	15	change_order
59	Can delete Order	15	delete_order
60	Can view Order	15	view_order
61	Can add Student	16	add_student
62	Can change Student	16	change_student
63	Can delete Student	16	delete_student
64	Can view Student	16	view_student
65	Can add Testimonial	17	add_testimonial
66	Can change Testimonial	17	change_testimonial
67	Can delete Testimonial	17	delete_testimonial
68	Can view Testimonial	17	view_testimonial
69	Can add Order Item	18	add_orderitem
70	Can change Order Item	18	change_orderitem
71	Can delete Order Item	18	delete_orderitem
72	Can view Order Item	18	view_orderitem
73	Can add enrollment	19	add_enrollment
74	Can change enrollment	19	change_enrollment
75	Can delete enrollment	19	delete_enrollment
76	Can view enrollment	19	view_enrollment
77	Can add Module	20	add_offeringmodule
78	Can change Module	20	change_offeringmodule
79	Can delete Module	20	delete_offeringmodule
80	Can view Module	20	view_offeringmodule
81	Can add Course	21	add_modulecourse
82	Can change Course	21	change_modulecourse
83	Can delete Course	21	delete_modulecourse
84	Can view Course	21	view_modulecourse
85	Can add session	22	add_session
86	Can change session	22	change_session
87	Can delete session	22	delete_session
88	Can view session	22	view_session
89	Can add session resource	23	add_sessionresource
90	Can change session resource	23	change_sessionresource
91	Can delete session resource	23	delete_sessionresource
92	Can view session resource	23	view_sessionresource
93	Can add Topic	24	add_topic
94	Can change Topic	24	change_topic
95	Can delete Topic	24	delete_topic
96	Can view Topic	24	view_topic
97	Can add attachment	25	add_attachment
98	Can change attachment	25	change_attachment
99	Can delete attachment	25	delete_attachment
100	Can view attachment	25	view_attachment
101	Can add task result	26	add_taskresult
102	Can change task result	26	change_taskresult
103	Can delete task result	26	delete_taskresult
104	Can view task result	26	view_taskresult
105	Can add chord counter	27	add_chordcounter
106	Can change chord counter	27	change_chordcounter
107	Can delete chord counter	27	delete_chordcounter
108	Can view chord counter	27	view_chordcounter
109	Can add group result	28	add_groupresult
110	Can change group result	28	change_groupresult
111	Can delete group result	28	delete_groupresult
112	Can view group result	28	view_groupresult
113	Can add Benefit	29	add_benefit
114	Can change Benefit	29	change_benefit
115	Can delete Benefit	29	delete_benefit
116	Can view Benefit	29	view_benefit
117	Can add Blog Tag	30	add_blogtag
118	Can change Blog Tag	30	change_blogtag
119	Can delete Blog Tag	30	delete_blogtag
120	Can view Blog Tag	30	view_blogtag
121	Can add Blog Post	31	add_blogpost
122	Can change Blog Post	31	change_blogpost
123	Can delete Blog Post	31	delete_blogpost
124	Can view Blog Post	31	view_blogpost
125	Can add Educational Institution	32	add_educationalinstitution
126	Can change Educational Institution	32	change_educationalinstitution
127	Can delete Educational Institution	32	delete_educationalinstitution
128	Can view Educational Institution	32	view_educationalinstitution
129	Can add Event Schedule	33	add_eventschedule
130	Can change Event Schedule	33	change_eventschedule
131	Can delete Event Schedule	33	delete_eventschedule
132	Can view Event Schedule	33	view_eventschedule
133	Can add event schedule enrollment	34	add_eventscheduleenrollment
134	Can change event schedule enrollment	34	change_eventscheduleenrollment
135	Can delete event schedule enrollment	34	delete_eventscheduleenrollment
136	Can view event schedule enrollment	34	view_eventscheduleenrollment
137	Can add Lead Source	35	add_leadsource
138	Can change Lead Source	35	change_leadsource
139	Can delete Lead Source	35	delete_leadsource
140	Can view Lead Source	35	view_leadsource
141	Can add major	36	add_major
142	Can change major	36	change_major
143	Can delete major	36	delete_major
144	Can view major	36	view_major
145	Can add Partnership	37	add_partnership
146	Can change Partnership	37	change_partnership
147	Can delete Partnership	37	delete_partnership
148	Can view Partnership	37	view_partnership
149	Can add payment method	38	add_paymentmethod
150	Can change payment method	38	change_paymentmethod
151	Can delete payment method	38	delete_paymentmethod
152	Can view payment method	38	view_paymentmethod
153	Can add payment	39	add_payment
154	Can change payment	39	change_payment
155	Can delete payment	39	delete_payment
156	Can view payment	39	view_payment
157	Can add Message Template	40	add_template
158	Can change Message Template	40	change_template
159	Can delete Message Template	40	delete_template
160	Can view Message Template	40	view_template
161	Can add Event Reminder	41	add_eventreminder
162	Can change Event Reminder	41	change_eventreminder
163	Can delete Event Reminder	41	delete_eventreminder
164	Can view Event Reminder	41	view_eventreminder
165	Can add Broadcast Message	42	add_broadcastmessage
166	Can change Broadcast Message	42	change_broadcastmessage
167	Can delete Broadcast Message	42	delete_broadcastmessage
168	Can view Broadcast Message	42	view_broadcastmessage
169	Can add term	43	add_term
170	Can change term	43	change_term
171	Can delete term	43	delete_term
172	Can view term	43	view_term
\.


--
-- Data for Name: authtoken_token; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.authtoken_token (key, created, user_id) FROM stdin;
db81f05f8b76398231ecb433ea031e5c5c0e4998	2025-02-26 06:07:16.923736+00	00a5b78d-22cb-4406-b9d7-f442c38b5d40
63edf19c47f73883b5b632887992d1f1f109d961	2025-02-27 23:26:23.469528+00	7307e0aa-9cd7-44db-92c4-f833641606a5
dc46bbf916480b3aa3e6c4cf941ae15cddbe7940	2025-02-27 23:35:30.824639+00	84eae7f0-ec7d-4f33-abe0-8e0eb6cffff4
150062cd9a0fac551126849f74c88a7b0d8da9ee	2025-02-28 14:15:20.243801+00	c41e21f9-525a-44f2-9b07-9df22fab5157
668b014de25da64b2051c64559ef4bdcfa0f4196	2025-03-28 21:15:25.105163+00	73899230-d542-4df9-ac5a-18d74e0d8578
ac0d1969f117d87b5c64cc7f9e26b19a04ba53a4	2025-03-31 15:31:47.116082+00	26602812-27d6-4320-9d0f-0a9b8b41cc44
c67d867aac85aee2b4a29b89911828cf1621e5fa	2025-04-16 00:14:16.152932+00	2edd1b05-304c-441e-bd14-099dcb667572
919fcdacf12801b2a12acbabec9eeed1a0f2af89	2025-04-17 04:22:55.187764+00	e8fb2887-a36a-4497-8710-6166283d2abc
aeb6c2140bdab395d6c2a16950b334822e410937	2025-04-29 16:30:08.183068+00	438fff54-df30-45b7-af07-6680975ed3f4
1de228218610079eaeaa584f25e79fadd4a4e13b	2025-05-17 00:55:41.427553+00	16c8df94-b3bf-4a1d-9026-40c7dd295695
3226a511d395213d001ac9c989dcd883a6ed67a5	2025-05-17 19:04:49.356276+00	196cd138-7527-4068-99d6-ef52f7ad2c17
0730fb0446467c0009945922d24a1e044738b569	2025-06-05 19:21:54.540821+00	0caeccd9-e704-4b48-a3d0-05ab8ff47d4c
fd8b8b15766e915438707eee1ad1d3cc70146a73	2025-06-08 04:13:30.325366+00	12a48c4b-f9f1-49b9-b4ba-8291bc203bb8
51050dd9e427b28cbc51d4034a2beee67efef177	2025-06-14 21:35:49.391747+00	161df90e-9a84-40e5-95ab-31757d19ec11
f88e7e6bfa364cae5871623cee885149d6bef024	2025-06-28 16:09:14.702446+00	b5ab4d84-6187-48ce-a7be-9563d6116aa0
6d01e87632038b25a916c4d3ba1f7713a8f8c9ea	2025-06-28 21:43:02.248668+00	62767bc8-1e23-4a77-a55c-ce2dc9f46c8f
01aec2ba6f02e87aeea55c41d02177ce1331723b	2025-07-03 17:30:22.453664+00	2e0e215c-ed90-4bd0-a5d8-9c90005cce33
f7c87ba3e4cc28f0e035b5860addddcf81516303	2025-07-06 15:49:19.284927+00	13aaa0c6-8361-4466-ad62-bc11db4e1c36
a93d40c3ed94990a734fc3aef4beccdc5dd50fd6	2025-07-06 20:35:24.228213+00	f38734ba-ca79-4ef8-8a53-eea8ee2ff5af
9a65242c8d6dc2ec664b555fd7ec17448ce413ca	2025-07-08 18:57:34.424038+00	0f03747a-7282-4407-b93f-c6db4bf39ff2
a532d8d046c6462e42665fa5eff4b068026e6079	2025-07-12 02:09:53.841644+00	1e924469-2792-41e2-88b2-de62fd35ec8b
a724fa3af5f1b7041f4c8c1d1ef7db8f0cdd75e4	2025-07-14 17:11:33.371112+00	37279067-57a7-497e-8c68-377fc0d36d22
c7480bef3864e619db2f2a038e7b139d47753229	2025-07-15 06:17:39.012284+00	0303e615-540b-47cd-8694-2735f5c0255f
1496bf14de8087bb368b6cf4ab6fef5302488f5b	2025-07-15 15:50:10.374373+00	82f2bdb5-0a87-4983-a687-4ad056610c2f
53d59768981749fcc01ce828c003b4cfd0663ad8	2025-07-17 04:18:11.050627+00	cdaa2ad2-76bd-44ba-9f01-c167a4325513
\.


--
-- Data for Name: broadcast_message; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.broadcast_message (created_at, updated_at, deleted, deleted_at, mid, offering_id, offering_name, pipeline_state, variables, send_at, status, deleted_by_id, template_id_id) FROM stdin;
\.


--
-- Data for Name: core_attachment; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_attachment (created_at, updated_at, deleted, deleted_at, aid, deleted_by_id, instructor_id, offering_id) FROM stdin;
2025-03-06 17:43:29.109381+00	2025-03-06 17:43:29.109391+00	f	\N	402b308c-1868-49fa-b262-ceb88adff289	\N	d5cf1d2d-e456-4fc4-ae07-4da7b44695ca	7829808c-71ff-45a0-9dd6-ca90e01447ea
2025-03-06 17:43:29.109434+00	2025-03-06 17:43:29.109438+00	f	\N	121f153a-c591-44c8-9d61-0b7f8d1f0bac	\N	4dba9bfd-b37f-410b-84c8-bbcdaf742498	7829808c-71ff-45a0-9dd6-ca90e01447ea
2025-03-06 17:43:29.109467+00	2025-03-06 17:43:29.10947+00	f	\N	742c0ea9-8041-49b0-a624-be1d8cab3c2b	\N	8922a180-9c57-4729-8d34-33c8237147d0	7829808c-71ff-45a0-9dd6-ca90e01447ea
2025-03-06 17:43:29.109497+00	2025-03-06 17:43:29.109501+00	f	\N	d4643e6f-7bae-444c-acee-22949d029467	\N	6200e0bc-fa2f-4a00-8f57-553160328063	7829808c-71ff-45a0-9dd6-ca90e01447ea
2025-03-28 23:54:27.651318+00	2025-03-28 23:54:27.651329+00	f	\N	e678a160-eca2-4695-8460-c5f5d5a50039	\N	4dba9bfd-b37f-410b-84c8-bbcdaf742498	3424b4ac-987e-4ed4-9890-fa79603f8789
2025-05-16 22:05:03.39375+00	2025-05-16 22:05:03.393758+00	f	\N	a800be74-121a-4712-bc67-09b7b5485e91	\N	4dba9bfd-b37f-410b-84c8-bbcdaf742498	42114c08-4ba1-46d1-a930-09ac632c09f9
2025-05-18 01:21:24.701542+00	2025-05-18 01:21:24.701551+00	f	\N	95ebad4c-ce8c-428d-b5e8-e5e675435464	\N	4dba9bfd-b37f-410b-84c8-bbcdaf742498	86629ba1-3a42-407d-83c3-32d1f7171e67
2025-05-18 04:12:50.233851+00	2025-05-18 04:12:50.233859+00	f	\N	5276b859-27b9-4df2-adef-855bb029d3bf	\N	4dba9bfd-b37f-410b-84c8-bbcdaf742498	2949a0bb-dd02-4312-b17f-c7d893d28de1
2025-05-21 20:49:35.529976+00	2025-05-21 20:49:35.529985+00	f	\N	6e3565e5-bff4-4c39-8cfd-c96c0f792525	\N	4dba9bfd-b37f-410b-84c8-bbcdaf742498	a6e7d9c9-64d3-4565-a7cb-029e45e90a49
\.


--
-- Data for Name: core_benefit; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_benefit (created_at, updated_at, deleted, deleted_at, bid, name, description, deleted_by_id) FROM stdin;
\.


--
-- Data for Name: core_blogcategory; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_blogcategory (created_at, updated_at, deleted, deleted_at, bcid, name, description, deleted_by_id, parent_id, slug) FROM stdin;
2025-06-07 01:00:11.797161+00	2025-06-07 01:00:11.797174+00	f	\N	9fd97e8d-7968-4ea3-8d7e-2a4b90ab89f6	Preparación	Programa de Preparación para CEUs	\N	\N	preparacion
\.


--
-- Data for Name: core_blogpost; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_blogpost (created_at, updated_at, deleted, deleted_at, bid, slug, title, summary, content, reading_time, status, published_at, featured, featured_order, meta_title, meta_description, meta_keywords, view_count, cover_image_id, created_by_id, deleted_by_id, thumbnail_id) FROM stdin;
2025-06-07 00:20:44.19311+00	2025-06-07 02:04:38.612172+00	f	\N	1117fa45-1635-4e99-9a66-94630b1c254f	5-estrategias-clave-para-prepararte-para-los-cursos-de-extension-universitaria-en-peru	5 Estrategias Clave para Prepararte para los Cursos de Extensión Universitaria en Perú	Los Cursos de Extensión Universitaria en Perú representan una valiosa oportunidad para adquirir conocimientos especializados y abrir puertas laborales en el sector público. Ofrecidos principalmente por entidades como PROINVERSIÓN, BCRP, SBS, OSINERGMIN y OSIPTEL, estos programas requieren una preparación adecuada para superar sus exigentes procesos de selección y aprovechar al máximo su contenido.	{"038f991b-8468-44fb-9f6e-eb50b784c3b4": {"id": "038f991b-8468-44fb-9f6e-eb50b784c3b4", "meta": {"align": "left", "depth": 0, "order": 40}, "type": "BulletedList", "value": [{"id": "cee9b333-cf60-4fd9-827b-f9a39df7728b", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Estudia conceptos básicos relacionados con estos temas"}]}]}, "0bd5e08c-720d-45f2-a736-b8e446a4bb88": {"id": "0bd5e08c-720d-45f2-a736-b8e446a4bb88", "meta": {"align": "left", "depth": 0, "order": 6}, "type": "BulletedList", "value": [{"id": "4e931a2f-657b-4723-a980-efc87049e3c9", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Están dirigidos a estudiantes de últimos ciclos o recién egresados"}]}]}, "11d8e5fb-29ab-42a8-a6b1-16da175369e9": {"id": "11d8e5fb-29ab-42a8-a6b1-16da175369e9", "meta": {"align": "left", "depth": 0, "order": 26}, "type": "Paragraph", "value": [{"id": "4c952f7d-1928-48ba-ac53-1fdfc82963c3", "type": "paragraph", "props": {"node_type": "block"}, "children": [{"text": "Una buena planificación te permitirá aprovechar al máximo la experiencia formativa y evitar contratiempos que puedan afectar tu desempeño en el curso."}]}]}, "1664334c-d09c-471e-b9f0-c40dafdfd6cd": {"id": "1664334c-d09c-471e-b9f0-c40dafdfd6cd", "meta": {"align": "left", "depth": 0, "order": 49}, "type": "BulletedList", "value": [{"id": "9508aea2-3ea1-4303-b20a-43002d161cd7", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Practica para entrevistas personales enfocadas en motivación y aptitudes"}]}]}, "1b4301ca-acd8-4f91-93a8-cc88ebeb4537": {"id": "1b4301ca-acd8-4f91-93a8-cc88ebeb4537", "meta": {"align": "left", "depth": 0, "order": 18}, "type": "HeadingTwo", "value": [{"id": "6a3cfe3b-eb70-4410-8463-16409ffc6964", "type": "heading-two", "props": {"node_type": "block"}, "children": [{"text": "2. Organizar tu Tiempo"}]}]}, "2418ef15-f16e-4fc6-a5a1-e97aa8f066cb": {"id": "2418ef15-f16e-4fc6-a5a1-e97aa8f066cb", "meta": {"align": "left", "depth": 0, "order": 0}, "type": "Paragraph", "value": [{"id": "fb31dbb5-0aaf-47e2-9fcd-7abd6cbc479e", "type": "paragraph", "props": {"node_type": "block"}, "children": [{"text": "Los Cursos de Extensión Universitaria en Perú son programas formativos especializados que son ofrecidos principalmente por entidades públicas como PROINVERSIÓN, BCRP, SBS, OSINERGMIN, OSIPTEL, entre otras, en colaboración con instituciones educativas. Estos cursos están enfocados en temas específicos de gestión pública, inversión y otros campos relevantes para el desarrollo del país."}]}]}, "244739b0-aebc-49db-8b1b-881d54eeeb3e": {"id": "244739b0-aebc-49db-8b1b-881d54eeeb3e", "meta": {"align": "left", "depth": 0, "order": 16}, "type": "BulletedList", "value": [{"id": "fa1475a7-c46e-4275-96ef-547b326124dc", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Investiga ediciones anteriores del curso y sus resultados"}]}]}, "25e971a3-1912-456f-9482-06180310afd2": {"id": "25e971a3-1912-456f-9482-06180310afd2", "meta": {"align": "left", "depth": 0, "order": 21}, "type": "BulletedList", "value": [{"id": "da2636f9-6bc3-4ec0-974b-3f64672e13d3", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Evalúa la compatibilidad del curso con tus otras responsabilidades académicas o laborales"}]}]}, "2b390f81-0486-45ea-a676-e08e940df953": {"id": "2b390f81-0486-45ea-a676-e08e940df953", "meta": {"align": "left", "depth": 0, "order": 56}, "type": "Paragraph", "value": [{"id": "671c2547-d537-4ce8-aac8-1e0df2f201b2", "type": "paragraph", "props": {"node_type": "block"}, "children": [{"text": "Sin embargo, la competencia para ingresar a estos prestigiosos programas es cada vez más intensa. Los exámenes de admisión evalúan conocimientos especializados y requieren una preparación rigurosa. Por ello, considerar un programa de preparación específico como el que ofrece el"}, {"text": " "}, {"bold": true, "text": "Centro de Especialización Universitaria (CEU)"}, {"text": "puede ser una inversión inteligente en tu futuro profesional."}]}]}, "2b9af68a-b141-4c1d-b8f4-14a6a793bbf4": {"id": "2b9af68a-b141-4c1d-b8f4-14a6a793bbf4", "meta": {"align": "left", "depth": 0, "order": 35}, "type": "Paragraph", "value": [{"id": "563162cc-9965-4c79-818a-aff8d6935335", "type": "paragraph", "props": {"node_type": "block"}, "children": [{"text": "Contar con un espacio adecuado para el estudio te permitirá mantener el foco en los contenidos del curso y participar activamente en todas las actividades propuestas."}]}]}, "2da1a393-a3c0-4ec7-ba0f-bd2167c15717": {"id": "2da1a393-a3c0-4ec7-ba0f-bd2167c15717", "meta": {"align": "left", "depth": 0, "order": 20}, "type": "Paragraph", "value": [{"id": "93c05bce-01b2-4238-acc0-679b8abcb92b", "type": "paragraph", "props": {"node_type": "block"}, "children": [{"text": "¿Cómo implementarla?"}]}]}, "3050b966-caf3-494c-b8c9-8608d7aae5d5": {"id": "3050b966-caf3-494c-b8c9-8608d7aae5d5", "meta": {"align": "left", "depth": 0, "order": 34}, "type": "BulletedList", "value": [{"id": "de78f390-0866-42dd-b7e3-7dc785e99b9f", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Crea un ambiente confortable que favorezca la concentración"}]}]}, "3300f31c-055c-4f1b-9fc7-2b3b9bc41acd": {"id": "3300f31c-055c-4f1b-9fc7-2b3b9bc41acd", "meta": {"align": "left", "depth": 0, "order": 17}, "type": "Paragraph", "value": [{"id": "5725224e-9ec7-4f97-823c-9df3a1bf103f", "type": "paragraph", "props": {"node_type": "block"}, "children": [{"text": "Por ejemplo, si estás interesado en el Curso de Extensión Universitaria de PROINVERSIÓN, deberías familiarizarte con su rol en las Asociaciones Público-Privadas (APP) y los Proyectos en Activos (PA), así como los principales proyectos que han gestionado recientemente en el Perú."}]}]}, "37777825-6e9f-4212-bf79-00168700c444": {"id": "37777825-6e9f-4212-bf79-00168700c444", "meta": {"align": "left", "depth": 0, "order": 31}, "type": "BulletedList", "value": [{"id": "e5e23dcd-74c2-4538-9807-549b62efc322", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Asegura una conexión a internet estable para las clases virtuales"}]}]}, "385e9729-5c2f-4384-a8f0-5c62e56c2d93": {"id": "385e9729-5c2f-4384-a8f0-5c62e56c2d93", "meta": {"align": "left", "depth": 0, "order": 28}, "type": "Paragraph", "value": [{"id": "8c5ff468-f7e0-49a0-b9dd-4b44c4010c7f", "type": "paragraph", "props": {"node_type": "block"}, "children": [{"text": "Para el éxito en tu Curso de Extensión Universitaria, es fundamental contar con un espacio físico o virtual adecuado para el estudio y la participación en las actividades académicas."}]}]}, "3942ca11-84ed-41d1-bff4-04a7b89fd595": {"id": "3942ca11-84ed-41d1-bff4-04a7b89fd595", "meta": {"align": "left", "depth": 0, "order": 27}, "type": "HeadingTwo", "value": [{"id": "92cf4650-1a9e-49b5-8ea9-ac5d10a05031", "type": "heading-two", "props": {"node_type": "block"}, "children": [{"text": "3. Establecer un Espacio de Estudio Adecuado"}]}]}, "3a5b24fb-46fd-49c1-b782-45cd8639ebc2": {"id": "3a5b24fb-46fd-49c1-b782-45cd8639ebc2", "meta": {"align": "left", "depth": 0, "order": 50}, "type": "BulletedList", "value": [{"id": "a3f87c75-802a-44b7-b0aa-1ad09a4d196a", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Elabora un CV orientado a destacar habilidades relevantes"}]}]}, "3bd4dff3-3f67-4f9b-b037-28ce45a3c1ad": {"id": "3bd4dff3-3f67-4f9b-b037-28ce45a3c1ad", "meta": {"align": "left", "depth": 0, "order": 33}, "type": "BulletedList", "value": [{"id": "688221fd-8768-425f-b6b9-89031f6725f4", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Configura dispositivos electrónicos necesarios para acceder a plataformas virtuales"}]}]}, "40a54160-effe-4c49-8d0d-f6f2d5b48068": {"id": "40a54160-effe-4c49-8d0d-f6f2d5b48068", "meta": {"align": "left", "depth": 0, "order": 45}, "type": "HeadingTwo", "value": [{"id": "022569c0-178a-4c5a-9907-d22c1f194184", "type": "heading-two", "props": {"node_type": "block"}, "children": [{"text": "5. Mantener una Mentalidad Abierta y Proactiva"}]}]}, "4893be0d-593e-4fcd-b677-7f4f4801ec3a": {"id": "4893be0d-593e-4fcd-b677-7f4f4801ec3a", "meta": {"align": "left", "depth": 0, "order": 12}, "type": "BulletedList", "value": [{"id": "645285a1-bf15-43b7-860a-c983da27727a", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Revisa el portal web oficial de la entidad"}]}]}, "491ff339-ef27-43b4-8a84-16cf51d8de75": {"id": "491ff339-ef27-43b4-8a84-16cf51d8de75", "meta": {"align": "left", "depth": 0, "order": 42}, "type": "BulletedList", "value": [{"id": "3a7ea7ae-71d7-4a15-a6a5-0cf622038f88", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Sigue noticias actuales relacionadas con la temática"}]}]}, "493b95e3-dd3f-42c3-b476-2097168bbe6a": {"id": "493b95e3-dd3f-42c3-b476-2097168bbe6a", "meta": {"align": "left", "depth": 0, "order": 29}, "type": "Paragraph", "value": [{"id": "63e5395f-3152-4c79-853a-e25bee19b20d", "type": "paragraph", "props": {"node_type": "block"}, "children": [{"bold": true, "text": "¿Cómo implementarla?"}]}]}, "4945cf11-ca63-4bbb-80f8-70c8086b0db2": {"id": "4945cf11-ca63-4bbb-80f8-70c8086b0db2", "meta": {"align": "left", "depth": 0, "order": 48}, "type": "BulletedList", "value": [{"id": "acc26546-7323-4962-ac2d-51129d698c07", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Prepárate para pruebas psicotécnicas y psicológicas"}]}]}, "4d340d11-5c4e-4b86-90b3-796ebb26fb5a": {"id": "4d340d11-5c4e-4b86-90b3-796ebb26fb5a", "meta": {"align": "left", "depth": 0, "order": 3}, "type": "BulletedList", "value": [{"id": "d1b48acf-fc27-4888-baae-9ee1f1903372", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Tienen una duración definida"}]}]}, "57f7bf76-41fd-4adc-beda-e8337ba7ef5a": {"id": "57f7bf76-41fd-4adc-beda-e8337ba7ef5a", "meta": {"align": "left", "depth": 0, "order": 38}, "type": "Paragraph", "value": [{"id": "fd95492e-0f0a-4596-81ee-6857b4875f3d", "type": "paragraph", "props": {"node_type": "block"}, "children": [{"bold": true, "text": "¿Cómo implementarla?"}]}]}, "5d4be953-5db4-42ac-abdf-9be697ab1ebe": {"id": "5d4be953-5db4-42ac-abdf-9be697ab1ebe", "meta": {"align": "left", "depth": 0, "order": 5}, "type": "BulletedList", "value": [{"id": "72951d97-6cda-4e2b-a533-4f6b2db301cb", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Ofrecen certificaciones oficiales"}]}]}, "6183a8f3-890f-49c5-84bd-a3614d1ab50f": {"id": "6183a8f3-890f-49c5-84bd-a3614d1ab50f", "meta": {"align": "left", "depth": 0, "order": 4}, "type": "BulletedList", "value": [{"id": "fefde9f6-97f3-46e3-b837-8c17b1bc8484", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Utilizan metodología teórico-práctica especializada"}]}]}, "68ee44a9-5f66-4964-b547-d942206d35de": {"id": "68ee44a9-5f66-4964-b547-d942206d35de", "meta": {"align": "left", "depth": 0, "order": 8}, "type": "BulletedList", "value": [{"id": "4a6a690d-1e1c-42b2-8131-3a319267edbb", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Ofrecen oportunidades de prácticas para los mejores estudiantes"}]}]}, "693aaff1-6821-4154-a3b9-31a94c61b79b": {"id": "693aaff1-6821-4154-a3b9-31a94c61b79b", "meta": {"align": "left", "depth": 0, "order": 32}, "type": "BulletedList", "value": [{"id": "7395d688-4a56-4baa-8016-3147417b516a", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Organiza los materiales de estudio de manera accesible"}]}]}, "694a794b-f385-42f0-9da0-5d71145cc598": {"id": "694a794b-f385-42f0-9da0-5d71145cc598", "meta": {"align": "left", "depth": 0, "order": 10}, "type": "Paragraph", "value": [{"id": "3f895ebe-afd5-465e-81ef-e48a8abb55c9", "type": "paragraph", "props": {"node_type": "block"}, "children": [{"text": "Antes de postular a un Curso de Extensión Universitaria, es fundamental investigar a fondo la entidad que lo ofrece, su misión, visión, proyectos principales y el contenido específico del programa. Esto te permitirá entender el enfoque del curso y su relevancia para tu desarrollo profesional."}]}]}, "70ed3bf9-fa47-47df-9a32-de9c7089ed82": {"id": "70ed3bf9-fa47-47df-9a32-de9c7089ed82", "meta": {"align": "left", "depth": 0, "order": 52}, "type": "BulletedList", "value": [{"id": "ea281a58-9298-461f-850f-1e149d0c56b1", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Mantén una actitud positiva y resiliente ante los desafíos"}]}]}, "77f7732f-b992-4392-95db-95272e39a189": {"id": "77f7732f-b992-4392-95db-95272e39a189", "meta": {"align": "left", "depth": 0, "order": 13}, "type": "BulletedList", "value": [{"id": "7f33bfba-f254-4630-a2c3-b6307e513473", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Busca noticias recientes sobre sus proyectos o iniciativas"}]}]}, "81cbb2f0-c87d-4e03-b9d6-9693589b41ec": {"id": "81cbb2f0-c87d-4e03-b9d6-9693589b41ec", "meta": {"align": "left", "depth": 0, "order": 43}, "type": "BulletedList", "value": [{"id": "38750aa5-1ea8-4418-a8ec-34bb07ae163d", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Familiarízate con la terminología específica del sector"}]}]}, "899670fc-6a18-48f5-8dd1-bc22a2562abe": {"id": "899670fc-6a18-48f5-8dd1-bc22a2562abe", "meta": {"depth": 0, "order": 57}, "type": "Blockquote", "value": [{"id": "f59cc453-f58b-4681-b814-5d302b4adbbc", "type": "blockquote", "children": [{"text": "¡No pierdas esta oportunidad!\\nEl Programa de Preparación para Cursos de Extensión Universitaria del Centro de Especialización Universitaria está diseñado específicamente para ayudarte a superar los exigentes exámenes de admisión, con docentes experimentados, malla curricular adaptada y simulacros de exámenes. ¡Prepárate adecuadamente y aprovecha al máximo esta extraordinaria oportunidad de aprendizaje y crecimiento profesional!"}]}]}, "902cab2b-a326-4e19-87a8-123ebc7725da": {"id": "902cab2b-a326-4e19-87a8-123ebc7725da", "meta": {"align": "left", "depth": 0, "order": 24}, "type": "BulletedList", "value": [{"id": "5f47d987-e711-4ade-ae8b-4750ad7c7271", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Considera con anticipación la posibilidad de realizar prácticas pre-profesionales posteriores"}]}]}, "9e151042-f25b-4ee5-90a8-ad2c5fb5dbf8": {"id": "9e151042-f25b-4ee5-90a8-ad2c5fb5dbf8", "meta": {"align": "left", "depth": 0, "order": 15}, "type": "BulletedList", "value": [{"id": "3fe0ecee-0085-4733-bb60-30319c186b2d", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Entiende la relevancia del curso en el contexto de la entidad"}]}]}, "9ef27717-210e-42cf-97f4-4849109c0d3d": {"id": "9ef27717-210e-42cf-97f4-4849109c0d3d", "meta": {"align": "left", "depth": 0, "order": 2}, "type": "BulletedList", "value": [{"id": "ac5da596-5924-4e51-a3a6-7724abb3c5ee", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Son ofrecidos por entidades públicas"}]}]}, "a13cd708-c802-4b6f-8fa2-0d101ccf1d9b": {"id": "a13cd708-c802-4b6f-8fa2-0d101ccf1d9b", "meta": {"depth": 0, "order": 44}, "type": "Blockquote", "value": [{"id": "c906bef6-5829-40b1-b0f4-85ad684791a0", "type": "blockquote", "children": [{"bold": true, "text": "Programa de Preparación Especializado "}, {"text": "\\nUna opción altamente recomendable para maximizar tus posibilidades de éxito es el Programa de Preparación para Cursos de Extensión Universitaria que ofrecemos. Este programa incluye una malla curricular completa en macroeconomía, microeconomía, finanzas y herramientas cuantitativas con docentes experimentados que han ocupado primeros puestos en Cursos de Extensión Universitaria anteriores."}]}]}, "a6ab905f-245a-453d-93f1-ec52a3ac81ab": {"id": "a6ab905f-245a-453d-93f1-ec52a3ac81ab", "meta": {"align": "left", "depth": 0, "order": 22}, "type": "BulletedList", "value": [{"id": "ba3cf5e1-9a6f-4a50-90e5-d3ce1d416a67", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Planifica el transporte hacia el lugar de las clases (en caso de modalidad presencial)"}]}]}, "ac64ac43-2a2c-4edd-bd7a-3e2f6dd1e469": {"id": "ac64ac43-2a2c-4edd-bd7a-3e2f6dd1e469", "meta": {"align": "left", "depth": 0, "order": 54}, "type": "HeadingTwo", "value": [{"id": "6f79b007-eec8-48b1-94f7-05815cafd447", "type": "heading-two", "props": {"node_type": "block"}, "children": [{"text": "Conclusión"}]}]}, "b1190b68-c86d-4adf-a12b-2dfe2f3e0786": {"id": "b1190b68-c86d-4adf-a12b-2dfe2f3e0786", "meta": {"align": "left", "depth": 0, "order": 11}, "type": "Paragraph", "value": [{"id": "3089766c-1237-474d-a88b-3f3ae4110c59", "type": "paragraph", "props": {"node_type": "block"}, "children": [{"text": "¿Cómo implementarla?"}]}]}, "b36a956d-88f9-450b-9a99-89052fd8db30": {"id": "b36a956d-88f9-450b-9a99-89052fd8db30", "meta": {"align": "left", "depth": 0, "order": 51}, "type": "BulletedList", "value": [{"id": "4f3a1779-9043-469d-9101-5c4ed4582ccf", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Prepara una presentación personal efectiva"}]}]}, "b6a1a6bf-5417-42d9-9693-a628a2e62c2c": {"id": "b6a1a6bf-5417-42d9-9693-a628a2e62c2c", "meta": {"align": "left", "depth": 0, "order": 53}, "type": "Paragraph", "value": [{"id": "4cf712f0-542f-4bb7-8667-094c82062d9e", "type": "paragraph", "props": {"node_type": "block"}, "children": [{"text": "Recuerda que estos cursos no solo te brindan formación especializada en temas no cubiertos en el pregrado regular, sino que también pueden ser tu puerta de entrada a prácticas pre-profesionales o profesionales en importantes instituciones públicas."}]}]}, "b7ff1427-8d6e-4414-976d-a9be0c9f5cfb": {"id": "b7ff1427-8d6e-4414-976d-a9be0c9f5cfb", "meta": {"align": "left", "depth": 0, "order": 25}, "type": "BulletedList", "value": [{"id": "92fff44a-2b1b-4909-8f21-42f5fefa9f1b", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Respeta rigurosamente el cronograma de inscripción y actividades"}]}]}, "b9eca244-e856-4e84-9446-84d786fb2e35": {"id": "b9eca244-e856-4e84-9446-84d786fb2e35", "meta": {"align": "left", "depth": 0, "order": 9}, "type": "HeadingTwo", "value": [{"id": "2f16d246-6455-458f-a985-164df612113a", "type": "heading-two", "props": {"node_type": "block"}, "children": [{"text": "1. Definir Objetivos Claros"}]}]}, "c21b1468-d0ea-4625-96bf-ad67c69e008d": {"id": "c21b1468-d0ea-4625-96bf-ad67c69e008d", "meta": {"align": "left", "depth": 0, "order": 46}, "type": "Paragraph", "value": [{"id": "134d7516-8ba7-4e4e-90af-29d58ba95d3c", "type": "paragraph", "props": {"node_type": "block"}, "children": [{"text": "Los Cursos de Extensión Universitaria ofrecidos por entidades públicas suelen tener procesos formales de admisión. Es crucial desarrollar una mentalidad abierta y proactiva para superar con éxito las distintas etapas de evaluación."}]}]}, "c457cc32-5da7-4d55-aaf1-f747e8f0351f": {"id": "c457cc32-5da7-4d55-aaf1-f747e8f0351f", "meta": {"align": "left", "depth": 0, "order": 41}, "type": "BulletedList", "value": [{"id": "fa94a09f-b7e3-4705-997a-4a929a946654", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Lee publicaciones o informes de la entidad que ofrece el curso"}]}]}, "c5db3955-f703-457a-bce2-745f238e4b6d": {"id": "c5db3955-f703-457a-bce2-745f238e4b6d", "meta": {"align": "left", "depth": 0, "order": 1}, "type": "Paragraph", "value": [{"id": "ad64a4e2-1f7d-4f43-ad34-f6cbded87573", "type": "paragraph", "props": {"node_type": "block"}, "children": [{"text": "Características principales:"}]}]}, "c6c9450f-9b12-4131-80b0-5e5b44eb072a": {"id": "c6c9450f-9b12-4131-80b0-5e5b44eb072a", "meta": {"align": "left", "depth": 0, "order": 14}, "type": "BulletedList", "value": [{"id": "8271108e-2cf6-47fb-9731-38ea29b733ca", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Infórmate sobre la plana docente y su experiencia profesional"}]}]}, "ce9a27d3-0331-47eb-947e-b12ad82e2034": {"id": "ce9a27d3-0331-47eb-947e-b12ad82e2034", "meta": {"align": "left", "depth": 0, "order": 30}, "type": "BulletedList", "value": [{"id": "0eb4bd09-00ad-4030-8ae2-ba53deaed226", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Prepara un espacio libre de distracciones para el estudio"}]}]}, "cf2c19ec-46d2-4611-ae46-b54b53971b8c": {"id": "cf2c19ec-46d2-4611-ae46-b54b53971b8c", "meta": {"align": "left", "depth": 0, "order": 7}, "type": "BulletedList", "value": [{"id": "8ac73115-8c22-47a5-80bd-35fbf89b507d", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Incluyen procesos formales de selección con evaluaciones y entrevistas"}]}]}, "d0c807ce-5cdb-4673-835b-c3d74642a857": {"id": "d0c807ce-5cdb-4673-835b-c3d74642a857", "meta": {"align": "left", "depth": 0, "order": 55}, "type": "Paragraph", "value": [{"id": "3485c14d-9b0a-4e67-af47-9b695de3ab77", "type": "paragraph", "props": {"node_type": "block"}, "children": [{"text": "Los Cursos de Extensión Universitaria ofrecidos por entidades públicas como PROINVERSIÓN, BCRP, SBS, OSINERGMIN, OSIPTEL y SMV representan una valiosa oportunidad para adquirir conocimientos especializados y abrir puertas en el sector público peruano. Con las estrategias detalladas en este blog, estarás mejor preparado para enfrentar con éxito tanto el proceso de admisión como el desarrollo del programa."}]}]}, "da18d7dc-ba1b-421b-9afb-0e4daef1f637": {"id": "da18d7dc-ba1b-421b-9afb-0e4daef1f637", "meta": {"align": "left", "depth": 0, "order": 39}, "type": "BulletedList", "value": [{"id": "ed384e99-b76d-4e2a-b962-0a557c19ed68", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Identifica los temas centrales del curso"}]}]}, "dfe58d2e-9882-4ac6-902b-d61388bbc64c": {"id": "dfe58d2e-9882-4ac6-902b-d61388bbc64c", "meta": {"align": "left", "depth": 0, "order": 36}, "type": "HeadingTwo", "value": [{"id": "c512ab8f-d756-4473-a94b-e47ecc2555cb", "type": "heading-two", "props": {"node_type": "block"}, "children": [{"text": "4. Aprovechar los Recursos Disponibles"}]}]}, "ef661caa-5f36-47d5-8322-58ae33f6fbca": {"id": "ef661caa-5f36-47d5-8322-58ae33f6fbca", "meta": {"align": "left", "depth": 0, "order": 37}, "type": "Paragraph", "value": [{"id": "43432eef-9e59-4a71-89f0-d37b5750a707", "type": "paragraph", "props": {"node_type": "block"}, "children": [{"text": "Para aumentar tus posibilidades de selección y posterior aprovechamiento del Curso de Extensión Universitaria, es fundamental utilizar todos los recursos disponibles para reforzar tus conocimientos previos relacionados con la temática del programa."}]}]}, "f51a08a5-c68c-40ef-8180-0709a7281ce7": {"id": "f51a08a5-c68c-40ef-8180-0709a7281ce7", "meta": {"align": "left", "depth": 0, "order": 47}, "type": "Paragraph", "value": [{"id": "ed688c6d-268d-459b-bade-11d84503f6ec", "type": "paragraph", "props": {"node_type": "block"}, "children": [{"text": "¿Cómo implementarla?"}]}]}, "f536d4a5-bf18-44a2-ad7e-39755cc44962": {"id": "f536d4a5-bf18-44a2-ad7e-39755cc44962", "meta": {"align": "left", "depth": 0, "order": 23}, "type": "BulletedList", "value": [{"id": "2ce7f995-ab9f-4cbc-8cf8-8eb07148c387", "type": "bulleted-list", "props": {"node_type": "block"}, "children": [{"text": "Organiza un calendario detallado para cumplir con todas las actividades del programa"}]}]}, "f89bdf3e-6f62-434d-bc8e-8ab47a9113b7": {"id": "f89bdf3e-6f62-434d-bc8e-8ab47a9113b7", "meta": {"align": "left", "depth": 0, "order": 19}, "type": "Paragraph", "value": [{"id": "57c84258-295d-4d2c-b311-e0de1f13d3bc", "type": "paragraph", "props": {"node_type": "block"}, "children": [{"text": "Los Cursos de Extensión Universitaria suelen ser programas intensivos que requieren dedicación y compromiso. Organizar anticipadamente tu tiempo y asegurar tu disponibilidad para cumplir con el programa completo es esencial para tu éxito."}]}]}}	6	published	2025-06-07 01:03:08.584223+00	f	\N	\N	\N	\N	728	5427bc53-12b6-4de5-a87e-d95fc0d54153	0caeccd9-e704-4b48-a3d0-05ab8ff47d4c	\N	c8e086a4-69b9-4ffb-89ff-d8dfffd613a0
\.


--
-- Data for Name: core_blogpost_authors; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_blogpost_authors (id, blogpost_id, instructor_id) FROM stdin;
1	1117fa45-1635-4e99-9a66-94630b1c254f	4dba9bfd-b37f-410b-84c8-bbcdaf742498
\.


--
-- Data for Name: core_blogpost_categories; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_blogpost_categories (id, blogpost_id, blogcategory_id) FROM stdin;
1	1117fa45-1635-4e99-9a66-94630b1c254f	9fd97e8d-7968-4ea3-8d7e-2a4b90ab89f6
\.


--
-- Data for Name: core_blogpost_tags; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_blogpost_tags (id, blogpost_id, blogtag_id) FROM stdin;
1	1117fa45-1635-4e99-9a66-94630b1c254f	a6815414-8124-4f3a-a637-f03a955476a3
\.


--
-- Data for Name: core_blogtag; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_blogtag (created_at, updated_at, deleted, deleted_at, btid, name, slug, badge_color, description, deleted_by_id) FROM stdin;
2025-06-07 01:02:02.964424+00	2025-06-07 01:02:02.964446+00	f	\N	a6815414-8124-4f3a-a637-f03a955476a3	Desarrollo Profesional	desarrollo-profesional	#b6ad03	\N	\N
\.


--
-- Data for Name: core_educationalinstitution; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_educationalinstitution (created_at, updated_at, deleted, deleted_at, eiid, name, country, region, city, acronym, institution_type, deleted_by_id) FROM stdin;
\.


--
-- Data for Name: core_enrollment; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_enrollment (created_at, updated_at, deleted, deleted_at, eid, enrollment_type, deleted_by_id, offering_id, order_id, user_id) FROM stdin;
\.


--
-- Data for Name: core_event; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_event (created_at, updated_at, deleted, deleted_at, name, description, modality, type, price, deleted_by_id, thumbnail_id, instructor_id, offering_id, cover_image_id, eid, location) FROM stdin;
\.


--
-- Data for Name: core_eventreminder; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_eventreminder (created_at, updated_at, deleted, deleted_at, rid, reminder_type, event_alliance_id, event_name, variables, send_at, status, deleted_by_id, template_id_id) FROM stdin;
\.


--
-- Data for Name: core_eventschedule; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_eventschedule (created_at, updated_at, deleted, deleted_at, esid, is_general, start_date, end_date, name, description, stage, modality, location, price, agenda, ext_event_id, ext_event_link, cover_image_id, deleted_by_id, event_id, instructor_id, thumbnail_id, ext_event_reference) FROM stdin;
\.


--
-- Data for Name: core_eventschedule_partnerships; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_eventschedule_partnerships (id, eventschedule_id, partnership_id) FROM stdin;
\.


--
-- Data for Name: core_eventscheduleenrollment; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_eventscheduleenrollment (id, created_at, updated_at, deleted, deleted_at, interests, diffusion_channel, has_contact, deleted_by_id, event_schedule_id, user_id) FROM stdin;
\.


--
-- Data for Name: core_file; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_file (created_at, updated_at, deleted, deleted_at, fid, name, description, bucket_name, object_name, is_private, width, height, deleted_by_id, is_used) FROM stdin;
2025-02-28 00:28:34.603111+00	2025-02-28 00:28:34.603125+00	f	\N	8288b70a-190f-4df6-b438-5f7c5cc468e6	BBVA-finanzas-personales-y-corporativas.webp		public	8288b70a-190f-4df6-b438-5f7c5cc468e6/BBVA-finanzas-personales-y-corporativas.webp	f	323	249	\N	t
2025-02-28 17:15:28.086839+00	2025-02-28 17:15:28.086851+00	f	\N	f7509c6e-78ea-45ee-be12-765e6e6761bc	Captura de pantalla 2025-02-28 121454.webp		public	f7509c6e-78ea-45ee-be12-765e6e6761bc/Captura de pantalla 2025-02-28 121454.webp	f	276	386	\N	t
2025-02-28 17:03:58.446463+00	2025-03-03 17:40:52.441563+00	t	2025-03-03 17:40:52.441435+00	54b03d18-f7d1-4c50-a49e-d3d61073a6a0	Captura de pantalla 2025-02-28 120334.webp		public	54b03d18-f7d1-4c50-a49e-d3d61073a6a0/Captura de pantalla 2025-02-28 120334.webp	f	276	386	84eae7f0-ec7d-4f33-abe0-8e0eb6cffff4	t
2025-03-03 22:57:23.883131+00	2025-03-03 22:57:30.333281+00	t	2025-03-03 22:57:30.333212+00	f58a2c72-3c85-4839-9e60-b9ae04d592a8	10 CONTACTO.webp		public	f58a2c72-3c85-4839-9e60-b9ae04d592a8/10 CONTACTO.webp	f	323	249	84eae7f0-ec7d-4f33-abe0-8e0eb6cffff4	t
2025-02-28 00:19:16.962913+00	2025-03-06 00:17:44.737094+00	t	2025-03-06 00:17:44.737026+00	ea875f41-9982-4b80-a985-1f0458c31d68	finanzas.webp		public	ea875f41-9982-4b80-a985-1f0458c31d68/finanzas.webp	f	323	249	7307e0aa-9cd7-44db-92c4-f833641606a5	t
2025-03-06 00:17:44.922888+00	2025-03-06 00:17:44.922902+00	f	\N	51a9a686-894c-428a-bbb8-af75a3f40541	cropped_shot_diverse_coworkers_working_together_boardroom_brainstorming2222222.webp		public	51a9a686-894c-428a-bbb8-af75a3f40541/cropped_shot_diverse_coworkers_working_together_boardroom_brainstorming2222222.webp	f	323	249	\N	t
2025-02-28 17:11:25.681236+00	2025-03-21 00:33:21.581739+00	t	2025-03-21 00:33:21.581652+00	1cb5883a-b751-4306-879c-88c9993abc4d	Alvaro Inti.webp		public	1cb5883a-b751-4306-879c-88c9993abc4d/Alvaro Inti.webp	f	276	386	7307e0aa-9cd7-44db-92c4-f833641606a5	t
2025-03-03 18:13:14.623914+00	2025-03-21 00:38:19.686302+00	t	2025-03-21 00:38:19.686214+00	495e5455-2b51-4e98-a7e6-d7e6549d3a35	POST PARA CAMPAÑA  (10).webp		public	495e5455-2b51-4e98-a7e6-d7e6549d3a35/POST PARA CAMPAÑA  (10).webp	f	276	386	7307e0aa-9cd7-44db-92c4-f833641606a5	t
2025-03-21 00:38:19.720824+00	2025-03-21 00:38:19.720841+00	f	\N	245fc385-2b1a-4e94-937e-7cd491cdcf8a	PROFESOR_NICK.webp		public	245fc385-2b1a-4e94-937e-7cd491cdcf8a/PROFESOR_NICK.webp	f	276	386	\N	t
2025-03-21 00:33:21.677571+00	2025-03-21 00:38:41.355682+00	t	2025-03-21 00:38:41.355616+00	51a442cf-e4b1-4f52-b442-109af08811ed	PROFESOR_1.webp		public	51a442cf-e4b1-4f52-b442-109af08811ed/PROFESOR_1.webp	f	276	386	7307e0aa-9cd7-44db-92c4-f833641606a5	t
2025-03-21 00:38:41.381865+00	2025-03-21 00:38:41.381881+00	f	\N	12eed3ca-01be-4ffa-93c6-882c46020f8a	PROFESOR_1.webp		public	12eed3ca-01be-4ffa-93c6-882c46020f8a/PROFESOR_1.webp	f	276	386	\N	t
2025-03-03 18:06:20.068671+00	2025-03-21 00:39:24.150492+00	t	2025-03-21 00:39:24.150429+00	2cf42dc6-715a-44d0-80a7-572754529b21	POST PARA CAMPAÑA  (9).webp		public	2cf42dc6-715a-44d0-80a7-572754529b21/POST PARA CAMPAÑA  (9).webp	f	276	386	7307e0aa-9cd7-44db-92c4-f833641606a5	t
2025-03-21 00:39:24.177459+00	2025-03-21 00:39:24.177469+00	f	\N	4df6f3c3-7f7c-4aa3-b72f-c42ee2f46b44	PROFESOR_EXON.webp		public	4df6f3c3-7f7c-4aa3-b72f-c42ee2f46b44/PROFESOR_EXON.webp	f	276	386	\N	t
2025-03-03 17:40:52.542501+00	2025-03-21 02:03:22.689272+00	t	2025-03-21 02:03:22.689206+00	06c6a36d-1e8d-4f2b-b878-b100a5e7b4d8	Post (10).webp		public	06c6a36d-1e8d-4f2b-b878-b100a5e7b4d8/Post (10).webp	f	276	386	7307e0aa-9cd7-44db-92c4-f833641606a5	t
2025-03-21 02:03:22.714471+00	2025-03-21 02:03:37.269354+00	t	2025-03-21 02:03:37.269266+00	7b9f66a0-a4d7-486e-b8ed-bd769ded8330	PROFESOR_GERARDO.webp		public	7b9f66a0-a4d7-486e-b8ed-bd769ded8330/PROFESOR_GERARDO.webp	f	276	386	7307e0aa-9cd7-44db-92c4-f833641606a5	t
2025-03-21 02:03:37.295068+00	2025-03-21 02:03:37.295077+00	f	\N	062e2e90-a0e1-4216-98d0-c6b9dfc73299	PROFESOR_GERARDO.webp		public	062e2e90-a0e1-4216-98d0-c6b9dfc73299/PROFESOR_GERARDO.webp	f	276	386	\N	t
2025-03-03 22:57:30.464452+00	2025-03-28 19:17:01.670537+00	t	2025-03-28 19:17:01.670406+00	c71bc1c9-b5d6-4bcc-a8ed-5e44ed23d70d	10 CONTACTO.webp		public	c71bc1c9-b5d6-4bcc-a8ed-5e44ed23d70d/10 CONTACTO.webp	f	323	249	84eae7f0-ec7d-4f33-abe0-8e0eb6cffff4	t
2025-03-28 19:17:02.195433+00	2025-03-28 19:17:02.195447+00	f	\N	e6076b99-7292-4c28-97db-b38aec78782b	young-friends-park (1).webp		public	e6076b99-7292-4c28-97db-b38aec78782b/young-friends-park (1).webp	f	323	249	\N	t
2025-03-29 00:00:12.772713+00	2025-03-29 00:00:12.772728+00	f	\N	d5835b11-5a2b-4d6f-a894-faa7b6cf4475	1 PORTADA.webp		public	d5835b11-5a2b-4d6f-a894-faa7b6cf4475/1 PORTADA.webp	f	323	249	\N	t
2025-04-08 23:48:17.403135+00	2025-04-08 23:51:35.239982+00	t	2025-04-08 23:51:35.239916+00	08c608af-bdbe-421f-929b-75d33c98f212	office-workers-using-finance-graphs_23-2150408682.webp		public	08c608af-bdbe-421f-929b-75d33c98f212/office-workers-using-finance-graphs_23-2150408682.webp	f	323	249	73899230-d542-4df9-ac5a-18d74e0d8578	t
2025-04-08 23:51:35.489197+00	2025-04-09 00:02:11.813231+00	t	2025-04-09 00:02:11.813163+00	7655e77e-a3e1-4f9b-ae3e-7e95eab6b854	colleagues-reading-using-laptop-study-session.webp		public	7655e77e-a3e1-4f9b-ae3e-7e95eab6b854/colleagues-reading-using-laptop-study-session.webp	f	323	249	73899230-d542-4df9-ac5a-18d74e0d8578	t
2025-04-09 00:02:12.063061+00	2025-04-09 00:02:14.180057+00	t	2025-04-09 00:02:14.179989+00	febd2fda-9e9c-4ae8-94de-38518976c24e	colleagues-reading-using-laptop-study-session.webp		public	febd2fda-9e9c-4ae8-94de-38518976c24e/colleagues-reading-using-laptop-study-session.webp	f	323	249	73899230-d542-4df9-ac5a-18d74e0d8578	t
2025-04-09 00:02:14.435492+00	2025-04-09 00:02:14.435508+00	f	\N	55d9fa98-aa2c-471c-86f8-5b951467f8a8	colleagues-reading-using-laptop-study-session.webp		public	55d9fa98-aa2c-471c-86f8-5b951467f8a8/colleagues-reading-using-laptop-study-session.webp	f	323	249	\N	t
2025-03-01 05:00:05.668872+00	2025-04-09 00:35:03.380868+00	t	2025-04-09 00:35:03.380766+00	93e8127f-c9ef-4d6d-922f-08e7e5ab53e1	working-girls-with-papers-table.webp		public	93e8127f-c9ef-4d6d-922f-08e7e5ab53e1/working-girls-with-papers-table.webp	f	323	249	73899230-d542-4df9-ac5a-18d74e0d8578	t
2025-04-09 00:35:03.677117+00	2025-04-09 00:35:03.677131+00	f	\N	a0f42cfa-4476-483a-98c2-54e4ec38db83	searching-data.webp		public	a0f42cfa-4476-483a-98c2-54e4ec38db83/searching-data.webp	f	323	249	\N	t
2025-05-16 21:30:44.071938+00	2025-05-16 21:31:52.017652+00	t	2025-05-16 21:31:52.017567+00	6f35bd14-7e3a-4ec2-be06-677e0ef24107	persona-de-negocios-mirando-graficos-de-finanzas (1).webp		public	6f35bd14-7e3a-4ec2-be06-677e0ef24107/persona-de-negocios-mirando-graficos-de-finanzas (1).webp	f	323	249	73899230-d542-4df9-ac5a-18d74e0d8578	t
2025-05-16 21:31:52.509383+00	2025-05-16 21:31:55.095053+00	t	2025-05-16 21:31:55.094977+00	c74a0352-04e9-443d-81b0-bca238ea18ab	persona-de-negocios-mirando-graficos-de-finanzas (1).webp		public	c74a0352-04e9-443d-81b0-bca238ea18ab/persona-de-negocios-mirando-graficos-de-finanzas (1).webp	f	323	249	73899230-d542-4df9-ac5a-18d74e0d8578	t
2025-05-16 21:31:55.586447+00	2025-05-16 21:32:39.295654+00	t	2025-05-16 21:32:39.295578+00	96526435-f1c3-4423-a42d-afe0ac4ebbb0	persona-de-negocios-mirando-graficos-de-finanzas (1).webp		public	96526435-f1c3-4423-a42d-afe0ac4ebbb0/persona-de-negocios-mirando-graficos-de-finanzas (1).webp	f	323	249	73899230-d542-4df9-ac5a-18d74e0d8578	t
2025-05-16 21:32:39.776584+00	2025-05-16 21:33:04.496394+00	t	2025-05-16 21:33:04.496299+00	5704013a-4fa4-465d-93eb-f0d24e043193	persona-de-negocios-mirando-graficos-de-finanzas (1).webp		public	5704013a-4fa4-465d-93eb-f0d24e043193/persona-de-negocios-mirando-graficos-de-finanzas (1).webp	f	323	249	73899230-d542-4df9-ac5a-18d74e0d8578	t
2025-05-16 21:33:04.998374+00	2025-05-16 21:33:04.998388+00	f	\N	aaccaf9d-bd5e-4800-818a-57a2bc1146c7	persona-de-negocios-mirando-graficos-de-finanzas (1).webp		public	aaccaf9d-bd5e-4800-818a-57a2bc1146c7/persona-de-negocios-mirando-graficos-de-finanzas (1).webp	f	323	249	\N	t
2025-05-18 01:18:57.063507+00	2025-05-18 01:18:58.090444+00	t	2025-05-18 01:18:58.090374+00	64bf76e2-494b-4e2e-8b05-b2f5f5b70e49	persona-de-negocios-mirando-graficos-de-finanzas.webp		public	64bf76e2-494b-4e2e-8b05-b2f5f5b70e49/persona-de-negocios-mirando-graficos-de-finanzas.webp	f	323	249	73899230-d542-4df9-ac5a-18d74e0d8578	t
2025-05-18 01:18:58.385329+00	2025-05-18 01:19:11.541594+00	t	2025-05-18 01:19:11.541526+00	9d06f1fa-c8ae-4de6-99e4-e7b9917211e8	persona-de-negocios-mirando-graficos-de-finanzas.webp		public	9d06f1fa-c8ae-4de6-99e4-e7b9917211e8/persona-de-negocios-mirando-graficos-de-finanzas.webp	f	323	249	73899230-d542-4df9-ac5a-18d74e0d8578	t
2025-05-18 01:19:11.890484+00	2025-05-18 01:19:42.099207+00	t	2025-05-18 01:19:42.099106+00	33ceb2e3-82e2-407e-9b14-5f55311dbcb3	experiencia-en-programacion-con-una-persona-que-trabaja-con-codigos-en-la-computadora.webp		public	33ceb2e3-82e2-407e-9b14-5f55311dbcb3/experiencia-en-programacion-con-una-persona-que-trabaja-con-codigos-en-la-computadora.webp	f	323	249	73899230-d542-4df9-ac5a-18d74e0d8578	t
2025-05-18 01:19:42.467344+00	2025-05-18 01:20:15.933679+00	t	2025-05-18 01:20:15.933609+00	20112824-6cf9-49a7-9509-2965a4fe9317	experiencia-en-programacion-con-una-persona-que-trabaja-con-codigos-en-la-computadora.webp		public	20112824-6cf9-49a7-9509-2965a4fe9317/experiencia-en-programacion-con-una-persona-que-trabaja-con-codigos-en-la-computadora.webp	f	323	249	73899230-d542-4df9-ac5a-18d74e0d8578	t
2025-05-18 01:20:16.276365+00	2025-05-18 01:20:46.691252+00	t	2025-05-18 01:20:46.691183+00	dcdb65d6-65c2-41c2-b353-106c6d1bf130	experiencia-en-programacion-con-una-persona-que-trabaja-con-codigos-en-la-computadora.webp		public	dcdb65d6-65c2-41c2-b353-106c6d1bf130/experiencia-en-programacion-con-una-persona-que-trabaja-con-codigos-en-la-computadora.webp	f	323	249	73899230-d542-4df9-ac5a-18d74e0d8578	t
2025-05-18 01:20:47.037336+00	2025-05-18 01:20:47.037351+00	f	\N	8446342e-71b6-415e-82c6-960c356f225b	experiencia-en-programacion-con-una-persona-que-trabaja-con-codigos-en-la-computadora.webp		public	8446342e-71b6-415e-82c6-960c356f225b/experiencia-en-programacion-con-una-persona-que-trabaja-con-codigos-en-la-computadora.webp	f	323	249	\N	t
2025-05-18 04:07:36.91609+00	2025-05-18 04:07:37.359653+00	t	2025-05-18 04:07:37.359585+00	2f61e8f0-d896-46bf-a0c3-fbd19d5ecc3b	oficinistas-que-usan-graficos-de-finanzas.webp		public	2f61e8f0-d896-46bf-a0c3-fbd19d5ecc3b/oficinistas-que-usan-graficos-de-finanzas.webp	f	323	249	73899230-d542-4df9-ac5a-18d74e0d8578	t
2025-05-18 04:07:37.834139+00	2025-05-18 04:08:08.750384+00	t	2025-05-18 04:08:08.750287+00	e00544bb-5557-4ff1-b551-c1cbde43bf78	oficinistas-que-usan-graficos-de-finanzas.webp		public	e00544bb-5557-4ff1-b551-c1cbde43bf78/oficinistas-que-usan-graficos-de-finanzas.webp	f	323	249	73899230-d542-4df9-ac5a-18d74e0d8578	t
2025-05-18 04:08:09.234172+00	2025-05-18 04:08:09.651743+00	t	2025-05-18 04:08:09.651675+00	229a605d-a0d1-49f6-ada4-1221fde35745	oficinistas-que-usan-graficos-de-finanzas.webp		public	229a605d-a0d1-49f6-ada4-1221fde35745/oficinistas-que-usan-graficos-de-finanzas.webp	f	323	249	73899230-d542-4df9-ac5a-18d74e0d8578	t
2025-05-18 04:08:10.122654+00	2025-05-18 04:08:10.122671+00	f	\N	2f5bdc19-c01a-48fa-9add-30a14bd2aff8	oficinistas-que-usan-graficos-de-finanzas.webp		public	2f5bdc19-c01a-48fa-9add-30a14bd2aff8/oficinistas-que-usan-graficos-de-finanzas.webp	f	323	249	\N	t
2025-05-21 20:56:25.798314+00	2025-05-21 20:56:26.605996+00	t	2025-05-21 20:56:26.605928+00	01d68a2c-42b0-4f7a-8d02-b414aa6d03b6	empresario-analizando-los-datos-del-mercado.webp		public	01d68a2c-42b0-4f7a-8d02-b414aa6d03b6/empresario-analizando-los-datos-del-mercado.webp	f	323	249	73899230-d542-4df9-ac5a-18d74e0d8578	t
2025-05-21 20:56:26.749921+00	2025-05-21 20:56:27.496249+00	t	2025-05-21 20:56:27.496166+00	8ff3a098-4a4d-4efa-ae05-acfcc0a057b2	empresario-analizando-los-datos-del-mercado.webp		public	8ff3a098-4a4d-4efa-ae05-acfcc0a057b2/empresario-analizando-los-datos-del-mercado.webp	f	323	249	73899230-d542-4df9-ac5a-18d74e0d8578	t
2025-05-21 20:56:27.646507+00	2025-05-21 20:56:27.646537+00	f	\N	dde6f88a-a62a-433b-a8d3-b341279db47d	empresario-analizando-los-datos-del-mercado.webp		public	dde6f88a-a62a-433b-a8d3-b341279db47d/empresario-analizando-los-datos-del-mercado.webp	f	323	249	\N	t
2025-06-07 00:53:04.625897+00	2025-06-07 00:53:08.57414+00	f	\N	c8e086a4-69b9-4ffb-89ff-d8dfffd613a0	Portada-Blog-1 (2).png	5 Estrategias Clave para Prepararte para los Cursos de Extensión Universitaria en Perú	public	c8e086a4-69b9-4ffb-89ff-d8dfffd613a0/Portada-Blog-1 (2).webp	f	600	400	\N	t
2025-06-07 00:53:29.401733+00	2025-06-07 02:00:40.950903+00	f	\N	5427bc53-12b6-4de5-a87e-d95fc0d54153	Portada-Blog-1 (2).png	5 Estrategias Clave para Prepararte para los Cursos de Extensión Universitaria en Perú	public	5427bc53-12b6-4de5-a87e-d95fc0d54153/Portada-Blog-1 (2).webp	f	0	0	\N	f
\.


--
-- Data for Name: core_instructor; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_instructor (created_at, updated_at, deleted, deleted_at, "order", iid, full_name, biography, title, highlighted_info, facebook_url, linkedin_url, instagram_url, status, deleted_by_id, profile_photo_id, user_id) FROM stdin;
2025-03-03 18:06:20.026286+00	2025-03-21 02:03:43.624266+00	f	\N	2	d5cf1d2d-e456-4fc4-ae07-4da7b44695ca	Exon Agusto Chauca A	Maestrista en Ingeniería Financiera en la Universidad Nacional de Ingeniería. Bachiller en Economía y Negocios Internacionales de la Universidad ESAN. Becario del XII Curso de Extensión en Finanzas Avanzadas del BCRP. Certificación Chartered Financial Analyst (CFA) - Nivel I. Certificación Financial Risk Manager (FRM) - Nivel II. Especialista en Inversiones y Riesgo Financieros. Actualmente labora  en Riesgo de Tesorería en mi Banco.	Msc(c) Ingeniero Financiero	Especialista en Inversiones y Riesgo Financieros. Actualmente labora  en Riesgo de Tesorería en mi Banco				Published	\N	4df6f3c3-7f7c-4aa3-b72f-c42ee2f46b44	\N
2025-03-03 18:13:14.575207+00	2025-03-21 02:03:50.960548+00	f	\N	3	8922a180-9c57-4729-8d34-33c8237147d0	Nick Ower Chavez M	Bachiller en Ingeniería Económica por la Universidad Nacional de Ingeniería. Becario del XII Curso de Extensión Universitaria en Finanzas Avanzadas del BCRP. Especializado en Riesgos Financieros. Actualmente labora como Coordinador de Riesgos en Alfin Banco.	Bsc. Ingeniería Económica	Coordinador de Riesgos en Alfin Banco				Published	\N	245fc385-2b1a-4e94-937e-7cd491cdcf8a	\N
2025-02-28 17:11:25.398452+00	2025-03-21 02:04:01.531728+00	f	\N	4	6200e0bc-fa2f-4a00-8f57-553160328063	Alvaro Inti lobato	Maestrista en Ingeniería Financiera de la Universidad Nacional de Ingeniería. Licenciado en Administración de Negocios Internacionales por la Universidad Nacional Mayor de San Marcos.\r\nSegundo puesto del XXI Programa de Especialización en Mercado de Valores (SMV). Especializado en Finanzas corporativas y Mercado de Capitales. Actualmente labora en la Superintendencia de Mercado de Valores.	Msc(c) Ingeniería Financiera	Especializado en Finanzas corporativas y Mercado de Capitales. Actualmente labora en la Superintendencia de Mercado de Valores.				Published	\N	12eed3ca-01be-4ffa-93c6-882c46020f8a	\N
2025-02-28 17:15:28.063055+00	2025-03-21 02:04:06.831307+00	f	\N	5	3e58fcfc-1ca7-4bd3-9045-da58f792fa11	Sofia Antaurco Paucar	Maestrista en Comercio Internacional y Aduana en la Universidad Nacional Mayor de San Marcos. Licenciada en Administración de Negocios Internacionales en la Universidad Nacional Mayor de San Marcos. Laborando mas de 5 años en aduanas y comercio internacional. Actualmente laborando como consultora independiente de Negocios	Msc(c) Maestrista en Comercio Internacional y Aduana	Laborando mas de 5 años en aduanas y comercio internacional.				Published	\N	f7509c6e-78ea-45ee-be12-765e6e6761bc	\N
2025-02-28 17:03:58.422218+00	2025-03-21 02:05:19.818908+00	f	\N	1	4dba9bfd-b37f-410b-84c8-bbcdaf742498	Gerardo Inti Lobato	Maestrista de Econometría Bancaria y Financiera en la UNI. Bachiller en Ingeniería Económica por la UNI y Administrador de Negocios Internacionales por UNMSM. Primer Puesto del XXI CEU en Regulación Económica del OSIPTEL y Primer Puesto del XIX Programa de Especialización de la Superintendencia del Mercado de Valores (SMV). Becario del XII Curso de Especialización en Finanzas Avanzadas del BCRP y Becario del LXVII Curso de Especialización en Economía Avanzadas del BCRP. Especialista en Riesgos e Inversiones. - Actualmente labora como Ingeniero Financiero en el BСР.	Msc(c) Econometría Bancaria y Financiera	Becario del XII Curso de Especialización en Finanzas Avanzadas del BCRP y Becario del LXVII Curso de Especialización en Economía Avanzadas del BCRP.				Published	\N	062e2e90-a0e1-4216-98d0-c6b9dfc73299	\N
\.


--
-- Data for Name: core_leadsource; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_leadsource (created_at, updated_at, deleted, deleted_at, lsid, name, description, deleted_by_id) FROM stdin;
\.


--
-- Data for Name: core_major; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_major (created_at, updated_at, deleted, deleted_at, mid, name, deleted_by_id) FROM stdin;
\.


--
-- Data for Name: core_modulecourse; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_modulecourse (created_at, updated_at, deleted, deleted_at, mcid, title, deleted_by_id, module_id) FROM stdin;
2025-02-28 00:02:42.583756+00	2025-02-28 00:06:12.764539+00	f	\N	6e6ab76d-2e5b-4af7-9cc4-918c3e17da27	 Introducción a los Mercados Financieros	\N	0952572e-fbec-44b5-821c-f2dd209705ef
2025-02-28 00:02:42.589437+00	2025-02-28 00:06:12.768822+00	f	\N	bd526af2-16e7-4840-9f00-fc44894530f9	Estructura del Mercado de Valores	\N	0952572e-fbec-44b5-821c-f2dd209705ef
2025-02-28 00:02:42.593153+00	2025-02-28 00:06:12.772032+00	f	\N	2f3be383-b65a-408d-b02d-1d8fb0e50b20	Legislación y Regulación del Mercado de Valores	\N	0952572e-fbec-44b5-821c-f2dd209705ef
2025-02-28 00:02:42.59611+00	2025-02-28 00:06:12.775036+00	f	\N	3a62f01b-8f1e-4be1-84d6-3e1a6d4db6a5	 Herramientas Financieras	\N	0952572e-fbec-44b5-821c-f2dd209705ef
2025-02-28 00:02:42.600717+00	2025-02-28 00:06:12.778844+00	f	\N	2cc69436-cd8c-400e-af0f-552fae24749a	Instrumentos financieros y mercados	\N	0952572e-fbec-44b5-821c-f2dd209705ef
2025-02-28 00:02:42.604142+00	2025-02-28 00:06:12.781817+00	f	\N	a798ee80-2b44-451d-b97a-70dbe56e8b19	Análisis de Inversiones	\N	0952572e-fbec-44b5-821c-f2dd209705ef
2025-02-28 00:02:42.607035+00	2025-02-28 00:06:12.784786+00	f	\N	e304f94b-8c04-427b-bbb9-aab5667ea975	Teoría de Portafolios y Modelos Cuantitativos	\N	0952572e-fbec-44b5-821c-f2dd209705ef
2025-02-28 00:02:42.611311+00	2025-02-28 00:06:12.7895+00	f	\N	8d1b5988-223e-4101-892a-9d9173031df3	Mercado Primario: Conceptos y Funcionamiento	\N	c8692ed2-3e81-460b-86ac-6a68d505582e
2025-02-28 00:02:42.614193+00	2025-02-28 00:06:12.792706+00	f	\N	156096de-b4ec-418e-b8b7-527520b31316	 Legislación del Mercado Primario	\N	c8692ed2-3e81-460b-86ac-6a68d505582e
2025-02-28 00:02:42.617202+00	2025-02-28 00:06:12.796013+00	f	\N	ad8de2cc-aef1-408a-b4f8-0095ae212233	 Estructuración de Deuda y Capital en Mercado de Valores	\N	c8692ed2-3e81-460b-86ac-6a68d505582e
2025-02-28 00:02:42.619913+00	2025-02-28 00:06:12.799363+00	f	\N	e19b913e-89e1-4b4b-b3be-f045c1499d76	Registro Público del Mercado de Valores y Supervisión	\N	c8692ed2-3e81-460b-86ac-6a68d505582e
2025-02-28 00:02:42.622695+00	2025-02-28 00:06:12.8027+00	f	\N	9086f83d-2a0c-4990-a69e-6e38ea98c08e	Calificación de Riesgo y Empresas de Valorización	\N	c8692ed2-3e81-460b-86ac-6a68d505582e
2025-02-28 00:02:42.625563+00	2025-02-28 00:06:12.805749+00	f	\N	bc273305-2e13-45e5-9fa6-fe595e97865d	 Innovaciones en Mercados de Emisión	\N	c8692ed2-3e81-460b-86ac-6a68d505582e
2025-02-28 00:02:42.629975+00	2025-02-28 00:06:12.810296+00	f	\N	81e949d8-1ac1-432e-9212-f056f7aa19e8	 Infraestructura del Mercado Secundario	\N	e60e24df-a931-4dd1-a633-47f09b283daf
2025-02-28 00:02:42.632825+00	2025-02-28 00:06:12.813182+00	f	\N	48e34863-cfdd-48db-96de-983c5287890a	Regulación del Mercado Secundario	\N	e60e24df-a931-4dd1-a633-47f09b283daf
2025-02-28 00:02:42.635542+00	2025-02-28 00:06:12.815936+00	f	\N	07b83762-3c47-46a2-9212-4f8224756e97	Operaciones Bursátiles y Trading	\N	e60e24df-a931-4dd1-a633-47f09b283daf
2025-02-28 00:02:42.638314+00	2025-02-28 00:06:12.818804+00	f	\N	f1950eb6-b2a2-4dd6-b09c-e58270d13edb	 Gestión y Supervisión de Fondos	\N	e60e24df-a931-4dd1-a633-47f09b283daf
2025-02-28 00:02:42.641259+00	2025-02-28 00:06:12.821754+00	f	\N	6c3801bb-e79c-4a1d-bd13-109d667ff620	Financiamiento Alternativo y Crowdfunding	\N	e60e24df-a931-4dd1-a633-47f09b283daf
2025-02-28 00:02:42.644213+00	2025-02-28 00:06:12.824544+00	f	\N	e0078787-1881-491c-8200-e2da17e35c3f	Inversiones Alternativas y Derivados	\N	e60e24df-a931-4dd1-a633-47f09b283daf
2025-02-28 00:02:42.64859+00	2025-02-28 00:06:12.829237+00	f	\N	7e751af6-806d-4a9d-aa34-a7ac8408a91d	Gestión de Portafolios y Estrategias de Inversión	\N	8084c4cf-b533-485b-9ee0-80e9fe76ed9c
2025-02-28 00:02:42.651445+00	2025-02-28 00:06:12.832076+00	f	\N	82704b36-ff13-43fe-820c-a394a25cd547	Gestión de Riesgos en Mercado de Valores	\N	8084c4cf-b533-485b-9ee0-80e9fe76ed9c
2025-02-28 00:02:42.654108+00	2025-02-28 00:06:12.834792+00	f	\N	ac06058b-5e27-46cb-940c-f51f83aef834	ESG y Finanzas Sostenibles	\N	8084c4cf-b533-485b-9ee0-80e9fe76ed9c
2025-02-28 00:02:42.656704+00	2025-02-28 00:06:12.837607+00	f	\N	f30907c0-64c4-46d8-9b01-5f16416faaf1	Regulación y Compliance en Inversiones	\N	8084c4cf-b533-485b-9ee0-80e9fe76ed9c
2025-02-28 00:02:42.65954+00	2025-02-28 00:06:12.840368+00	f	\N	b735a94b-c623-4665-907e-741b18a86008	Fintech y Digitalización de los Mercados Financieros	\N	8084c4cf-b533-485b-9ee0-80e9fe76ed9c
2025-02-28 00:02:42.662302+00	2025-02-28 00:06:12.843155+00	f	\N	54823bd6-2d1d-46d0-a6e0-ff71a1f19ffa	Taller de Bolsa y Simulación de Inversiones	\N	8084c4cf-b533-485b-9ee0-80e9fe76ed9c
2025-04-08 23:37:03.923737+00	2025-04-08 23:37:03.923746+00	f	\N	986e3c29-3d5f-4c32-aa07-b5146f69c795	1. Microeconomía Intermedia 	\N	e0649540-1293-4d00-a576-893e4305c308
2025-04-08 23:37:03.928731+00	2025-04-08 23:37:03.928739+00	f	\N	4ad823e1-9974-4ac5-b351-8a5a334ac410	2. Microeconomía Avanzada 	\N	e0649540-1293-4d00-a576-893e4305c308
2025-04-08 23:37:03.936937+00	2025-04-08 23:37:03.936949+00	f	\N	5a627ec0-bf33-4193-9f46-27efaaaed0a6	1. Macroeconomía Intermedia 	\N	8bfd62f3-1dcc-4141-bce7-791188497173
2025-04-08 23:37:03.943919+00	2025-04-08 23:37:03.94393+00	f	\N	61d19e09-6b9e-4737-a678-a360756109ed	2. Macroeconomía Avanzada 	\N	8bfd62f3-1dcc-4141-bce7-791188497173
2025-04-08 23:37:03.951783+00	2025-04-08 23:37:03.951791+00	f	\N	65801111-e840-49a7-bdb5-3e3ce6575caf	1. Finanzas Intermedia 	\N	8e6a08b8-4be3-48f3-84a3-ab1602bd196b
2025-04-08 23:37:03.957691+00	2025-04-08 23:37:03.957699+00	f	\N	415d2ba9-789c-4e20-9f9b-4a83c67e9569	2. Finanzas Avanzada 	\N	8e6a08b8-4be3-48f3-84a3-ab1602bd196b
2025-04-08 23:37:03.965149+00	2025-04-08 23:37:03.965157+00	f	\N	7f407d20-3d01-48c7-ba1d-0e4c0dfe4aa1	1. Matemática 	\N	b3b867a3-c066-409f-b2a9-589a7a6b4640
2025-04-08 23:37:03.968099+00	2025-04-08 23:37:03.968106+00	f	\N	81238da0-13ab-4cd8-9519-9393b5ef8edb	2. Estadística	\N	b3b867a3-c066-409f-b2a9-589a7a6b4640
2025-04-08 23:37:03.971089+00	2025-04-08 23:37:03.971097+00	f	\N	78393d54-6641-44b0-ac7d-5f0248cd72b9	3. Econometría 	\N	b3b867a3-c066-409f-b2a9-589a7a6b4640
2025-04-08 23:37:03.975369+00	2025-04-08 23:37:03.97538+00	f	\N	6faa1446-9f04-44fc-a192-d7aa754ff0d9	Modelos de Series de tiempo	\N	b3b867a3-c066-409f-b2a9-589a7a6b4640
2025-05-18 04:03:51.395349+00	2025-05-18 04:12:50.089821+00	f	\N	710b6f7b-9b40-4120-bda0-b5de474f6972	Mercados Financieros	\N	ac632844-63b1-453b-a640-ae1db8366944
2025-05-18 04:03:51.399404+00	2025-05-18 04:12:50.093351+00	f	\N	6d7fa26d-66ba-4449-aebb-dbf4a78b7e77	Herramientas Financieras	\N	ac632844-63b1-453b-a640-ae1db8366944
2025-02-27 23:53:18.369467+00	2025-03-06 17:43:29.008634+00	f	\N	ce0ce6f5-7dc6-4ade-b966-f3fbcbddea1d	Gestión Integral de Riesgos (Frameworks y Principios)	\N	713d38ee-278b-4df1-81aa-ad6c9d2835da
2025-02-27 23:53:18.373927+00	2025-03-06 17:43:29.012179+00	f	\N	a04b27fc-8235-498e-9ec4-8a1e2e861808	Estadística Financiera Aplicada	\N	713d38ee-278b-4df1-81aa-ad6c9d2835da
2025-02-27 23:53:18.377426+00	2025-03-06 17:43:29.016054+00	f	\N	8c6cc7a6-cb05-4be8-a1aa-e58316d72405	Econometría Financiera	\N	713d38ee-278b-4df1-81aa-ad6c9d2835da
2025-02-27 23:53:18.380795+00	2025-03-06 17:43:29.021498+00	f	\N	79a8441b-a4c1-45c9-807b-d82781fca5bd	Factores de Riesgo	\N	713d38ee-278b-4df1-81aa-ad6c9d2835da
2025-02-27 23:53:18.383475+00	2025-03-06 17:43:29.024754+00	f	\N	665ef86a-478f-4aed-8aad-f677ead738e1	Valorización de Instrumentos Financieros	\N	713d38ee-278b-4df1-81aa-ad6c9d2835da
2025-02-27 23:53:18.386839+00	2025-03-06 17:43:29.02903+00	f	\N	c35f91e2-eb0c-47ba-8c37-a62bb5bd021a	Métricas de Riesgo	\N	713d38ee-278b-4df1-81aa-ad6c9d2835da
2025-02-27 23:53:18.391176+00	2025-03-06 17:43:29.034445+00	f	\N	79ca9212-534b-43c6-9b43-a65e9f65a600	Regulación y Reportería en Riesgo de Mercado	\N	48cc2cfc-2c29-4327-8d23-717861c835a2
2025-02-27 23:53:18.393888+00	2025-03-06 17:43:29.037789+00	f	\N	4e37feac-b5a1-4358-a776-4bb831528e5b	Estimación del VaR en Instrumentos Financieros	\N	48cc2cfc-2c29-4327-8d23-717861c835a2
2025-02-27 23:53:18.398197+00	2025-03-06 17:43:29.041233+00	f	\N	876ea1bf-18b1-4363-8ec3-1e1ead4c1ff1	Modelos de Tasas de Interés 	\N	48cc2cfc-2c29-4327-8d23-717861c835a2
2025-02-27 23:53:18.400964+00	2025-03-06 17:43:29.04472+00	f	\N	b718b1ac-0631-4e1b-8a94-d270cb645ab9	Backtesting y Stress Testing en Riesgo de Mercado 	\N	48cc2cfc-2c29-4327-8d23-717861c835a2
2025-02-27 23:53:18.405458+00	2025-03-06 17:43:29.05005+00	f	\N	b8345614-36e5-472a-83fd-35a9a6f0f180	Regulación y Reportería en Riesgo de Crédito	\N	6f54cd90-1b03-4ab1-8dd6-d7bb6041d2ee
2025-02-27 23:53:18.408729+00	2025-03-06 17:43:29.053349+00	f	\N	e8a7e344-5e13-4884-8523-ce67e9cb01df	Modelos Scoring (PD, LGD, EAD)	\N	6f54cd90-1b03-4ab1-8dd6-d7bb6041d2ee
2025-02-27 23:53:18.411445+00	2025-03-06 17:43:29.056627+00	f	\N	e6fe6072-8dac-4c07-975a-c6cf7be534b3	Machine Learning para Riesgo de Crédito	\N	6f54cd90-1b03-4ab1-8dd6-d7bb6041d2ee
2025-02-27 23:53:18.415201+00	2025-03-06 17:43:29.060863+00	f	\N	82942d7e-f609-47f1-a84c-bba029ee3dfc	Backtesting y Stress Testing en Riesgo de Crédito	\N	6f54cd90-1b03-4ab1-8dd6-d7bb6041d2ee
2025-02-27 23:53:18.418004+00	2025-03-06 17:43:29.064566+00	f	\N	c32ed0fa-3342-4b2a-a287-c3a297136df8	Modelos Estructurales y de Rating 	\N	6f54cd90-1b03-4ab1-8dd6-d7bb6041d2ee
2025-02-27 23:53:18.422913+00	2025-03-06 17:43:29.071738+00	f	\N	fc9d8ad9-c631-447d-ba77-e5a919540f44	Regulación y Reportería en Riesgo de Liquidez	\N	ec0ebb80-75ca-4489-a09f-b4fd6bf365bc
2025-02-27 23:53:18.42557+00	2025-03-06 17:43:29.075173+00	f	\N	8919f2d8-c525-4d65-b8eb-7f924f77fd08	Modelos Cuantitativos en Riesgo de Liquidez	\N	ec0ebb80-75ca-4489-a09f-b4fd6bf365bc
2025-02-27 23:53:18.428398+00	2025-03-06 17:43:29.078385+00	f	\N	f8afd76e-4c0e-424a-b250-888f5880ba96	Gestión de Activos y Pasivos (ALM)	\N	ec0ebb80-75ca-4489-a09f-b4fd6bf365bc
2025-02-27 23:53:18.430933+00	2025-03-06 17:43:29.081545+00	f	\N	25637ea5-d676-4631-9d02-a8cead99e398	Riesgo de Liquidez de Mercado	\N	ec0ebb80-75ca-4489-a09f-b4fd6bf365bc
2025-02-27 23:53:18.433439+00	2025-03-06 17:43:29.084824+00	f	\N	10ebd51f-581a-4699-a6dc-f0ad58981882	Gestión de Riesgo de Operativo	\N	ec0ebb80-75ca-4489-a09f-b4fd6bf365bc
2025-02-27 23:53:18.437603+00	2025-03-06 17:43:29.089505+00	f	\N	9af7ba51-7a98-4b01-9fd6-11b541967355	Regulación y Reportería en Riesgo Operacional	\N	0900e8ff-91e1-457a-992e-32e47421b6bc
2025-02-27 23:53:18.440596+00	2025-03-06 17:43:29.092579+00	f	\N	b5eaf4f0-72ab-4132-891b-12b7b69838f5	Identificación de Eventos de Riesgo Operacional	\N	0900e8ff-91e1-457a-992e-32e47421b6bc
2025-02-27 23:53:18.443444+00	2025-03-06 17:43:29.095632+00	f	\N	23a6b1e9-e64c-4836-ac85-fc385af85ed2	Modelos Cualitativos de Riesgo Operacional	\N	0900e8ff-91e1-457a-992e-32e47421b6bc
2025-02-27 23:53:18.4462+00	2025-03-06 17:43:29.098663+00	f	\N	1e4d82ae-2796-421a-b4d3-d30b4cdbb467	Modelos Cuantitativos de Riesgo Operacional	\N	0900e8ff-91e1-457a-992e-32e47421b6bc
2025-02-27 23:53:18.449499+00	2025-03-06 17:43:29.102251+00	f	\N	3cc49191-84f1-407f-8c41-2337a8f73225	Gestión de Riesgos Emergentes en RO	\N	0900e8ff-91e1-457a-992e-32e47421b6bc
2025-05-18 04:03:51.402834+00	2025-05-18 04:12:50.096711+00	f	\N	624daccd-d2b6-4606-938f-69be0fcfb4e3	Fundamentos de Banca	\N	ac632844-63b1-453b-a640-ae1db8366944
2025-05-18 04:03:51.410171+00	2025-05-18 04:12:50.104938+00	f	\N	fbbe4664-41cd-4bcc-a338-cfb7bacfe426	Instrumentos Financieros	\N	ac632844-63b1-453b-a640-ae1db8366944
2025-05-18 04:03:51.41385+00	2025-05-18 04:12:50.108928+00	f	\N	dff200ec-00a1-4216-9384-e6aa666e9d47	Econometría Financiera	\N	ac632844-63b1-453b-a640-ae1db8366944
2025-05-18 04:03:51.416971+00	2025-05-18 04:12:50.11223+00	f	\N	e1da0abe-d410-4987-a99d-b3503e594846	Ingenieria Financiera	\N	ac632844-63b1-453b-a640-ae1db8366944
2025-03-28 23:54:51.188094+00	2025-05-16 21:34:45.472538+00	f	\N	db0cf6de-2c1b-4cff-85b0-fb6e2f3e0e5e	1. Matemática Financiera	\N	352c8079-5e0a-4335-88ee-533ddbe3b440
2025-03-28 23:54:51.191401+00	2025-05-16 21:34:45.477197+00	f	\N	76a273e0-487c-4e6b-9de0-0d0e627ceeb2	2. Análisis de Estados Financieros	\N	352c8079-5e0a-4335-88ee-533ddbe3b440
2025-03-28 23:54:51.19446+00	2025-05-16 21:34:45.480979+00	f	\N	966dfe06-9936-4296-93bd-5094423aec67	3. Finanzas Corporativas	\N	352c8079-5e0a-4335-88ee-533ddbe3b440
2025-02-28 16:46:46.167617+00	2025-02-28 16:46:46.167626+00	f	\N	dbad2cf4-18ba-4af9-b183-33ca10ff0ac2	Análisis Matemático y Algebra Lineal	\N	c6a26fb0-9e7f-4cc8-a4dc-1bfb7fdc86d6
2025-02-28 16:46:46.171384+00	2025-02-28 16:46:46.171395+00	f	\N	55a006c0-d0a5-4f8e-87e7-a24d7d4822be	 Estadística Descriptiva e Inferencial	\N	c6a26fb0-9e7f-4cc8-a4dc-1bfb7fdc86d6
2025-02-28 16:46:46.178457+00	2025-02-28 16:46:46.178465+00	f	\N	dfe6a620-c81e-4723-a35d-22754737ce6f	Economía General	\N	390f646b-0bc5-4b13-a931-0dcd3faaec63
2025-02-28 16:46:46.181864+00	2025-02-28 16:46:46.181873+00	f	\N	521a9dee-5110-442a-9a72-e4324a1190ff	Microeconomía	\N	390f646b-0bc5-4b13-a931-0dcd3faaec63
2025-02-28 16:46:46.187647+00	2025-02-28 16:46:46.187655+00	f	\N	ccb813a5-3a99-403e-8b45-d95d5f322ecd	Macroeconomía	\N	390f646b-0bc5-4b13-a931-0dcd3faaec63
2025-02-28 16:46:46.19303+00	2025-02-28 16:46:46.193038+00	f	\N	84601331-8b68-44bd-b573-5ac2a7411452	Econometría Básica	\N	390f646b-0bc5-4b13-a931-0dcd3faaec63
2025-02-28 16:46:46.196847+00	2025-02-28 16:46:46.196856+00	f	\N	3d389381-9829-4c80-98d9-00ee76576088	Administración General	\N	2b380963-d44e-4770-b012-55ebb460b7d7
2025-02-28 16:46:46.200188+00	2025-02-28 16:46:46.200196+00	f	\N	be5be351-556e-4d5e-924d-abf7b5ee445c	 Procesos Administrativos	\N	2b380963-d44e-4770-b012-55ebb460b7d7
2025-02-28 16:46:46.20448+00	2025-02-28 16:46:46.204503+00	f	\N	e280910c-d464-4b0b-aab9-a8b15104e778	Planeamiento Empresarial	\N	2b380963-d44e-4770-b012-55ebb460b7d7
2025-02-28 16:46:46.207649+00	2025-02-28 16:46:46.207658+00	f	\N	02b9b6a0-bf72-4887-a897-5381d96887fb	Gestión de Empresas y Emprendimiento	\N	2b380963-d44e-4770-b012-55ebb460b7d7
2025-02-28 16:46:46.211231+00	2025-02-28 16:46:46.211238+00	f	\N	ff77038c-9c45-493e-8ceb-7d3d9fe3326a	Fundamentos de Contabilidad	\N	2b380963-d44e-4770-b012-55ebb460b7d7
2025-02-28 16:46:46.215294+00	2025-02-28 16:46:46.215302+00	f	\N	34a6fd26-b390-40df-92fd-96056de36c79	Costos y Presupuestos	\N	2b380963-d44e-4770-b012-55ebb460b7d7
2025-02-28 16:46:46.218635+00	2025-02-28 16:46:46.218642+00	f	\N	4126e870-1a93-4d64-8cb0-769d06c1eadd	Matemática Financiera	\N	2b380963-d44e-4770-b012-55ebb460b7d7
2025-02-28 16:46:46.221934+00	2025-02-28 16:46:46.221943+00	f	\N	f94a3416-2d19-4110-983e-e62b0047c40b	Finanzas Corporativas	\N	2b380963-d44e-4770-b012-55ebb460b7d7
2025-03-28 23:54:51.197567+00	2025-05-16 21:34:45.484418+00	f	\N	1f2b6af8-6002-4f25-9ac3-0f668e1f1221	4. Productos Financieros	\N	352c8079-5e0a-4335-88ee-533ddbe3b440
2025-03-03 23:10:05.046148+00	2025-03-03 23:10:05.046156+00	f	\N	54b12722-f668-43d4-97b7-7658cb10caf2	Microeconomía Intermedia 	\N	0aaf0d3f-68ca-44dc-96a2-1b31614b4625
2025-03-03 23:10:05.050998+00	2025-03-03 23:10:05.051005+00	f	\N	4def8daf-4f8d-4efb-9986-17d650b583b9	 Microeconomía Avanzada 	\N	0aaf0d3f-68ca-44dc-96a2-1b31614b4625
2025-03-03 23:10:05.057629+00	2025-03-03 23:10:05.057636+00	f	\N	d6106b57-a177-41fe-90df-4c8e31b96875	 Macroeconomía Intermedia	\N	a869c806-2ac8-422d-8a2c-35ea1b7a61f3
2025-03-03 23:10:05.06299+00	2025-03-03 23:10:05.062997+00	f	\N	2aaa6c7e-d216-47d6-b48d-281ce08e3e7a	Macroeconomía Avanzada 	\N	a869c806-2ac8-422d-8a2c-35ea1b7a61f3
2025-03-03 23:10:05.069493+00	2025-03-03 23:10:05.069501+00	f	\N	5fc3be80-7500-4628-a75d-315b133f44c8	Finanzas Intermedia 	\N	b0a76e72-7b57-44e4-8e76-3c0560581c09
2025-03-03 23:10:05.07473+00	2025-03-03 23:10:05.074737+00	f	\N	0580d4be-5a3c-4dac-a411-8af4e8ede5bb	Finanzas Avanzada 	\N	b0a76e72-7b57-44e4-8e76-3c0560581c09
2025-03-03 23:10:05.081818+00	2025-03-03 23:10:05.081826+00	f	\N	65eff931-d382-4748-a7f3-6323094cf656	Matemática	\N	ebb9614f-9ea1-4ef5-8047-46896ee52eaa
2025-03-03 23:10:05.08454+00	2025-03-03 23:10:05.084549+00	f	\N	27fadcdd-9e29-4663-ad23-15cbdd28f496	Estadística	\N	ebb9614f-9ea1-4ef5-8047-46896ee52eaa
2025-03-03 23:10:05.0872+00	2025-03-03 23:10:05.087208+00	f	\N	309ac93f-c233-4885-be40-313bcb23fcee	Econometría 	\N	ebb9614f-9ea1-4ef5-8047-46896ee52eaa
2025-03-03 23:10:05.091187+00	2025-03-03 23:10:05.091196+00	f	\N	bfc77aa5-f1e3-493a-84d1-3fb3c3b1f588	Series de Tiempo	\N	ebb9614f-9ea1-4ef5-8047-46896ee52eaa
2025-03-28 23:54:51.200865+00	2025-05-16 21:34:45.488266+00	f	\N	25de881c-128b-4e18-ba5b-da740e361f48	5. Proyectos de Inversión	\N	352c8079-5e0a-4335-88ee-533ddbe3b440
2025-03-28 23:54:51.205887+00	2025-05-16 21:34:45.493464+00	f	\N	ef0b9b6f-fe71-4f79-b4ac-a144bfdde15f	6. Valorización de Empresas	\N	352c8079-5e0a-4335-88ee-533ddbe3b440
2025-03-28 23:54:51.215152+00	2025-05-16 21:34:45.497222+00	f	\N	83e6da25-2397-4cea-abdc-69890aacb8de	1. Mercados Financieros	\N	d26b0162-05aa-4e51-b9b5-a5f75a17587c
2025-03-28 23:54:51.219092+00	2025-05-16 21:34:45.502041+00	f	\N	3d4aa98d-71d2-4822-a465-5f2e5375426d	2. Gestión de Instrumentos de Renta Fija	\N	d26b0162-05aa-4e51-b9b5-a5f75a17587c
2025-03-28 23:54:51.222998+00	2025-05-16 21:34:45.506833+00	f	\N	8d4bd964-5935-4ef2-8dd5-97c350c9620e	3. Gestión de Instrumentos de Renta Variable	\N	d26b0162-05aa-4e51-b9b5-a5f75a17587c
2025-03-28 23:54:51.227481+00	2025-05-16 21:34:45.511767+00	f	\N	be1dc3df-63c9-4cea-8b8e-80f59a7bd9ea	4. Gestión de Instrumentos Derivados	\N	d26b0162-05aa-4e51-b9b5-a5f75a17587c
2025-03-28 23:54:51.231297+00	2025-05-16 21:34:45.516828+00	f	\N	2ebc0a92-f809-46ff-b7fe-a4f60cc14a4c	5. Gestión de Portafolios de Inversión	\N	d26b0162-05aa-4e51-b9b5-a5f75a17587c
2025-03-28 23:54:51.242675+00	2025-05-16 21:34:45.522486+00	f	\N	623a9f69-2f13-4c0b-bfd1-1f63cde501a5	1. Gestión Integral de Riesgos	\N	490d24d3-4879-44b1-b5aa-025b10fc6f57
2025-03-28 23:54:51.246879+00	2025-05-16 21:34:45.526906+00	f	\N	6596f595-6107-473c-b820-a1c1d5c40e79	2. Gestión de Riesgo de Mercado	\N	490d24d3-4879-44b1-b5aa-025b10fc6f57
2025-03-28 23:54:51.251994+00	2025-05-16 21:34:45.532167+00	f	\N	4d3849ac-ca85-497d-bda8-3e25c136a19d	3 . Gestión de Riesgo de Crédito	\N	490d24d3-4879-44b1-b5aa-025b10fc6f57
2025-03-28 23:54:51.257025+00	2025-05-16 21:34:45.537985+00	f	\N	75a24883-948a-4e82-94b1-285596388a18	4 . Gestión de Tasa de Interés y Liquidez	\N	490d24d3-4879-44b1-b5aa-025b10fc6f57
2025-03-28 23:54:51.260365+00	2025-05-16 21:34:45.541287+00	f	\N	8432bac8-a0aa-4959-b056-b1037f82a363	5. Gestión de Riesgo de Operativo	\N	490d24d3-4879-44b1-b5aa-025b10fc6f57
2025-05-17 03:39:01.822009+00	2025-05-18 01:21:40.46155+00	f	\N	69a8ec97-45c3-4ee6-a75a-e423b798cb81	Matemática básica para Ciencia de Datos 	\N	eb1d9272-e64e-4a68-bf05-b96c980ef680
2025-05-17 03:39:01.82528+00	2025-05-18 01:21:40.464364+00	f	\N	477aca90-**************-f5f4b9646157	Estadística para Ciencia de Datos	\N	eb1d9272-e64e-4a68-bf05-b96c980ef680
2025-05-17 03:39:01.830545+00	2025-05-18 01:21:40.468449+00	f	\N	33053657-57f6-4976-abc9-3ead4ecc4559	Programación en python	\N	eb1d9272-e64e-4a68-bf05-b96c980ef680
2025-05-17 03:39:01.834331+00	2025-05-18 01:21:40.471257+00	f	\N	9d7773ff-f660-4547-a0fa-4e03e3a627f6	Manejo de Base de Datos	\N	eb1d9272-e64e-4a68-bf05-b96c980ef680
2025-05-17 03:39:01.838593+00	2025-05-18 01:21:40.474842+00	f	\N	20f262eb-0cc9-42c7-97ad-1008d2cebec5	Visualización de Datos	\N	eb1d9272-e64e-4a68-bf05-b96c980ef680
2025-05-17 03:39:01.842296+00	2025-05-18 01:21:40.478098+00	f	\N	6e91278d-2b43-4c1e-a109-fb15d063f6d5	Data Storytelling	\N	eb1d9272-e64e-4a68-bf05-b96c980ef680
2025-05-17 03:39:01.845192+00	2025-05-18 01:21:40.481149+00	f	\N	75a8561a-9b4d-420e-a73e-164e277df1a0	Taller Aplicado 1\t	\N	eb1d9272-e64e-4a68-bf05-b96c980ef680
2025-05-17 03:39:01.85012+00	2025-05-18 01:21:40.487097+00	f	\N	ac8c088e-2e7e-4f35-b015-db34b112afb0	Fundamentos del aprendizaje supervisado	\N	eaa24f4b-0422-4ff6-b351-d2d883ce9596
2025-05-17 03:39:01.852033+00	2025-05-18 01:21:40.488875+00	f	\N	1e54fd01-fe03-4b16-be82-de7781799c67	Preparación de datos para modelos	\N	eaa24f4b-0422-4ff6-b351-d2d883ce9596
2025-05-17 03:39:01.854851+00	2025-05-18 01:21:40.491427+00	f	\N	91c3932d-adca-41bb-a25a-99da689849bb	Modelos de regresión 	\N	eaa24f4b-0422-4ff6-b351-d2d883ce9596
2025-05-17 03:39:01.857886+00	2025-05-18 01:21:40.494296+00	f	\N	b0ff26ef-886d-48d2-8ebb-4e543fe3e0b7	Modelos bayesianos	\N	eaa24f4b-0422-4ff6-b351-d2d883ce9596
2025-05-17 03:39:01.859979+00	2025-05-18 01:21:40.496004+00	f	\N	507fbc93-3b4c-4bcf-a23b-63226a2584ac	Modelos de clasificación: 	\N	eaa24f4b-0422-4ff6-b351-d2d883ce9596
2025-05-17 03:39:01.863107+00	2025-05-18 01:21:40.498605+00	f	\N	f008f0e6-7fad-4b25-84a3-5fe143f53ae7	Técnicas avanzadas de modelos ensemble 	\N	eaa24f4b-0422-4ff6-b351-d2d883ce9596
2025-05-17 03:39:01.866224+00	2025-05-18 01:21:40.501488+00	f	\N	9c989570-6a29-45d0-9a3b-fbb830ff98b4	Evaluación, optimización y tuning de modelos	\N	eaa24f4b-0422-4ff6-b351-d2d883ce9596
2025-05-17 03:39:01.868769+00	2025-05-18 01:21:40.504643+00	f	\N	e511d26a-3233-4f86-b662-4829cc4754dd	Taller Aplicado 2	\N	eaa24f4b-0422-4ff6-b351-d2d883ce9596
2025-05-17 03:39:01.873552+00	2025-05-18 01:21:40.509148+00	f	\N	0d90e1f7-01e3-40f9-aea4-db6448c3b9ff	Fundamentos del aprendizaje no supervisado	\N	e34a3812-fcc7-4018-bdf9-c315ff4bb2eb
2025-05-17 03:39:01.875426+00	2025-05-18 01:21:40.51091+00	f	\N	a3e63bfd-8e1c-47df-b9bc-e344099c8c6c	Reducción de dimensionalidad (Componentes principales - PCA)	\N	e34a3812-fcc7-4018-bdf9-c315ff4bb2eb
2025-05-17 03:39:01.877636+00	2025-05-18 01:21:40.512738+00	f	\N	b8034525-0718-4b96-890b-51a0df86a3cf	Análisis de clúster	\N	e34a3812-fcc7-4018-bdf9-c315ff4bb2eb
2025-05-17 03:39:01.880161+00	2025-05-18 01:21:40.514691+00	f	\N	1580aecb-7a37-4be1-834f-081035561170	Métodos jerárquicos y particionales 	\N	e34a3812-fcc7-4018-bdf9-c315ff4bb2eb
2025-05-17 03:39:01.883849+00	2025-05-18 01:21:40.517488+00	f	\N	35c65680-c956-4af0-a11a-a029e4a15b9f	Algoritmos avanzados: Afinidad, Mean Shift, OPTICS, Kohonen (SOM)	\N	e34a3812-fcc7-4018-bdf9-c315ff4bb2eb
2025-05-17 03:39:01.886188+00	2025-05-18 01:21:40.519203+00	f	\N	c2f5d7bb-a201-423e-a7a1-7d418666d363	Evaluación avanzada de clúster	\N	e34a3812-fcc7-4018-bdf9-c315ff4bb2eb
2025-05-17 03:39:01.889251+00	2025-05-18 01:21:40.521062+00	f	\N	92b2092b-ad28-49eb-96d1-357257c414c8	Taller Aplicado 3	\N	e34a3812-fcc7-4018-bdf9-c315ff4bb2eb
2025-05-17 03:39:01.8953+00	2025-05-18 01:21:40.525448+00	f	\N	6e6b9789-272a-436d-8560-a30fb32929c9	Fundamentos de Deep Learning e Inteligencia Artificial	\N	f39c4d44-2da9-40a3-a8f5-9fea840db4be
2025-05-17 03:39:01.897258+00	2025-05-18 01:21:40.527286+00	f	\N	a31e5fd2-c29e-4b11-a9c1-642bcdba4d9e	Redes Neuronales\t	\N	f39c4d44-2da9-40a3-a8f5-9fea840db4be
2025-05-17 03:39:01.901911+00	2025-05-18 01:21:40.531458+00	f	\N	37f289b7-a36e-4065-9125-b29159bc482a	Optimización de redes neuronales 	\N	f39c4d44-2da9-40a3-a8f5-9fea840db4be
2025-05-17 03:39:01.903826+00	2025-05-18 01:21:40.533596+00	f	\N	0fc2ee08-e12b-4a52-9aa2-0dcbc9e464c8	Procesamiento del lenguaje natural (NLP)	\N	f39c4d44-2da9-40a3-a8f5-9fea840db4be
2025-05-17 03:39:01.905731+00	2025-05-18 01:21:40.535405+00	f	\N	6b9db606-ef71-47d1-a7f6-ddcebd9e1ec6	Taller Aplicado 4	\N	f39c4d44-2da9-40a3-a8f5-9fea840db4be
2025-05-17 03:39:01.819235+00	2025-05-18 01:21:40.459331+00	f	\N	f979866c-5d3b-4465-b3ec-b9705292935c	Introducción a la Ciencia de Datos orientada a negocios	\N	eb1d9272-e64e-4a68-bf05-b96c980ef680
2025-05-18 04:03:51.424994+00	2025-05-18 04:12:50.122457+00	f	\N	dcca79ce-df49-41d0-bc1b-904b7934eb1f	Factores de Riesgo de Mercado	\N	59767960-f9ce-4a2c-9b96-ec24548ffb10
2025-05-18 04:03:51.427347+00	2025-05-18 04:12:50.125111+00	f	\N	0ad3948e-39bf-4ff4-83ea-7f9bbf54ca0b	Valorización de Instrumentos Financieros de Mercado	\N	59767960-f9ce-4a2c-9b96-ec24548ffb10
2025-05-18 04:03:51.406247+00	2025-05-18 04:12:50.100835+00	f	\N	5db22dfe-e2a0-4706-8358-02e4eb3c6baf	Fundamentos de Programación en Python	\N	ac632844-63b1-453b-a640-ae1db8366944
2025-05-18 04:03:51.422101+00	2025-05-18 04:12:50.119127+00	f	\N	85afc1bc-23f1-4ae0-9437-d69e0594cd52	Regulación Financiera en Riesgo de Mercado	\N	59767960-f9ce-4a2c-9b96-ec24548ffb10
2025-05-18 04:03:51.431047+00	2025-05-18 04:12:50.129492+00	f	\N	79152590-2023-46af-879f-5fc4a8eef632	Sensibilidades de Instrumentos Financieros de Mercado	\N	59767960-f9ce-4a2c-9b96-ec24548ffb10
2025-05-18 04:03:51.434761+00	2025-05-18 04:12:50.134473+00	f	\N	cdc89534-8128-4798-af22-6bcbb1aadfea	Modelos de Riesgos de Mercado en Instrumentos Financieros	\N	59767960-f9ce-4a2c-9b96-ec24548ffb10
2025-05-18 04:03:51.438473+00	2025-05-18 04:12:50.139897+00	f	\N	f1f1ed81-9409-44c8-9e19-1b4f4d079f3d	Estimación del VaR de Instrumentos Financieros de Mercado	\N	59767960-f9ce-4a2c-9b96-ec24548ffb10
2025-05-18 04:03:51.442122+00	2025-05-18 04:12:50.144054+00	f	\N	9ab3e919-2090-4989-820e-db76a89c464a	Backtesting y Stress Testing en Riesgo de Mercado	\N	59767960-f9ce-4a2c-9b96-ec24548ffb10
2025-05-18 04:03:51.44761+00	2025-05-18 04:12:50.150707+00	f	\N	3a492f23-6dc0-4367-8941-49742f0b5269	Regulación Financiera en Riesgo de Crédito	\N	217ed8e0-b66b-4f7e-bb64-b4e49c97fcd8
2025-05-18 04:03:51.450492+00	2025-05-18 04:12:50.153982+00	f	\N	2909797d-4978-46c6-b290-1dbc8b992e8c	Modelos Riesgos de Crédito	\N	217ed8e0-b66b-4f7e-bb64-b4e49c97fcd8
2025-05-18 04:03:51.454564+00	2025-05-18 04:12:50.158706+00	f	\N	5e7e27f3-c0a3-4fb3-bb94-33900f125f1d	Modelos Pérdida Esperada	\N	217ed8e0-b66b-4f7e-bb64-b4e49c97fcd8
2025-05-18 04:03:51.457972+00	2025-05-18 04:12:50.163121+00	f	\N	2367501e-107b-49d8-bd35-c80834ca177b	Modelos Scoring	\N	217ed8e0-b66b-4f7e-bb64-b4e49c97fcd8
2025-05-18 04:03:51.460596+00	2025-05-18 04:12:50.166639+00	f	\N	a567ecd8-aa1c-42c4-9f28-1d0b4ab7046b	Modelos Machine Learning 	\N	217ed8e0-b66b-4f7e-bb64-b4e49c97fcd8
2025-05-18 04:03:51.463563+00	2025-05-18 04:12:50.169761+00	f	\N	be311ea4-8eea-45fa-ac9f-64b7391698d1	Modelos de Rating	\N	217ed8e0-b66b-4f7e-bb64-b4e49c97fcd8
2025-05-18 04:03:51.466404+00	2025-05-18 04:12:50.173024+00	f	\N	336f2087-da81-4988-ace2-69179fed40f6	Backtesting y Stress Testing en Riesgo de Crédito	\N	217ed8e0-b66b-4f7e-bb64-b4e49c97fcd8
2025-05-18 04:03:51.470858+00	2025-05-18 04:12:50.178253+00	f	\N	ce2290ef-959f-495d-8329-ae2210587813	Regulación Financiera en Riesgo de Liquidez	\N	106e5f83-4dae-4a5e-8bab-053357ada930
2025-05-18 04:03:51.473628+00	2025-05-18 04:12:50.181626+00	f	\N	7b6c535f-be8a-403a-b30c-d12d42835cdb	Modelos Cuantitativos en Riesgo de Liquidez	\N	106e5f83-4dae-4a5e-8bab-053357ada930
2025-05-18 04:03:51.477696+00	2025-05-18 04:12:50.186486+00	f	\N	d85f3cc1-787a-47c3-941c-6d034f878c1e	Backtesting y Stress Testing en Riesgo de Liquidez	\N	106e5f83-4dae-4a5e-8bab-053357ada930
2025-05-18 04:03:51.482248+00	2025-05-18 04:12:50.191799+00	f	\N	4a506d0a-008d-4ac1-84c6-d84448979663	Regulación Financiera en Riesgo de ALM	\N	33017d2a-d019-4c0f-8ea8-f2a56f974478
2025-05-18 04:03:51.485261+00	2025-05-18 04:12:50.194893+00	f	\N	362a65e9-b0f4-4fa5-b825-3a125dc6f667	Modelos Cuantitativos en Riesgo de ALM	\N	33017d2a-d019-4c0f-8ea8-f2a56f974478
2025-05-18 04:03:51.490329+00	2025-05-18 04:12:50.200413+00	f	\N	4caed78f-fb23-4d1a-90e8-accc4a443dcb	Derivados de Crédito	\N	35776dda-f413-4df3-8a05-1c9c5d2ae99c
2025-05-18 04:03:51.492996+00	2025-05-18 04:12:50.203641+00	f	\N	7a17f49a-4921-4eac-b97e-5d3d70ef9315	Métricas de Riesgo de Contraparte	\N	35776dda-f413-4df3-8a05-1c9c5d2ae99c
2025-05-18 04:03:51.49884+00	2025-05-18 04:12:50.211352+00	f	\N	a5e7ab1f-42d0-4f2f-876f-766c218554db	Regulación y Reportería en Riesgo Operacional	\N	9e13a45c-e6ec-4e1f-a75b-607961101f3e
2025-05-18 04:03:51.501609+00	2025-05-18 04:12:50.214941+00	f	\N	9fffd7ac-261f-41da-9483-5f608b1e9f73	Identificación de Eventos de Riesgo Operacional	\N	9e13a45c-e6ec-4e1f-a75b-607961101f3e
2025-05-18 04:03:51.504326+00	2025-05-18 04:12:50.218284+00	f	\N	df9ed1d8-5c7b-4a7d-9b43-b9d71e6a569d	Modelos Cualitativos de Riesgo Operacional	\N	9e13a45c-e6ec-4e1f-a75b-607961101f3e
2025-05-18 04:03:51.507256+00	2025-05-18 04:12:50.221437+00	f	\N	a8fb014c-ff72-4781-828b-2a42d732894b	Modelos Cuantitativos de Riesgo Operacional 	\N	9e13a45c-e6ec-4e1f-a75b-607961101f3e
2025-05-18 04:03:51.510686+00	2025-05-18 04:12:50.225321+00	f	\N	cf0d80b8-59f8-4afa-ad40-79f8db678303	Taller de Riesgo Operacional	\N	9e13a45c-e6ec-4e1f-a75b-607961101f3e
2025-05-18 04:03:51.512209+00	2025-05-18 04:12:50.226928+00	f	\N	1af226f0-902c-4acb-8f13-020ee77b5356	Talleres Complementarios	\N	9e13a45c-e6ec-4e1f-a75b-607961101f3e
2025-05-21 20:49:35.466918+00	2025-05-21 20:49:35.466926+00	f	\N	4b6d3529-8e5e-47be-8e64-5e808c4aad34	Matemáticas Financieras	\N	38bb636f-5dda-45e3-9e28-3c2e8b9423b0
2025-05-21 20:49:35.469634+00	2025-05-21 20:49:35.469642+00	f	\N	50dccf87-1e25-4478-8e8b-200fa3111584	Contabilidad Gerencial	\N	38bb636f-5dda-45e3-9e28-3c2e8b9423b0
2025-05-21 20:49:35.472321+00	2025-05-21 20:49:35.472333+00	f	\N	67b87df1-57b4-432c-b34c-560b48c79501	Análisis de Estados Financieros	\N	38bb636f-5dda-45e3-9e28-3c2e8b9423b0
2025-05-21 20:49:35.47527+00	2025-05-21 20:49:35.475284+00	f	\N	c9fa4165-ee46-4e7d-840d-765f743e414e	Costos y Presupuestos	\N	38bb636f-5dda-45e3-9e28-3c2e8b9423b0
2025-05-21 20:49:35.478202+00	2025-05-21 20:49:35.478216+00	f	\N	c398b8e0-8849-469f-9211-88b1bc42d08c	Proyectos de Inversión	\N	38bb636f-5dda-45e3-9e28-3c2e8b9423b0
2025-05-21 20:49:35.4806+00	2025-05-21 20:49:35.480608+00	f	\N	5c45f295-755b-4665-869d-c739c5919b4a	Taller de Software Aplicado a Finanzas (Excel, VBA, Python, Bloomberg)	\N	38bb636f-5dda-45e3-9e28-3c2e8b9423b0
2025-05-21 20:49:35.484649+00	2025-05-21 20:49:35.484657+00	f	\N	836331e5-32e8-44c4-b7a4-000e9e43ba7a	Finanzas de Corto Plazo (Capital de Trabajo)	\N	4095aed4-4e2b-4ed2-9388-331b2c87e016
2025-05-21 20:49:35.486897+00	2025-05-21 20:49:35.486905+00	f	\N	a30feee7-c3e1-401d-9451-0cc64b1c11b3	Finanzas de Largo Plazo (Estructura y Costo de Capital)	\N	4095aed4-4e2b-4ed2-9388-331b2c87e016
2025-05-21 20:49:35.48909+00	2025-05-21 20:49:35.489098+00	f	\N	485e44e7-aff9-4993-a073-b96a0c1330ad	Valorización de Empresas	\N	4095aed4-4e2b-4ed2-9388-331b2c87e016
2025-05-21 20:49:35.491436+00	2025-05-21 20:49:35.491444+00	f	\N	93391abd-3e8b-4bbd-9e68-7809ee5b90cd	Fusiones y Adquisiciones	\N	4095aed4-4e2b-4ed2-9388-331b2c87e016
2025-05-21 20:49:35.49545+00	2025-05-21 20:49:35.495458+00	f	\N	df092030-675f-4907-bb82-3af758b3d5ab	Sistema Financiero	\N	dcc89059-c1b7-4141-b525-ecbd55f357dd
2025-05-21 20:49:35.497811+00	2025-05-21 20:49:35.497819+00	f	\N	8d894f9b-73b3-4fbf-8743-0abfbbf119c3	Mercado de Valores	\N	dcc89059-c1b7-4141-b525-ecbd55f357dd
2025-05-21 20:49:35.500127+00	2025-05-21 20:49:35.500134+00	f	\N	793f908e-650e-434c-a3d2-13ea96b374e0	Productos Financieros	\N	dcc89059-c1b7-4141-b525-ecbd55f357dd
2025-05-21 20:49:35.50243+00	2025-05-21 20:49:35.502437+00	f	\N	2d72a744-18a9-498f-a712-e3cf4c9f653e	Instrumentos de Renta Fija, Variable y Derivados	\N	dcc89059-c1b7-4141-b525-ecbd55f357dd
2025-05-21 20:49:35.504748+00	2025-05-21 20:49:35.504756+00	f	\N	8f310cd6-ec84-4580-90c4-36060dc2a5ef	Gestión de Portafolios de Inversión	\N	dcc89059-c1b7-4141-b525-ecbd55f357dd
2025-05-21 20:49:35.508699+00	2025-05-21 20:49:35.508707+00	f	\N	fc71c1b7-f97c-402b-9fdd-bad6dc2e9447	Planeamiento y Control Financiero	\N	ad29351a-2ccc-406a-8469-a16c90ded53d
2025-05-21 20:49:35.511016+00	2025-05-21 20:49:35.511027+00	f	\N	00aacdc8-b5f1-429c-aa8c-a466d5505474	Gestión de Tesorería	\N	ad29351a-2ccc-406a-8469-a16c90ded53d
2025-05-21 20:49:35.513323+00	2025-05-21 20:49:35.51333+00	f	\N	b963b941-c4d2-41f6-866a-e891b2b5f127	Gestión Tributaria	\N	ad29351a-2ccc-406a-8469-a16c90ded53d
2025-05-21 20:49:35.515563+00	2025-05-21 20:49:35.515571+00	f	\N	18c649e7-e5c6-404a-b62b-514b02a95055	Gestión de Riesgos Corporativos	\N	ad29351a-2ccc-406a-8469-a16c90ded53d
2025-05-21 20:49:35.517848+00	2025-05-21 20:49:35.517855+00	f	\N	8518a1aa-c4dc-46a1-9817-469799f4e704	Gobierno Corporativo	\N	ad29351a-2ccc-406a-8469-a16c90ded53d
2025-05-21 20:49:35.520172+00	2025-05-21 20:49:35.52018+00	f	\N	1c4f9189-f14e-4e84-b2f5-991ead7b3a50	Gestión Financiera Corporativa Aplicada	\N	ad29351a-2ccc-406a-8469-a16c90ded53d
2025-05-21 20:49:35.522429+00	2025-05-21 20:49:35.522437+00	f	\N	e64b1c63-7620-4374-9fac-33a5ea5c80bb	Talleres Complementarios	\N	ad29351a-2ccc-406a-8469-a16c90ded53d
\.


--
-- Data for Name: core_offering; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_offering (created_at, updated_at, deleted, deleted_at, oid, slug, name, start_date, end_date, description, duration, frequency, hours, schedule, modality, type, stage, format, base_price, discount, deleted_by_id, thumbnail_id, objectives, foreign_base_price, code_name, ext_reference, "order", long_name) FROM stdin;
2025-05-17 01:50:05.829122+00	2025-07-16 01:29:25.100788+00	f	\N	86629ba1-3a42-407d-83c3-32d1f7171e67	programa-de-especializacion-en-ciencia-de-datos-2025-1	Programa en Ciencia de Datos	2025-07-27	2025-11-23	El Programa de Especialización en Ciencia de Datos de CEU está diseñado para formar profesionales capaces de aplicar herramientas estadísticas, modelos de machine learning y técnicas de inteligencia artificial para resolver problemas reales en finanzas y negocios. A lo largo de 4 módulos y talleres prácticos, los participantes dominarán Python, librerías como NumPy, Pandas, Scikit-learn y TensorFlow, y aprenderán a implementar soluciones analíticas usando regresión, clasificación, clustering, redes neuronales y visualización de datos con herramientas como Seaborn. El enfoque es 100 % aplicado, con proyectos alineados al análisis de riesgo, segmentación de clientes y predicción de tendencias de mercado.	4 meses	Sábados y domingos	90	5:00 pm a 8:00 pm	REMOTE	SPECIALIZATION	ENROLLMENT	LIVE	1000.00	15.00	\N	8446342e-71b6-415e-82c6-960c356f225b	[{"title": "", "description": "Comprender y aplicar fundamentos estadísticos y matemáticos en contextos de análisis de datos."}, {"title": "", "description": "Utilizar herramientas y lenguajes como Python, SQL, Pandas, Scikit-learn y TensorFlow para el procesamiento, modelado y visualización de datos."}, {"title": "", "description": "Desarrollar modelos de machine learning supervisado y no supervisado, así como redes neuronales profundas (Deep Learning) para resolver problemas complejos."}, {"title": "", "description": "Aplicar técnicas de segmentación, predicción y clasificación en escenarios reales del ámbito financiero, comercial y empresarial."}, {"title": "", "description": "Comunicar hallazgos de forma efectiva mediante visualizaciones, dashboards y storytelling analítico."}, {"title": "", "description": "Construir un portafolio profesional con proyectos aplicados y preparados para su exposición en entornos laborales y digitales (GitHub, entrevistas, etc)."}]	0.00	\N	\N	\N	\N
2025-05-21 20:26:31.801699+00	2025-07-16 01:30:50.706076+00	f	\N	a6e7d9c9-64d3-4565-a7cb-029e45e90a49	programa-de-especializacion-en-finanzas-corporativas	Programa de Especialización en Finanzas Corporativas	2025-07-27	2025-11-23	El Programa de Especialización en Finanzas Corporativas brinda una formación integral y práctica, alineada a los estándares internacionales del CFA. Está diseñado especialmente para quienes buscan fortalecer su perfil profesional en finanzas, adquiriendo herramientas y habilidades prácticas esenciales para enfrentar desafíos financieros corporativos reales.	4 meses	Sábados y domingos	90	8:00 am a 11:00 am	REMOTE	SPECIALIZATION	ENROLLMENT	LIVE	1000.00	15.00	\N	dde6f88a-a62a-433b-a8d3-b341279db47d	[{"title": "Dominar la analítica financiera", "description": "Interpretar estados financieros, proyectar flujos de caja y medir el valor creado o destruido por las decisiones corporativas."}, {"title": "Optimizar la estructura de capital", "description": "Calcular WACC, diseñar políticas de dividendos y estructurar financiamientos que maximicen el valor empresarial."}, {"title": "Valorar empresas y proyectos", "description": "Aplicar DCF, múltiplos comparables y técnicas de evaluación bajo incertidumbre para respaldar fusiones, adquisiciones y CAPEX estratégicos."}, {"title": "Gestionar portafolios y riesgos", "description": "Construir carteras eficientes, usar derivados para cobertura y cuantificar riesgos con métricas avanzadas (VaR, stress testing)."}, {"title": "Aplicar tecnología financiera ", "description": "Automatizar reportes y análisis con Excel, VBA y Python."}, {"title": "Impulsar el crecimiento profesional ", "description": "Recibir feedback personalizado y coaching de carrera para implementar de inmediato las mejores prácticas aprendidas."}]	0.00	\N	\N	\N	\N
2025-05-16 21:27:33.568437+00	2025-07-05 03:50:04.227631+00	f	\N	42114c08-4ba1-46d1-a930-09ac632c09f9	taller-de-repaso-bcrp-osinergmin-osiptel-ositran-2025-1	Taller Para Postulantes BCRP y Reguladoras (OSINERGMIN, OSIPTEL y OSITRAN)	2025-09-06	2025-09-20	Este taller está diseñado para brindar a los participantes una formación integral en Economía y Finanzas, asegurando una preparación sólida para el examen de admisión al Curso de Extensión Universitaria del Banco Central de Reserva del Perú (BCRP), OSINERGMIN, OSIPTEL, OSITRAN y SUNASS.\r\n\r\nEl plan de estudios ha sido cuidadosamente estructurado para abarcar de manera completa temas esenciales de economía y finanzas.	5 días	Fin de semana	20	2:00 a 10:00 PM	REMOTE	REVIEW_WORKSHOP	ENROLLMENT	LIVE	0.00	0.00	\N	aaccaf9d-bd5e-4800-818a-57a2bc1146c7	[{"title": "Preparación", "description": "Preparar a los participantes para aprobar el examen de ingreso a los diferentes cursos de Extensión como BCRP, OSINERGMIN, OSIPTEL y OSTIRAN."}, {"title": "Formación", "description": "Formar profesionales competentes, con sólidos conocimientos en economía y finanzas, acorde con las exigencias del mercado laboral actual."}]	0.00	\N	\N	\N	\N
2025-03-03 22:52:41.935431+00	2025-03-28 21:23:31.405346+00	f	\N	51193e10-7a3d-43ec-b971-0573f138ca7d	cursos-de-extension-universitaria-2025-1	Cursos de Extensión Universitaria	2025-01-09	2025-04-29	El Programa de Preparación para Cursos de Extensión Universitaria ofrece al participante una preparación integral y focalizada con el objetivo de rendir de manera exitosa los exámenes a los diferentes Cursos de Extensión Universitaria patrocinados por entidades públicas de gran prestigio como el Banco Central de Reserva del Perú (BCRP), la Superintendencia de Banca Seguros y AFP (SBS), el Organismo Supervisor de la Inversión en Energía y Minería (OSINERGMIN), el Organismo Supervisor de Inversión Privada en Telecomunicaciones (OSIPTEL), Superintendencia del Mercado de Valores (SMV), entre otros. Ello con objetivo de formar profesionales de alto potencial, calificados y especializados, que los habilite para laborar dentro de dichas entidades públicas.	4 meses	Sábados y domingos	96	8:00 am - 11:00 am	REMOTE	PREPARATION	FINISHED	LIVE	1000.00	30.00	\N	e6076b99-7292-4c28-97db-b38aec78782b	[]	720.00	\N	\N	\N	\N
2025-03-28 18:47:02.13867+00	2025-07-05 03:16:18.960466+00	f	\N	3424b4ac-987e-4ed4-9890-fa79603f8789	programa-de-especializacion-en-finanzas-avanzadas-2025-2	Programa de Finanzas Avanzadas	2025-05-30	2025-09-26	Nuestro programa de Finanzas Avanzadas ofrece a los participantes todas las herramientas necesarias para desempeñarse adecuadamente en el mercado financiero. Se impartirán clases prácticas en Finanzas Corporativas, Valorización de Empresas, Gestión de Riesgos, Gestión de Inversiones, entre otros; Así como el manejo de los principales software que se usan actualmente en el ámbito financiero, como Excel, VBA, R-Studio y Python.	4 meses	Lunes, miércoles y viernes	90	6:00 pm a 8:00pm	REMOTE	SPECIALIZATION	FINISHED	LIVE	1000.00	25.00	\N	d5835b11-5a2b-4d6f-a894-faa7b6cf4475	[{"title": "Trabajar", "description": "Adquirir las herramientas e instrumentos necesarios para posicionarse adecuadamente en las principales empresas del país."}, {"title": "rincipales empresas del país.  Especializarse", "description": "Mejorar nuestro entendimiento y dominio de técnicas e instrumentos financieros."}, {"title": "Actualizar conocimientos", "description": "Revisar los últimos cambios técnicos y normativos del ámbito financiero."}, {"title": "Aprende", "description": "Gestionar las finanzas para crear valor en nuestra organización. "}]	270.00	\N	\N	\N	\N
2025-02-27 23:37:50.040983+00	2025-06-29 01:07:02.518018+00	f	\N	7829808c-71ff-45a0-9dd6-ca90e01447ea	gestion-de-riesgos-financieros-2025-1	Gestión de Riesgos Financieros	2025-03-29	2025-06-13	Diseñado para formar profesionales altamente capacitados en la identificación, medición y mitigación de riesgos financieros. Este programa abarca temas clave como riesgos de mercado, crédito, liquidez y operacional, alineándose con los estándares globales y la certificación Financial Risk Manager (FRM).	4 meses	Sábados y Domingos	96	5:00 pm - 8:00 pm	REMOTE	SPECIALIZATION	FINISHED	LIVE	1000.00	30.00	\N	51a9a686-894c-428a-bbb8-af75a3f40541	[{"title": "Aprendizaje práctico", "description": "Aplicación de metodologías de riesgo utilizando softwares líderes como Python, R y Bloomberg Terminal"}, {"title": "Enfoque integral", "description": "Cubre riesgos clave (mercado, crédito, liquidez y operacional) y riesgos emergentes como ESG y ciberseguridad."}, {"title": "Conexión con la industria", "description": "Aprende de profesionales con experiencia en la gestión de riesgos en instituciones financieras globales."}, {"title": "Material actualizado", "description": "Basado en normativas y mejores prácticas internacionales, incluyendo Basilea III y estándares ESG."}, {"title": "Flexibilidad", "description": "Modalidad en línea en vivo, adaptada para profesionales que buscan equilibrar sus estudios y su carrera."}]	272.00	\N	\N	\N	\N
2025-02-28 15:09:14.968587+00	2025-05-16 20:54:43.161456+00	f	\N	f3dbb697-1724-4106-93c2-96de14cf36ce	negocios-economia-y-finanzas-2025-1	Negocios, Economía y Finanzas	2025-04-19	2025-08-02	El Programa de Formación Universitaria está diseñado para fortalecer las habilidades técnicas\r\ny estratégicas de los estudiantes en sus primeros ciclos universitarios.\r\nA través de un enfoque práctico у multidisciplinario, abarca áreas fundamentales como matemáticas aplicadas, economía, finanzas, administración y contabilidad, integrando además herramientas digitales esenciales en el entorno corporativo.	4 meses	Sábado y Domingo	96	11:00 am a 2:00 pm	REMOTE	UNDERGRADUATE_FORMATION	FINISHED	LIVE	1000.00	40.00	\N	a0f42cfa-4476-483a-98c2-54e4ec38db83	[{"title": "", "description": "Brindar a los estudiantes una base sólida de conocimientos matemáticos, económicos, administrativos y financieros esenciales para su desarrollo profesional."}, {"title": "", "description": "Capacitar a los participantes en herramientas digitales y de análisis de datos, altamente demandadas en el mercado laboral actual."}, {"title": "", "description": "Fortalecer la capacidad de toma de decisiones estratégicas en el ámbito financiero y empresarial."}, {"title": "", "description": "Introducir conceptos clave de contabilidad, costos y presupuestos para una mejor gestión empresarial."}, {"title": "", "description": "Desarrollar habilidades en econometría y estadísticas aplicadas a la economía y los negocios."}, {"title": "", "description": "Impulsar el aprendizaje de gestión empresarial y emprendimiento con enfoque en modelos de negocio innovadores."}]	272.00	\N	\N	\N	\N
2025-02-27 23:51:19.28951+00	2025-05-16 20:54:24.341985+00	f	\N	f623cc82-d416-47bf-95b7-fff05c1465b7	mercado-de-capitales-2025-1	Mercado de Capitales	2025-03-29	2025-07-29	El Programa de Especialización en Mercado de Capitales está diseñado para capacitar a los participantes en el funcionamiento, regulación y operativa del mercado de valores en Perú, Latinoamérica y España. A través de un enfoque teórico-práctico, los participantes aprenderán a analizar instrumentos financieros, gestionar inversiones y aplicar técnicas avanzadas de trading y gestión de riesgos.	4 meses	Sábados y domingos	96	8:00 am - 11:00 am	REMOTE	SPECIALIZATION	FINISHED	LIVE	1000.00	40.00	\N	8288b70a-190f-4df6-b438-5f7c5cc468e6	[{"title": "Capacitar", "description": "Brindar conocimientos sólidos sobre el mercado de valores, su funcionamiento y la normativa aplicable en distintos países."}, {"title": " Especializar", "description": "Desarrollar habilidades para la toma de decisiones de inversión, la gestión de riesgos y el análisis de instrumentos financieros."}, {"title": "Actualizar Conocimientos", "description": "Presentar las últimas tendencias en mercados financieros, incluyendo derivados, fintech, inversión ESG y crowdfunding."}, {"title": "Aprender con práctica", "description": "Aplicar herramientas de análisis financiero mediante talleres y simulaciones en plataformas de trading y gestión de inversiones"}]	270.00	\N	\N	\N	\N
2025-05-18 02:01:10.400105+00	2025-07-16 01:30:39.612784+00	f	\N	2949a0bb-dd02-4312-b17f-c7d893d28de1	programa-de-especializacion-en-gestion-de-riesgos-financieros-2025-2	Programa de Gestión de Riesgos Financieros	2025-07-27	2025-11-23	El Programa de Especialización en Gestión de Riesgos Financieros está diseñado para formar profesionales altamente capacitados en la identificación, medición y mitigación de riesgos financieros. Este programa abarca temas clave como riesgos de mercado, crédito, liquidez y operacional, alineándose con los estándares globales y la certificación Financial Risk Manager (FRM).\r\nLos participantes desarrollarán competencias prácticas utilizando herramientas líderes en la industria como Excel, Python, R, SQL y el acceso a datos financieros reales mediante Bloomberg Terminal. Este enfoque integral les permitirá enfrentar los retos del entorno financiero actual y anticipar riesgos emergentes.	4 meses	Sábados y domingos	90	11:00 AM a 2:00 PM	REMOTE	SPECIALIZATION	ENROLLMENT	LIVE	1000.00	15.00	\N	2f5bdc19-c01a-48fa-9add-30a14bd2aff8	[{"title": "", "description": "Desarrollar capacidades analíticas y cuantitativas para la gestión de riesgos financieros en instituciones bancarias, empresas financieras, aseguradoras y consultoras."}, {"title": "", "description": "Formar especialistas capaces de implementar modelos avanzados como VaR, CVaR, ECL, XVA, scoring, LDA, entre otros, alineados a los marcos de Basilea y las NIIF."}, {"title": "", "description": "Aplicar herramientas estadísticas y de programación como Python, Excel, SQL y R para la valorización de instrumentos, stress testing y validación de modelos."}, {"title": "", "description": "Capacitar en normativas nacionales e internacionales como Basilea III, IFRS 9, FRTB, LCR y NSFR, fundamentales para una correcta toma de decisiones y cumplimiento regulatorio."}, {"title": "", "description": "Integrar teoría financiera, tecnología y normativa para crear soluciones efectivas de control, supervisión y mitigación de riesgos financieros complejos."}]	0.00	\N	\N	\N	\N
2025-04-08 23:19:20.826189+00	2025-07-05 03:15:37.327287+00	f	\N	9785aa42-1580-4ac0-8426-0dbce95614fd	programa-de-preparacion-para-cursos-de-extension-2025-2	Programa de Preparación Para Cursos de Extensión	2025-05-31	2025-09-28	Nuestro porgrama ofrece al participante una preparación integral y focalizada con el objetivo de rendir de manera exitosa los exámenes a los diferentes Cursos de Extensión Universitaria patrocinados por entidades públicas de gran prestigio como el Banco Central de Reserva del Perú (BCRP), la Superintendencia de Banca Seguros y AFP (SBS), el Organismo Supervisor de la Inversión en Energía y Minería (OSINERGMIN), el Organismo Supervisor de Inversión Privada en Telecomunicaciones (OSIPTEL), Superintendencia del Mercado de Valores (SMV), entre otros. Ello con objetivo de formar profesionales de alto potencial, calificados y especializados, que los habilite para laborar dentro de dichas entidades públicas.\r\n\r\nPara ello se ha diseñado un plan de estudio completo en macroeconomía, microeconomía, finanzas, econometría y herramientas cuantitativas. Además, reunimos una experimentada plana docente, quienes también han participado en distintos Cursos de Extensión Universitaria alcanzando los primeros puestos y gracias a ello, cuentan con una óptima línea de carrera en la actualidad.	4 meses	Sábados y domingos	90	2:00 pm a 5:00 pm	REMOTE	PREPARATION	FINISHED	LIVE	1000.00	25.00	\N	55d9fa98-aa2c-471c-86f8-5b951467f8a8	[]	720.00	\N	\N	\N	\N
\.


--
-- Data for Name: core_offeringmodule; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_offeringmodule (created_at, updated_at, deleted, deleted_at, omid, title, deleted_by_id, offering_id) FROM stdin;
2025-02-28 00:02:42.581766+00	2025-02-28 00:06:12.762736+00	f	\N	0952572e-fbec-44b5-821c-f2dd209705ef	Introducción al Mercado de Valores	\N	f623cc82-d416-47bf-95b7-fff05c1465b7
2025-02-28 00:02:42.610009+00	2025-02-28 00:06:12.787997+00	f	\N	c8692ed2-3e81-460b-86ac-6a68d505582e	Mercado Primario y Emisión de Valores	\N	f623cc82-d416-47bf-95b7-fff05c1465b7
2025-02-28 00:02:42.628496+00	2025-02-28 00:06:12.808941+00	f	\N	e60e24df-a931-4dd1-a633-47f09b283daf	Mercado Secundario y Operaciones Bursátiles	\N	f623cc82-d416-47bf-95b7-fff05c1465b7
2025-02-28 00:02:42.647227+00	2025-02-28 00:06:12.827822+00	f	\N	8084c4cf-b533-485b-9ee0-80e9fe76ed9c	Gestión de Inversiones y Riesgos en Mercado de Valores	\N	f623cc82-d416-47bf-95b7-fff05c1465b7
2025-02-28 16:46:46.165667+00	2025-02-28 16:46:46.165676+00	f	\N	c6a26fb0-9e7f-4cc8-a4dc-1bfb7fdc86d6	Fundamentos Matemáticos y Estadísticos	\N	f3dbb697-1724-4106-93c2-96de14cf36ce
2025-02-28 16:46:46.177017+00	2025-02-28 16:46:46.177026+00	f	\N	390f646b-0bc5-4b13-a931-0dcd3faaec63	Fundamentos Económicos	\N	f3dbb697-1724-4106-93c2-96de14cf36ce
2025-02-28 16:46:46.195424+00	2025-02-28 16:46:46.195431+00	f	\N	2b380963-d44e-4770-b012-55ebb460b7d7	Fundamentos Administrativos	\N	f3dbb697-1724-4106-93c2-96de14cf36ce
2025-03-03 23:10:05.044449+00	2025-03-03 23:10:05.044459+00	f	\N	0aaf0d3f-68ca-44dc-96a2-1b31614b4625	Microeconomía	\N	51193e10-7a3d-43ec-b971-0573f138ca7d
2025-03-03 23:10:05.056357+00	2025-03-03 23:10:05.056364+00	f	\N	a869c806-2ac8-422d-8a2c-35ea1b7a61f3	Macroeconomía	\N	51193e10-7a3d-43ec-b971-0573f138ca7d
2025-03-03 23:10:05.068169+00	2025-03-03 23:10:05.068177+00	f	\N	b0a76e72-7b57-44e4-8e76-3c0560581c09	Finanzas	\N	51193e10-7a3d-43ec-b971-0573f138ca7d
2025-03-03 23:10:05.080416+00	2025-03-03 23:10:05.080423+00	f	\N	ebb9614f-9ea1-4ef5-8047-46896ee52eaa	Cuantitativo	\N	51193e10-7a3d-43ec-b971-0573f138ca7d
2025-02-27 23:53:18.367075+00	2025-03-06 17:43:29.00672+00	f	\N	713d38ee-278b-4df1-81aa-ad6c9d2835da	Introducción a la Gestión de Riesgos	\N	7829808c-71ff-45a0-9dd6-ca90e01447ea
2025-02-27 23:53:18.389823+00	2025-03-06 17:43:29.032843+00	f	\N	48cc2cfc-2c29-4327-8d23-717861c835a2	Riesgo de Mercado	\N	7829808c-71ff-45a0-9dd6-ca90e01447ea
2025-02-27 23:53:18.404257+00	2025-03-06 17:43:29.048441+00	f	\N	6f54cd90-1b03-4ab1-8dd6-d7bb6041d2ee	Riesgo de Crédito	\N	7829808c-71ff-45a0-9dd6-ca90e01447ea
2025-02-27 23:53:18.421614+00	2025-03-06 17:43:29.069811+00	f	\N	ec0ebb80-75ca-4489-a09f-b4fd6bf365bc	Riesgo de Liquidez	\N	7829808c-71ff-45a0-9dd6-ca90e01447ea
2025-02-27 23:53:18.436192+00	2025-03-06 17:43:29.088105+00	f	\N	0900e8ff-91e1-457a-992e-32e47421b6bc	Riesgo Operacional	\N	7829808c-71ff-45a0-9dd6-ca90e01447ea
2025-05-17 03:39:01.816677+00	2025-05-18 01:21:40.457302+00	f	\N	eb1d9272-e64e-4a68-bf05-b96c980ef680	Fundamentos en Ciencia de Datos	\N	86629ba1-3a42-407d-83c3-32d1f7171e67
2025-05-17 03:39:01.84826+00	2025-05-18 01:21:40.485375+00	f	\N	eaa24f4b-0422-4ff6-b351-d2d883ce9596	Aprendizaje Supervisado 	\N	86629ba1-3a42-407d-83c3-32d1f7171e67
2025-05-17 03:39:01.871851+00	2025-05-18 01:21:40.507424+00	f	\N	e34a3812-fcc7-4018-bdf9-c315ff4bb2eb	Aprendizaje No Supervisado	\N	86629ba1-3a42-407d-83c3-32d1f7171e67
2025-05-17 03:39:01.893232+00	2025-05-18 01:21:40.523944+00	f	\N	f39c4d44-2da9-40a3-a8f5-9fea840db4be	Deep Learning e Inteligencia Artificial \t	\N	86629ba1-3a42-407d-83c3-32d1f7171e67
2025-04-08 23:37:03.921538+00	2025-04-08 23:37:03.921549+00	f	\N	e0649540-1293-4d00-a576-893e4305c308	Microeconomía	\N	9785aa42-1580-4ac0-8426-0dbce95614fd
2025-04-08 23:37:03.935262+00	2025-04-08 23:37:03.935274+00	f	\N	8bfd62f3-1dcc-4141-bce7-791188497173	Macroeconomía	\N	9785aa42-1580-4ac0-8426-0dbce95614fd
2025-04-08 23:37:03.950238+00	2025-04-08 23:37:03.950248+00	f	\N	8e6a08b8-4be3-48f3-84a3-ab1602bd196b	Finanzas	\N	9785aa42-1580-4ac0-8426-0dbce95614fd
2025-04-08 23:37:03.963729+00	2025-04-08 23:37:03.963737+00	f	\N	b3b867a3-c066-409f-b2a9-589a7a6b4640	Cuantitativo	\N	9785aa42-1580-4ac0-8426-0dbce95614fd
2025-03-28 23:28:00.175331+00	2025-05-16 21:34:45.470176+00	f	\N	352c8079-5e0a-4335-88ee-533ddbe3b440	Finanzas Corporativas	\N	3424b4ac-987e-4ed4-9890-fa79603f8789
2025-03-28 23:28:00.177613+00	2025-05-16 21:34:45.49567+00	f	\N	d26b0162-05aa-4e51-b9b5-a5f75a17587c	Gestión de Inversiones	\N	3424b4ac-987e-4ed4-9890-fa79603f8789
2025-03-28 23:28:00.179195+00	2025-05-16 21:34:45.520985+00	f	\N	490d24d3-4879-44b1-b5aa-025b10fc6f57	Gestión de  Riesgos  Financieros	\N	3424b4ac-987e-4ed4-9890-fa79603f8789
2025-05-16 22:05:03.380462+00	2025-05-16 22:05:03.380473+00	f	\N	ca64651a-baaf-45f1-8a07-e4728e077f72	Finanzas	\N	42114c08-4ba1-46d1-a930-09ac632c09f9
2025-05-16 22:05:03.383053+00	2025-05-16 22:05:03.383061+00	f	\N	3bd46d16-38a3-4006-9979-db0c231b024a	Macroeconomía	\N	42114c08-4ba1-46d1-a930-09ac632c09f9
2025-05-16 22:05:03.38488+00	2025-05-16 22:05:03.384889+00	f	\N	42e91ff6-e808-47b7-acac-169da061d96a	Aptitud Académica y Actualidad	\N	42114c08-4ba1-46d1-a930-09ac632c09f9
2025-05-16 22:05:03.386867+00	2025-05-16 22:05:03.386877+00	f	\N	b2bfd963-d2b2-4493-a4c0-e85f75db553b	Microeconomía	\N	42114c08-4ba1-46d1-a930-09ac632c09f9
2025-05-16 22:05:03.38893+00	2025-05-16 22:05:03.388941+00	f	\N	e0d5921b-b617-4a8a-8719-f8d8a2f33ca5	Estadística y Economía	\N	42114c08-4ba1-46d1-a930-09ac632c09f9
2025-05-18 04:03:51.393435+00	2025-05-18 04:12:50.087687+00	f	\N	ac632844-63b1-453b-a640-ae1db8366944	Introducción a la Gestión de Riesgos	\N	2949a0bb-dd02-4312-b17f-c7d893d28de1
2025-05-18 04:03:51.420608+00	2025-05-18 04:12:50.117208+00	f	\N	59767960-f9ce-4a2c-9b96-ec24548ffb10	Riesgo de Mercado	\N	2949a0bb-dd02-4312-b17f-c7d893d28de1
2025-05-18 04:03:51.445958+00	2025-05-18 04:12:50.14887+00	f	\N	217ed8e0-b66b-4f7e-bb64-b4e49c97fcd8	Riesgo de Crédito	\N	2949a0bb-dd02-4312-b17f-c7d893d28de1
2025-05-18 04:03:51.469482+00	2025-05-18 04:12:50.176735+00	f	\N	106e5f83-4dae-4a5e-8bab-053357ada930	Riesgo de Liquidez, ALM y Contraparte	\N	2949a0bb-dd02-4312-b17f-c7d893d28de1
2025-05-18 04:03:51.480829+00	2025-05-18 04:12:50.190265+00	f	\N	33017d2a-d019-4c0f-8ea8-f2a56f974478	Gestión de ALM	\N	2949a0bb-dd02-4312-b17f-c7d893d28de1
2025-05-18 04:03:51.489052+00	2025-05-18 04:12:50.198854+00	f	\N	35776dda-f413-4df3-8a05-1c9c5d2ae99c	Riesgo de Contraparte	\N	2949a0bb-dd02-4312-b17f-c7d893d28de1
2025-05-18 04:03:51.49756+00	2025-05-18 04:12:50.209793+00	f	\N	9e13a45c-e6ec-4e1f-a75b-607961101f3e	Riesgo Operacional 	\N	2949a0bb-dd02-4312-b17f-c7d893d28de1
2025-05-21 20:49:35.465039+00	2025-05-21 20:49:35.465048+00	f	\N	38bb636f-5dda-45e3-9e28-3c2e8b9423b0	Fundamentos Financieros y Aplicaciones	\N	a6e7d9c9-64d3-4565-a7cb-029e45e90a49
2025-05-21 20:49:35.483237+00	2025-05-21 20:49:35.483245+00	f	\N	4095aed4-4e2b-4ed2-9388-331b2c87e016	Gestión Financiera Corporativa 	\N	a6e7d9c9-64d3-4565-a7cb-029e45e90a49
2025-05-21 20:49:35.494089+00	2025-05-21 20:49:35.494097+00	f	\N	dcc89059-c1b7-4141-b525-ecbd55f357dd	Mercados Financieros 	\N	a6e7d9c9-64d3-4565-a7cb-029e45e90a49
2025-05-21 20:49:35.507305+00	2025-05-21 20:49:35.507314+00	f	\N	ad29351a-2ccc-406a-8469-a16c90ded53d	Estrategia Financiera Corporativa 	\N	a6e7d9c9-64d3-4565-a7cb-029e45e90a49
\.


--
-- Data for Name: core_order; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_order (created_at, updated_at, deleted, deleted_at, oid, deleted_by_id, owner_id, agreed_total, has_full_scholarship, interested_at, is_international, lost_at, prospect_at, sales_agent_id, stage, to_pay_at, sold_at) FROM stdin;
\.


--
-- Data for Name: core_order_benefits; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_order_benefits (id, order_id, benefit_id) FROM stdin;
\.


--
-- Data for Name: core_order_lead_sources; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_order_lead_sources (id, order_id, leadsource_id) FROM stdin;
\.


--
-- Data for Name: core_orderitem; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_orderitem (created_at, updated_at, deleted, deleted_at, quantity, deleted_by_id, offering_id, order_id, custom_amount, id) FROM stdin;
\.


--
-- Data for Name: core_partnership; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_partnership (created_at, updated_at, deleted, deleted_at, pid, name, description, deleted_by_id, institution_id, delegate_id) FROM stdin;
\.


--
-- Data for Name: core_payment; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_payment (created_at, updated_at, deleted, deleted_at, pid, is_paid, amount, payment_date, ext_payment_id, currency, deleted_by_id, order_id, voucher_id, payment_method_id, is_first_payment, is_refund, observations, scheduled_payment_date) FROM stdin;
\.


--
-- Data for Name: core_paymentmethod; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_paymentmethod (created_at, updated_at, deleted, deleted_at, pmid, name, description, deleted_by_id) FROM stdin;
\.


--
-- Data for Name: core_session; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_session (created_at, updated_at, deleted, deleted_at, sid, title, description, deleted_by_id, topic_id) FROM stdin;
\.


--
-- Data for Name: core_sessionresource; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_sessionresource (created_at, updated_at, deleted, deleted_at, rid, title, resource_type, description, deleted_by_id, file_id, session_id) FROM stdin;
\.


--
-- Data for Name: core_template; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_template (created_at, updated_at, deleted, deleted_at, tid, name, status, header_image_meta_url, body_text, positional_params_example, buttons, deleted_by_id, header_image_id) FROM stdin;
\.


--
-- Data for Name: core_term; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_term (created_at, updated_at, deleted, deleted_at, tid, name, deleted_by_id) FROM stdin;
\.


--
-- Data for Name: core_testimonial; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_testimonial (created_at, updated_at, deleted, deleted_at, "order", tid, author_name, author_title, content, status, author_photo_id, deleted_by_id) FROM stdin;
\.


--
-- Data for Name: core_topic; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_topic (created_at, updated_at, deleted, deleted_at, tid, title, course_id, deleted_by_id) FROM stdin;
2025-04-08 23:37:03.925506+00	2025-04-08 23:37:03.925534+00	f	\N	c373716e-8ca8-4785-89d8-7c111992d8d7	Teoría de consumidor 	986e3c29-3d5f-4c32-aa07-b5146f69c795	\N
2025-04-08 23:37:03.926318+00	2025-04-08 23:37:03.926325+00	f	\N	8cc9e5f8-9b07-446a-9ca2-490ab411ecb8	Teoría del productor	986e3c29-3d5f-4c32-aa07-b5146f69c795	\N
2025-02-28 00:02:42.587091+00	2025-02-28 00:06:12.767092+00	f	\N	9f5ab6f1-4267-4971-a4e3-72dbb2d9c919	Segmentación del mercado de valores	6e6ab76d-2e5b-4af7-9cc4-918c3e17da27	\N
2025-02-28 00:02:42.588017+00	2025-02-28 00:06:12.767752+00	f	\N	7b6df797-3c91-4180-a428-250ac6f17f64	Mercados de capitales vs. mercados monetarios	6e6ab76d-2e5b-4af7-9cc4-918c3e17da27	\N
2025-02-28 00:02:42.591304+00	2025-02-28 00:06:12.770414+00	f	\N	b86c53ac-c3ef-437d-9d4a-179637aa2c29	Agentes del mercado: emisores, inversionistas, intermediarios	bd526af2-16e7-4840-9f00-fc44894530f9	\N
2025-02-28 00:02:42.592127+00	2025-02-28 00:06:12.771107+00	f	\N	704610e9-a32d-42e0-94f6-8022ebd1dca6	Bolsa de Valores vs. Mercado Extrabursátil (OTC)	bd526af2-16e7-4840-9f00-fc44894530f9	\N
2025-02-28 00:02:42.594536+00	2025-02-28 00:06:12.773458+00	f	\N	d69cce26-e875-4172-abd8-3a305624a7d4	Reguladores en Perú (SMV, BCRP), España (CNMV), y Latinoamérica	2f3be383-b65a-408d-b02d-1d8fb0e50b20	\N
2025-02-28 00:02:42.59522+00	2025-02-28 00:06:12.77412+00	f	\N	3b2b388a-9057-46ce-bb85-2cdc1772a644	Normativa Basilea y MiFID II	2f3be383-b65a-408d-b02d-1d8fb0e50b20	\N
2025-02-28 00:02:42.59805+00	2025-02-28 00:06:12.77642+00	f	\N	ffea2f1b-ef70-41d3-b1d2-7d7cda4ce610	Matemática y Estadística Financiera	3a62f01b-8f1e-4be1-84d6-3e1a6d4db6a5	\N
2025-02-27 23:53:18.371951+00	2025-03-06 17:43:29.010353+00	f	\N	13385c30-6850-4915-97c7-d57a03b39dc2	Introducción al COSO, Basilea III, ISO 31000	ce0ce6f5-7dc6-4ade-b966-f3fbcbddea1d	\N
2025-02-27 23:53:18.372848+00	2025-03-06 17:43:29.01111+00	f	\N	2ae215b1-3b61-4153-8ff5-ab3ef5901900	Definición de apetito y tolerancia al riesgo	ce0ce6f5-7dc6-4ade-b966-f3fbcbddea1d	\N
2025-02-27 23:53:18.375289+00	2025-03-06 17:43:29.013628+00	f	\N	f0a4ea8c-af8a-40a1-96ff-8231ac004733	Distribuciones (normal, t-student, etc.)	a04b27fc-8235-498e-9ec4-8a1e2e861808	\N
2025-02-27 23:53:18.376573+00	2025-03-06 17:43:29.015095+00	f	\N	442ca990-5dc4-4232-9259-57e04ac9a453	Simulación de Monte Carlo	a04b27fc-8235-498e-9ec4-8a1e2e861808	\N
2025-02-27 23:53:18.378759+00	2025-03-06 17:43:29.017506+00	f	\N	c18ebbbf-59af-40dd-a813-1e2efc621b45	Modelos ARIMA, GARCH	8c6cc7a6-cb05-4be8-a1aa-e58316d72405	\N
2025-02-27 23:53:18.379373+00	2025-03-06 17:43:29.018213+00	f	\N	24704d8b-23e2-4c28-9792-8856f93b5d2f	Series temporales financieras	8c6cc7a6-cb05-4be8-a1aa-e58316d72405	\N
2025-02-27 23:53:18.379954+00	2025-03-06 17:43:29.018911+00	f	\N	ddb3de96-b197-4275-a783-2913664e41ed	Modelos de cointegración	8c6cc7a6-cb05-4be8-a1aa-e58316d72405	\N
2025-02-27 23:53:18.38202+00	2025-03-06 17:43:29.02298+00	f	\N	77baa24b-3547-4dc3-a5a1-0f4f9300a6f7	Identificación de factores (tasas de interés, divisas, inflación, crédito)	79a8441b-a4c1-45c9-807b-d82781fca5bd	\N
2025-02-27 23:53:18.38263+00	2025-03-06 17:43:29.023731+00	f	\N	f4453ffc-951c-4a9f-8626-05b305b9bf4f	Introducción a drivers de riesgo específicos	79a8441b-a4c1-45c9-807b-d82781fca5bd	\N
2025-02-27 23:53:18.384745+00	2025-03-06 17:43:29.026334+00	f	\N	c3cfec9c-9a7e-45e5-8473-a3b8c3e4c601	Opciones: Black-Scholes, Árbol Binomial	665ef86a-478f-4aed-8aad-f677ead738e1	\N
2025-02-27 23:53:18.385318+00	2025-03-06 17:43:29.027152+00	f	\N	fbfb71b7-63a5-4d9f-b9df-2efc14ef0eb6	Swaps y Forwards.	665ef86a-478f-4aed-8aad-f677ead738e1	\N
2025-02-27 23:53:18.385916+00	2025-03-06 17:43:29.028011+00	f	\N	e3170a17-5cd9-473f-b4e2-594114f5b227	Valoración de bonos y estructurados.	665ef86a-478f-4aed-8aad-f677ead738e1	\N
2025-02-27 23:53:18.388093+00	2025-03-06 17:43:29.030554+00	f	\N	e85f7bdf-da00-4615-8da0-f7d7246efaa4	VaR, Expected Shortfall (ES), Sensibilidades (Delta, Gamma, Vega)	c35f91e2-eb0c-47ba-8c37-a62bb5bd021a	\N
2025-02-27 23:53:18.388675+00	2025-03-06 17:43:29.031474+00	f	\N	dc9b58f7-2144-42cb-956a-94e44c8b115a	Introducción al Riesgo Ajustado al Capital (RAROC)	c35f91e2-eb0c-47ba-8c37-a62bb5bd021a	\N
2025-02-27 23:53:18.392387+00	2025-03-06 17:43:29.036076+00	f	\N	245a962d-af52-4ae7-bc3f-a7286488e853	Basilea III: Marco de riesgos de mercado	79ca9212-534b-43c6-9b43-a65e9f65a600	\N
2025-02-27 23:53:18.392976+00	2025-03-06 17:43:29.036793+00	f	\N	062c1c11-6573-488f-a32f-2a8a507279f2	FRTB (Fundamental Review of the Trading Book)	79ca9212-534b-43c6-9b43-a65e9f65a600	\N
2025-02-27 23:53:18.395149+00	2025-03-06 17:43:29.039397+00	f	\N	b0ccae5f-73bd-42ec-9032-127aa0479830	Métodos históricos, paramétricos y simulaciones	4e37feac-b5a1-4358-a776-4bb831528e5b	\N
2025-02-27 23:53:18.395773+00	2025-03-06 17:43:29.040101+00	f	\N	bc8c0794-eb33-4542-b22e-10d04061d5a3	Aplicación a portafolios multi-activo	4e37feac-b5a1-4358-a776-4bb831528e5b	\N
2025-02-27 23:53:18.399451+00	2025-03-06 17:43:29.042926+00	f	\N	61b03590-8a90-4e18-ad06-c5466073378a	Modelos Vasicek, CIR, Ho-Lee y Hull-White	876ea1bf-18b1-4363-8ec3-1e1ead4c1ff1	\N
2025-02-27 23:53:18.400088+00	2025-03-06 17:43:29.043716+00	f	\N	d7e8eb5f-b1dc-4d11-8396-8748f00f3051	Curvas de tasas de interés	876ea1bf-18b1-4363-8ec3-1e1ead4c1ff1	\N
2025-02-27 23:53:18.402231+00	2025-03-06 17:43:29.046301+00	f	\N	77fe9438-7efe-4a09-86c1-ae02475bc769	Técnicas de validación de modelos	b718b1ac-0631-4e1b-8a94-d270cb645ab9	\N
2025-02-27 23:53:18.403134+00	2025-03-06 17:43:29.047125+00	f	\N	6d5a6d1b-d80b-4bfc-b634-d6fab23533c8	Escenarios de estrés histórico e hipotético	b718b1ac-0631-4e1b-8a94-d270cb645ab9	\N
2025-02-27 23:53:18.406681+00	2025-03-06 17:43:29.051613+00	f	\N	1681b186-b662-478d-8c0a-11da30a4115d	Basilea III: CAR, provisiones, RWAs	b8345614-36e5-472a-83fd-35a9a6f0f180	\N
2025-02-27 23:53:18.407625+00	2025-03-06 17:43:29.052323+00	f	\N	abbadf19-734e-47a9-9519-ff9cf794244a	IFRS 9: ECL	b8345614-36e5-472a-83fd-35a9a6f0f180	\N
2025-02-27 23:53:18.409989+00	2025-03-06 17:43:29.054814+00	f	\N	ea3ae52b-0bfc-4353-8e2d-ed19aa24096a	Logit, probit y discriminante lineal	e8a7e344-5e13-4884-8523-ce67e9cb01df	\N
2025-02-27 23:53:18.410586+00	2025-03-06 17:43:29.055556+00	f	\N	02e15d97-ada8-42bc-8e62-f92fcb78b5f4	Métodos avanzados para LGD y EAD	e8a7e344-5e13-4884-8523-ce67e9cb01df	\N
2025-02-27 23:53:18.412849+00	2025-03-06 17:43:29.058209+00	f	\N	44676f27-60e2-4a77-9b8a-5b418659ad2c	Árboles de decisión, redes neuronales	e6fe6072-8dac-4c07-975a-c6cf7be534b3	\N
2025-02-27 23:53:18.413495+00	2025-03-06 17:43:29.058967+00	f	\N	adffd58f-5dde-4262-9024-87cc9fbe8798	Clustering y análisis de big data en crédito	e6fe6072-8dac-4c07-975a-c6cf7be534b3	\N
2025-02-27 23:53:18.414266+00	2025-03-06 17:43:29.059728+00	f	\N	fae24106-4f65-4ecc-8943-d79aa670ec98	Derivados de riesgo de crédito	e6fe6072-8dac-4c07-975a-c6cf7be534b3	\N
2025-02-27 23:53:18.416571+00	2025-03-06 17:43:29.062509+00	f	\N	24fc8594-3124-480b-ae05-4c873a527768	Validación de modelos ECL	82942d7e-f609-47f1-a84c-bba029ee3dfc	\N
2025-02-27 23:53:18.417167+00	2025-03-06 17:43:29.063404+00	f	\N	0dabd18e-893d-4e5a-9956-2c78c09501a1	Simulación de escenarios de crisis	82942d7e-f609-47f1-a84c-bba029ee3dfc	\N
2025-02-27 23:53:18.419347+00	2025-03-06 17:43:29.066341+00	f	\N	be0e23b4-777f-4f0e-8c39-b72ae93d3461	Modelo de Merton y KMV	c32ed0fa-3342-4b2a-a287-c3a297136df8	\N
2025-02-27 23:53:18.419949+00	2025-03-06 17:43:29.067118+00	f	\N	ca47e2ad-216f-44b5-9d80-9db6707d51b7	CreditMetrics, CreditRisk+	c32ed0fa-3342-4b2a-a287-c3a297136df8	\N
2025-02-27 23:53:18.420492+00	2025-03-06 17:43:29.067981+00	f	\N	9e81601e-ddac-4a3c-94f3-557e6a16abbe	Transiciones de rating (matrices de migración)	c32ed0fa-3342-4b2a-a287-c3a297136df8	\N
2025-02-27 23:53:18.424749+00	2025-03-06 17:43:29.074208+00	f	\N	3eab57c9-7082-426a-b5da-35f7145dae6f	Rol del regulador y reportes regulatorios	fc9d8ad9-c631-447d-ba77-e5a919540f44	\N
2025-02-27 23:53:18.426929+00	2025-03-06 17:43:29.076634+00	f	\N	8745cfc7-a622-4582-8f14-fddbc9f79102	Gap Analysis: Análisis de descalce de plazos (maturity mismatch)	8919f2d8-c525-4d65-b8eb-7f924f77fd08	\N
2025-02-27 23:53:18.42755+00	2025-03-06 17:43:29.077323+00	f	\N	7a7c15db-8ae2-48b3-a495-6a6582c47a8c	Modelos de cash flow forecasting y estrés de liquidez	8919f2d8-c525-4d65-b8eb-7f924f77fd08	\N
2025-02-27 23:53:18.429591+00	2025-03-06 17:43:29.079895+00	f	\N	5c00c057-68bf-4473-80d7-ed3a57f44a1a	Estrategias de cobertura de liquidez	f8afd76e-4c0e-424a-b250-888f5880ba96	\N
2025-02-27 23:53:18.430114+00	2025-03-06 17:43:29.080598+00	f	\N	1a27edb8-0a4a-4820-a88b-1b2864a90b6f	Curvas de liquidez y sus aplicaciones en ALM	f8afd76e-4c0e-424a-b250-888f5880ba96	\N
2025-02-27 23:53:18.432097+00	2025-03-06 17:43:29.082978+00	f	\N	d848a40d-2dac-4825-a921-e4234431ecda	Impacto del bid-ask spread y slippage	25637ea5-d676-4631-9d02-a8cead99e398	\N
2025-02-27 23:53:18.432644+00	2025-03-06 17:43:29.083779+00	f	\N	19b4ce9f-060c-4a31-8ae2-938c7cffa496	Métricas de valor en riesgo de liquidez (LVaR)	25637ea5-d676-4631-9d02-a8cead99e398	\N
2025-02-27 23:53:18.434657+00	2025-03-06 17:43:29.086183+00	f	\N	3c4cf5d6-6ef2-42bc-83c8-66e3bb63310a	Escenarios de estrés para crisis de liquidez	10ebd51f-581a-4699-a6dc-f0ad58981882	\N
2025-02-27 23:53:18.435201+00	2025-03-06 17:43:29.086859+00	f	\N	e2a4a4e8-05ee-463c-839d-f0044161711d	Casos históricos de crisis de liquidez: lecciones aprendidas	10ebd51f-581a-4699-a6dc-f0ad58981882	\N
2025-02-27 23:53:18.439102+00	2025-03-06 17:43:29.090867+00	f	\N	ed6cc9f5-d74e-4e2b-b71c-b881fafa1970	Marco regulatorio (Basilea II y III)	9af7ba51-7a98-4b01-9fd6-11b541967355	\N
2025-02-27 23:53:18.439715+00	2025-03-06 17:43:29.091609+00	f	\N	8c3b3de6-5116-4b68-8a67-83ec3db5d4ca	Indicadores clave de riesgo (KRIs) en RO	9af7ba51-7a98-4b01-9fd6-11b541967355	\N
2025-02-27 23:53:18.441916+00	2025-03-06 17:43:29.093981+00	f	\N	cd92e619-5dca-4759-93c4-5ff5f8dd6dfe	Mapas de calor: Matrices de probabilidad e impacto	b5eaf4f0-72ab-4132-891b-12b7b69838f5	\N
2025-02-27 23:53:18.4426+00	2025-03-06 17:43:29.094632+00	f	\N	cc60a7b3-eff5-44b5-abe8-d7d88a373565	Análisis de eventos de pérdida operacional y su categorización	b5eaf4f0-72ab-4132-891b-12b7b69838f5	\N
2025-02-27 23:53:18.444733+00	2025-03-06 17:43:29.097064+00	f	\N	80d33b52-0491-4d4d-8364-28cca700db12	Análisis de procesos (BPM) y evaluación de controles	23a6b1e9-e64c-4836-ac85-fc385af85ed2	\N
2025-02-27 23:53:18.445337+00	2025-03-06 17:43:29.097722+00	f	\N	777d0332-5565-4403-b25d-88368c26276c	Entrevistas, talleres y encuestas de riesgo operacional	23a6b1e9-e64c-4836-ac85-fc385af85ed2	\N
2025-02-27 23:53:18.447372+00	2025-03-06 17:43:29.100047+00	f	\N	0c170f8e-f607-485a-ae86-5e059cfc728a	Distribuciones de pérdidas: Modelos LDA (Loss Distribution Approach)	1e4d82ae-2796-421a-b4d3-d30b4cdbb467	\N
2025-02-27 23:53:18.447983+00	2025-03-06 17:43:29.100709+00	f	\N	1d9fd412-5130-4d49-ad9a-a3acf2b769a4	Modelos AMA (Advanced Measurement Approach)	1e4d82ae-2796-421a-b4d3-d30b4cdbb467	\N
2025-02-27 23:53:18.448562+00	2025-03-06 17:43:29.101334+00	f	\N	9256bdde-82f2-4dfd-8083-a3dfb9d8fb23	Aplicación de machine learning para predicción de pérdidas operativas	1e4d82ae-2796-421a-b4d3-d30b4cdbb467	\N
2025-02-27 23:53:18.450937+00	2025-03-06 17:43:29.103691+00	f	\N	991bf7db-95b8-4e18-adbe-6a5afa33773b	Riesgos tecnológicos y cibernéticos: identificación y mitigación	3cc49191-84f1-407f-8c41-2337a8f73225	\N
2025-02-27 23:53:18.451589+00	2025-03-06 17:43:29.104355+00	f	\N	f769041f-cd89-4415-ab44-1838bb716709	Gestión del riesgo en outsourcing y proveedores	3cc49191-84f1-407f-8c41-2337a8f73225	\N
2025-02-27 23:53:18.452273+00	2025-03-06 17:43:29.105022+00	f	\N	1e09fd72-9d45-4158-8f5e-a6fd4cc645d1	Riesgos ESG (ambiental, social y de gobernanza) aplicados a RO	3cc49191-84f1-407f-8c41-2337a8f73225	\N
2025-02-28 00:02:42.585773+00	2025-02-28 00:06:12.766246+00	f	\N	e4d9e89d-c7ae-481e-96d8-b2ddba8cb462	Funciones del mercado financiero	6e6ab76d-2e5b-4af7-9cc4-918c3e17da27	\N
2025-02-28 00:02:42.598732+00	2025-02-28 00:06:12.777166+00	f	\N	7d8eebc6-e5a9-4b47-b1ba-da8ff2918956	Análisis de Estados Financieros	3a62f01b-8f1e-4be1-84d6-3e1a6d4db6a5	\N
2025-02-28 00:02:42.599818+00	2025-02-28 00:06:12.777848+00	f	\N	e637f12f-cd07-42d5-a807-f9b4ee0d0fbd	Finanzas Corporativas	3a62f01b-8f1e-4be1-84d6-3e1a6d4db6a5	\N
2025-02-28 00:02:42.602404+00	2025-02-28 00:06:12.780226+00	f	\N	74f58757-7f83-44e7-b880-9e77b5c1c598	Valorización de acciones, bonos y derivados.	2cc69436-cd8c-400e-af0f-552fae24749a	\N
2025-02-28 00:02:42.603139+00	2025-02-28 00:06:12.780899+00	f	\N	b9a89a6e-1036-4b85-9076-e997774457ef	Fondos mutuos, fondos de inversión y productos estructurados.	2cc69436-cd8c-400e-af0f-552fae24749a	\N
2025-02-28 00:02:42.605597+00	2025-02-28 00:06:12.783211+00	f	\N	ce8599a1-3258-4af4-9006-246b1232cbea	Análisis Fundamental: Estados financieros, múltiplos, valoración	a798ee80-2b44-451d-b97a-70dbe56e8b19	\N
2025-02-28 00:02:42.606183+00	2025-02-28 00:06:12.78386+00	f	\N	a6fd7972-2dbd-46a9-afb3-ed2de6dd1f90	Análisis Técnico: Indicadores y patrones de trading	a798ee80-2b44-451d-b97a-70dbe56e8b19	\N
2025-02-28 00:02:42.608229+00	2025-02-28 00:06:12.786143+00	f	\N	4e0fad64-459e-49e7-b43d-2d43d463bc12	Diversificación y optimización de portafolios	e304f94b-8c04-427b-bbb9-aab5667ea975	\N
2025-02-28 00:02:42.608851+00	2025-02-28 00:06:12.786821+00	f	\N	acb3103f-db08-44dd-96ec-9d75acf8a2d9	Modelo de Markowitz y CAPM	e304f94b-8c04-427b-bbb9-aab5667ea975	\N
2025-02-28 00:02:42.61268+00	2025-02-28 00:06:12.791009+00	f	\N	e487a17d-3be0-442a-b186-56184b308eba	Tipos de mercados y actores clave	8d1b5988-223e-4101-892a-9d9173031df3	\N
2025-02-28 00:02:42.613324+00	2025-02-28 00:06:12.791684+00	f	\N	120e77b8-4705-45e1-a29d-a0c5987d20e4	Emisión de valores y captación de capital	8d1b5988-223e-4101-892a-9d9173031df3	\N
2025-02-28 00:02:42.615458+00	2025-02-28 00:06:12.794221+00	f	\N	6b406228-896e-4fba-a953-c529e40216c1	Normativa sobre emisiones públicas y privadas	156096de-b4ec-418e-b8b7-527520b31316	\N
2025-02-28 00:02:42.616191+00	2025-02-28 00:06:12.795015+00	f	\N	a1cc351c-0491-4422-b1fc-d79c265db697	Regulación de ofertas públicas (OPV, OPS, OPA, OPC, OPI)	156096de-b4ec-418e-b8b7-527520b31316	\N
2025-02-28 00:02:42.618447+00	2025-02-28 00:06:12.797501+00	f	\N	cfaf7ff8-f53d-4ea2-be28-d5b5a6598292	Proceso de emisión de bonos	ad8de2cc-aef1-408a-b4f8-0095ae212233	\N
2025-02-28 00:02:42.61905+00	2025-02-28 00:06:12.798283+00	f	\N	8dbc72ce-ae20-4883-8668-46918e30bacd	Oferta de acciones y financiamiento en bolsa	ad8de2cc-aef1-408a-b4f8-0095ae212233	\N
2025-02-28 00:02:42.621168+00	2025-02-28 00:06:12.800996+00	f	\N	aa7aba78-57df-4bd5-9072-e55e29815566	Mecanismos de registro y divulgación de información	e19b913e-89e1-4b4b-b3be-f045c1499d76	\N
2025-02-28 00:02:42.621771+00	2025-02-28 00:06:12.801702+00	f	\N	7f188d9f-e4e6-4d66-9af1-480d97fa85a0	Supervisión de emisores y cumplimiento regulatorio	e19b913e-89e1-4b4b-b3be-f045c1499d76	\N
2025-02-28 00:02:42.624048+00	2025-02-28 00:06:12.804112+00	f	\N	054c8c13-936b-440f-904f-f777413869b6	Modelos de rating y evaluación crediticia	9086f83d-2a0c-4990-a69e-6e38ea98c08e	\N
2025-02-28 00:02:42.624683+00	2025-02-28 00:06:12.804823+00	f	\N	9bf8dddd-ffea-49d6-9a11-1a13cb012606	Impacto del riesgo de crédito en la valoración de activos	9086f83d-2a0c-4990-a69e-6e38ea98c08e	\N
2025-02-28 00:02:42.62682+00	2025-02-28 00:06:12.807066+00	f	\N	cca19d99-6867-4133-bc40-c1d9f6e5dcd4	Tokenización de activos y securitización	bc273305-2e13-45e5-9fa6-fe595e97865d	\N
2025-02-28 00:02:42.62739+00	2025-02-28 00:06:12.807706+00	f	\N	a7b4ace9-bf64-4a75-b4dd-09591183d195	Financiamiento sostenible (bonos verdes y sociales)	bc273305-2e13-45e5-9fa6-fe595e97865d	\N
2025-02-28 00:02:42.631354+00	2025-02-28 00:06:12.811582+00	f	\N	eb49da3e-f8d9-4407-8813-5a7e66d4372f	Bolsas de valores en Perú (BVL), España (BME) y LATAM	81e949d8-1ac1-432e-9212-f056f7aa19e8	\N
2025-02-28 00:02:42.631967+00	2025-02-28 00:06:12.812283+00	f	\N	9415a65d-694a-4956-ad08-2120ccf1de84	Roles de las SAB (Sociedades Agentes de Bolsa) y CAVALI	81e949d8-1ac1-432e-9212-f056f7aa19e8	\N
2025-02-28 00:02:42.63405+00	2025-02-28 00:06:12.814448+00	f	\N	d4492384-ab9a-447b-a42b-93d4d190744a	Regulaciones sobre negociación y transparencia	48e34863-cfdd-48db-96de-983c5287890a	\N
2025-02-28 00:02:42.634714+00	2025-02-28 00:06:12.815082+00	f	\N	e1b38751-b636-44c4-8987-4d9a7884ac64	Normativa sobre manipulación de mercado e información privilegiada	48e34863-cfdd-48db-96de-983c5287890a	\N
2025-02-28 00:02:42.636839+00	2025-02-28 00:06:12.817275+00	f	\N	ee8e02b4-0726-44c8-a689-1756ddcc9617	Tipos de órdenes y ejecución en bolsa	07b83762-3c47-46a2-9212-4f8224756e97	\N
2025-02-28 00:02:42.63748+00	2025-02-28 00:06:12.817917+00	f	\N	fbe8da90-a2d6-4d05-a788-7daaa296ac3e	High-Frequency Trading (HFT) y algoritmos de negociación	07b83762-3c47-46a2-9212-4f8224756e97	\N
2025-02-28 00:02:42.639615+00	2025-02-28 00:06:12.820197+00	f	\N	9f10a628-c5ac-444a-bd55-bbe205e8124b	Fondos mutuos, fondos de inversión y fideicomisos	f1950eb6-b2a2-4dd6-b09c-e58270d13edb	\N
2025-02-28 00:02:42.640322+00	2025-02-28 00:06:12.820897+00	f	\N	522c46ac-8cb5-4aac-8699-2d33b24ca0bd	Evaluación de rentabilidad y riesgo	f1950eb6-b2a2-4dd6-b09c-e58270d13edb	\N
2025-02-28 00:02:42.642624+00	2025-02-28 00:06:12.823053+00	f	\N	417df78b-5259-4e61-bca1-934ad9333d39	Plataformas de crowdfunding en Latinoamérica y Europa	6c3801bb-e79c-4a1d-bd13-109d667ff620	\N
2025-02-28 00:02:42.643283+00	2025-02-28 00:06:12.823655+00	f	\N	71e70b62-7af2-44dd-a4fc-7e59774a1539	Regulaciones y modelos de negocio	6c3801bb-e79c-4a1d-bd13-109d667ff620	\N
2025-02-28 00:02:42.645442+00	2025-02-28 00:06:12.825871+00	f	\N	138d1409-0e5f-4199-a32a-d45b3a92e8ab	Factoring, Real Estate y Private Equity	e0078787-1881-491c-8200-e2da17e35c3f	\N
2025-02-28 00:02:42.645989+00	2025-02-28 00:06:12.826584+00	f	\N	f4ce1638-74bf-4c48-b58b-52c1ab35b427	Opciones y futuros sobre índices y materias primas	e0078787-1881-491c-8200-e2da17e35c3f	\N
2025-02-28 00:02:42.650069+00	2025-02-28 00:06:12.830598+00	f	\N	72ed47c6-5ed5-4382-8bb6-26d1f03623da	Asset Allocation y estrategias de inversión	7e751af6-806d-4a9d-aa34-a7ac8408a91d	\N
2025-02-28 00:02:42.65063+00	2025-02-28 00:06:12.831225+00	f	\N	d8e6ccd3-6d19-489e-a493-bf5da72bb45e	Modelo de Sharpe y análisis de rentabilidad ajustada a riesgo	7e751af6-806d-4a9d-aa34-a7ac8408a91d	\N
2025-02-28 00:02:42.652719+00	2025-02-28 00:06:12.83333+00	f	\N	5bb076a0-36d2-4a11-a6d8-45a549293a69	VaR, CVaR y análisis de sensibilidad	82704b36-ff13-43fe-820c-a394a25cd547	\N
2025-02-28 00:02:42.653313+00	2025-02-28 00:06:12.833951+00	f	\N	61a4569e-bbed-4cc0-b12f-fd5b1f54d77c	Estrategias de cobertura con derivados	82704b36-ff13-43fe-820c-a394a25cd547	\N
2025-02-28 00:02:42.655293+00	2025-02-28 00:06:12.836063+00	f	\N	8419fc5c-ea35-4a52-80cd-008332611df2	Inversión responsable y criterios ESG	ac06058b-5e27-46cb-940c-f51f83aef834	\N
2025-02-28 00:02:42.65586+00	2025-02-28 00:06:12.836741+00	f	\N	5a4ed6fd-96e4-4a0c-b755-88b8a5d30dee	Impacto del cambio climático en los mercados	ac06058b-5e27-46cb-940c-f51f83aef834	\N
2025-02-28 00:02:42.658061+00	2025-02-28 00:06:12.838901+00	f	\N	e383a624-3b94-4972-b934-3d4c78504d84	Protección al inversionista y transparencia	f30907c0-64c4-46d8-9b01-5f16416faaf1	\N
2025-02-28 00:02:42.658698+00	2025-02-28 00:06:12.839508+00	f	\N	fc6d0b6d-5663-4596-8851-f0eeaea03c8b	Prevención del lavado de activos en el mercado de valores	f30907c0-64c4-46d8-9b01-5f16416faaf1	\N
2025-02-28 00:02:42.660835+00	2025-02-28 00:06:12.84172+00	f	\N	174df22b-e7ef-4f6c-9dbf-f29aa593e348	Blockchain, Smart Contracts y Criptoactivos	b735a94b-c623-4665-907e-741b18a86008	\N
2025-02-28 00:02:42.661421+00	2025-02-28 00:06:12.842316+00	f	\N	9a7aaa4c-8de5-46d9-b3d9-0cad12d52965	Robo-Advisors y tendencias en inversión digital	b735a94b-c623-4665-907e-741b18a86008	\N
2025-02-28 00:02:42.663495+00	2025-02-28 00:06:12.844397+00	f	\N	1fd90844-5317-423b-941a-b7b760abad60	Análisis de portafolios con datos reales	54823bd6-2d1d-46d0-a6e0-ff71a1f19ffa	\N
2025-02-28 00:02:42.664146+00	2025-02-28 00:06:12.845078+00	f	\N	e5015941-98ba-495d-9feb-7f5fb115ea92	Simulación en plataforma de trading	54823bd6-2d1d-46d0-a6e0-ff71a1f19ffa	\N
2025-04-08 23:37:03.927051+00	2025-04-08 23:37:03.927058+00	f	\N	033ee0b5-c6f3-43a6-a64a-6d4d541ebca2	Competencia perfecta	986e3c29-3d5f-4c32-aa07-b5146f69c795	\N
2025-04-08 23:37:03.927711+00	2025-04-08 23:37:03.927718+00	f	\N	107435c8-ee48-4d08-b566-f32147e0a52a	Fallas de mercado	986e3c29-3d5f-4c32-aa07-b5146f69c795	\N
2025-04-08 23:37:03.930096+00	2025-04-08 23:37:03.930104+00	f	\N	75c8a1df-e392-4a55-af18-5987a26206fa	Monopolio	4ad823e1-9974-4ac5-b351-8a5a334ac410	\N
2025-04-08 23:37:03.930828+00	2025-04-08 23:37:03.930836+00	f	\N	23ad46c1-4148-42d3-a3fa-7d5000aa13a1	Oligopolio y teoría de juego	4ad823e1-9974-4ac5-b351-8a5a334ac410	\N
2025-04-08 23:37:03.931537+00	2025-04-08 23:37:03.93155+00	f	\N	5621133c-8183-4207-bcd0-2b9473ebdd85	Externalidades y Bienes Públicos	4ad823e1-9974-4ac5-b351-8a5a334ac410	\N
2025-04-08 23:37:03.932224+00	2025-04-08 23:37:03.932232+00	f	\N	1ad975bf-ff45-49da-8673-26aa886271b1	Teoría de Incertidumbre, Contratos	4ad823e1-9974-4ac5-b351-8a5a334ac410	\N
2025-04-08 23:37:03.932953+00	2025-04-08 23:37:03.932961+00	f	\N	b39ff439-1dad-4491-9712-cf37f03c1de3	Mercado de Factores	4ad823e1-9974-4ac5-b351-8a5a334ac410	\N
2025-04-08 23:37:03.933662+00	2025-04-08 23:37:03.933673+00	f	\N	7e6d83c4-f31a-4bce-8eaf-e3df425546d8	Equilibrio general y Regulación Económica	4ad823e1-9974-4ac5-b351-8a5a334ac410	\N
2025-04-08 23:37:03.939058+00	2025-04-08 23:37:03.939069+00	f	\N	1d08d2f6-e637-4121-9c77-17fa6c920959	Programación Financiera	5a627ec0-bf33-4193-9f46-27efaaaed0a6	\N
2025-04-08 23:37:03.939788+00	2025-04-08 23:37:03.939799+00	f	\N	deaf8bed-74ee-45e6-91c7-23fbe92ed343	Modelo Ahorro-Inversión	5a627ec0-bf33-4193-9f46-27efaaaed0a6	\N
2025-04-08 23:37:03.940538+00	2025-04-08 23:37:03.940549+00	f	\N	21518326-669b-486a-8147-cb67d414e096	Modelo Neoclásico	5a627ec0-bf33-4193-9f46-27efaaaed0a6	\N
2025-04-08 23:37:03.941271+00	2025-04-08 23:37:03.941279+00	f	\N	b6a892a8-9f4e-4ac2-be4f-78428c180211	Modelo Keynesiano	5a627ec0-bf33-4193-9f46-27efaaaed0a6	\N
2025-04-08 23:37:03.941978+00	2025-04-08 23:37:03.941988+00	f	\N	50f65cae-b8d4-485e-90ea-b7cd0ffa41e0	Modelo IS-LM economía cerrada	5a627ec0-bf33-4193-9f46-27efaaaed0a6	\N
2025-04-08 23:37:03.94269+00	2025-04-08 23:37:03.942701+00	f	\N	aa7c1e32-70be-4d1b-b1bc-7eca35eb77a3	Modelo IS-LM economía abierta	5a627ec0-bf33-4193-9f46-27efaaaed0a6	\N
2025-04-08 23:37:03.945622+00	2025-04-08 23:37:03.945631+00	f	\N	dd0d7963-233b-4c03-bd44-716df5cc1687	Modelos de Crecimiento Económico 	61d19e09-6b9e-4737-a678-a360756109ed	\N
2025-04-08 23:37:03.946339+00	2025-04-08 23:37:03.94635+00	f	\N	cb6931d2-5463-4144-b556-739278c05373	Microfundamentos de la macroeconomía	61d19e09-6b9e-4737-a678-a360756109ed	\N
2025-04-08 23:37:03.94708+00	2025-04-08 23:37:03.947091+00	f	\N	b38b5233-32d2-4f99-a3de-f793e053c29f	Política Fiscal 	61d19e09-6b9e-4737-a678-a360756109ed	\N
2025-04-08 23:37:03.947824+00	2025-04-08 23:37:03.947833+00	f	\N	7f94803c-cf2b-486f-a5c6-82e2666aebf9	Política Monetaria	61d19e09-6b9e-4737-a678-a360756109ed	\N
2025-04-08 23:37:03.948631+00	2025-04-08 23:37:03.948642+00	f	\N	1137f853-329f-4732-9ed8-045d91bf9936	Economía Internacional	61d19e09-6b9e-4737-a678-a360756109ed	\N
2025-04-08 23:37:03.953162+00	2025-04-08 23:37:03.95317+00	f	\N	41239937-7219-4417-a21d-1130d03dd101	Matemática Financiera	65801111-e840-49a7-bdb5-3e3ce6575caf	\N
2025-04-08 23:37:03.953801+00	2025-04-08 23:37:03.953808+00	f	\N	8c35219e-9e9e-44b8-8501-e96fcbb7955e	Contabilidad	65801111-e840-49a7-bdb5-3e3ce6575caf	\N
2025-04-08 23:37:03.954469+00	2025-04-08 23:37:03.954477+00	f	\N	a0ea3c30-5cde-4c05-807b-23fae3fca04d	Análisis de Estados Financieros	65801111-e840-49a7-bdb5-3e3ce6575caf	\N
2025-04-08 23:37:03.955148+00	2025-04-08 23:37:03.955155+00	f	\N	7972205b-b7a0-44ef-bea9-44c6441115ea	Finanzas Corporativas	65801111-e840-49a7-bdb5-3e3ce6575caf	\N
2025-04-08 23:37:03.955805+00	2025-04-08 23:37:03.955813+00	f	\N	2b6e34d1-b527-4e9f-820a-f0eaf33a370f	Valorización de Empresas	65801111-e840-49a7-bdb5-3e3ce6575caf	\N
2025-04-08 23:37:03.956672+00	2025-04-08 23:37:03.95668+00	f	\N	8a78d316-7ecb-4788-997d-e680ba077954	Mercados Financieros	65801111-e840-49a7-bdb5-3e3ce6575caf	\N
2025-04-08 23:37:03.959243+00	2025-04-08 23:37:03.959251+00	f	\N	4740f65b-7d0f-48e1-b13f-6ea0811145cd	Renta Fija	415d2ba9-789c-4e20-9f9b-4a83c67e9569	\N
2025-04-08 23:37:03.959862+00	2025-04-08 23:37:03.959869+00	f	\N	9131e4c4-5071-4e1f-a2b8-9063fe975b52	Renta Variable	415d2ba9-789c-4e20-9f9b-4a83c67e9569	\N
2025-04-08 23:37:03.960549+00	2025-04-08 23:37:03.960557+00	f	\N	9f248169-5206-435a-9b0b-8c3a1d5ce822	Derivados	415d2ba9-789c-4e20-9f9b-4a83c67e9569	\N
2025-04-08 23:37:03.961148+00	2025-04-08 23:37:03.961155+00	f	\N	5e78ca01-5020-40f9-b7a4-23e857929565	Ingeniería Financiera	415d2ba9-789c-4e20-9f9b-4a83c67e9569	\N
2025-04-08 23:37:03.961746+00	2025-04-08 23:37:03.961753+00	f	\N	d0e6a8ea-cbcf-47cb-90a9-13486eddaf70	Teoría de portafolio	415d2ba9-789c-4e20-9f9b-4a83c67e9569	\N
2025-04-08 23:37:03.962374+00	2025-04-08 23:37:03.962382+00	f	\N	f2f296a5-1aff-473c-bd49-531b8696acb8	Gestión de riesgos	415d2ba9-789c-4e20-9f9b-4a83c67e9569	\N
2025-04-08 23:37:03.966606+00	2025-04-08 23:37:03.966613+00	f	\N	6ded143d-dc45-4a45-86fd-3ebafa3077d5	Matemática 1	7f407d20-3d01-48c7-ba1d-0e4c0dfe4aa1	\N
2025-04-08 23:37:03.967206+00	2025-04-08 23:37:03.967214+00	f	\N	462960a6-d295-4040-86ec-fbeb77de9514	Matemática 2	7f407d20-3d01-48c7-ba1d-0e4c0dfe4aa1	\N
2025-04-08 23:37:03.969454+00	2025-04-08 23:37:03.969466+00	f	\N	f59f103c-8308-4451-9ca7-718f24b707c3	Estadística descriptiva	81238da0-13ab-4cd8-9519-9393b5ef8edb	\N
2025-04-08 23:37:03.970146+00	2025-04-08 23:37:03.970154+00	f	\N	ca598d0e-46bb-4e7f-975c-4ec18f0e11ff	Estadística inferencial	81238da0-13ab-4cd8-9519-9393b5ef8edb	\N
2025-04-08 23:37:03.972723+00	2025-04-08 23:37:03.972733+00	f	\N	aa75fe58-2f7e-4b63-a997-2877763c3413	Modelo de Regresión Lineal	78393d54-6641-44b0-ac7d-5f0248cd72b9	\N
2025-04-08 23:37:03.973408+00	2025-04-08 23:37:03.97342+00	f	\N	c095617b-caeb-45f7-918a-3bb19a91e2fc	Modelo de Regresión no Lineal	78393d54-6641-44b0-ac7d-5f0248cd72b9	\N
2025-04-08 23:37:03.974265+00	2025-04-08 23:37:03.974274+00	f	\N	d0ecf760-9057-41c5-9f97-2ec38f14afac	Modelos de Panel data	78393d54-6641-44b0-ac7d-5f0248cd72b9	\N
2025-05-17 03:39:01.832803+00	2025-05-18 01:21:40.470147+00	f	\N	97e32857-8b4f-4100-93b7-5771479f98e9	Sintaxis, funciones, estructuras de datos, NumPy, Pandas	33053657-57f6-4976-abc9-3ead4ecc4559	\N
2025-02-27 23:53:18.375934+00	2025-03-06 17:43:29.014355+00	f	\N	7fdad4a0-7349-49ae-b371-0625d68c4083	Correlación, regresión y análisis multivariado	a04b27fc-8235-498e-9ec4-8a1e2e861808	\N
2025-02-27 23:53:18.424168+00	2025-03-06 17:43:29.073461+00	f	\N	1027fe39-cc8b-45a7-877c-e8d47f93ad89	Basilea III: LCR (Liquidity Coverage Ratio) y NSFR (Net Stable Funding Ratio)	fc9d8ad9-c631-447d-ba77-e5a919540f44	\N
2025-05-18 04:03:51.419433+00	2025-05-18 04:12:50.11548+00	f	\N	d9c5c067-b635-4a10-ac63-3116f1e06b1e	Taller de Valorización de derivados Exóticos	e1da0abe-d410-4987-a99d-b3503e594846	\N
2025-05-18 04:03:51.423372+00	2025-05-18 04:12:50.120778+00	f	\N	fb9fb21e-747d-4222-9c6b-438d506c01a8	Basilea III: Marco de riesgos de mercado	85afc1bc-23f1-4ae0-9437-d69e0594cd52	\N
2025-05-18 04:03:51.423963+00	2025-05-18 04:12:50.121479+00	f	\N	51cf8e7d-3a68-4521-be7f-5d18f4123dcc	IRRBB y FRTB	85afc1bc-23f1-4ae0-9437-d69e0594cd52	\N
2025-05-18 04:03:51.428741+00	2025-05-18 04:12:50.12687+00	f	\N	e5d23a9f-0a14-4fdd-8735-684395ec8120	Bonos	0ad3948e-39bf-4ff4-83ea-7f9bbf54ca0b	\N
2025-05-18 04:03:51.429419+00	2025-05-18 04:12:50.127614+00	f	\N	8ee65b5b-3245-481d-be95-c02d96eaf186	Acciones	0ad3948e-39bf-4ff4-83ea-7f9bbf54ca0b	\N
2025-05-18 04:03:51.430068+00	2025-05-18 04:12:50.128355+00	f	\N	a0db5d0c-59c3-48cc-a4ef-e56144d641a9	Derivados (Forward, Futuros, Swaps y Opciones)	0ad3948e-39bf-4ff4-83ea-7f9bbf54ca0b	\N
2025-05-18 04:03:51.432511+00	2025-05-18 04:12:50.131467+00	f	\N	5d03808f-3c00-4cb3-9a3e-50c622cadd06	Bonos (DV01)	79152590-2023-46af-879f-5fc4a8eef632	\N
2025-05-18 04:03:51.433186+00	2025-05-18 04:12:50.132377+00	f	\N	277970a9-c026-4a62-8885-492344a0ae5b	Acciones (Volatilidad)	79152590-2023-46af-879f-5fc4a8eef632	\N
2025-05-18 04:03:51.433825+00	2025-05-18 04:12:50.133241+00	f	\N	4f1c2237-4a0e-48f0-915e-9d84bd700101	Derivados (griegas)	79152590-2023-46af-879f-5fc4a8eef632	\N
2025-05-18 04:03:51.43608+00	2025-05-18 04:12:50.136704+00	f	\N	dbba4c25-d15c-4af7-8d74-a314226a960b	Estimación del Valor en Riesgos (VaR)	cdc89534-8128-4798-af22-6bcbb1aadfea	\N
2025-05-18 04:03:51.436725+00	2025-05-18 04:12:50.137739+00	f	\N	333b50fa-7619-48b6-aa38-ea86494d4057	Métodos históricos, paramétricos y simulaciones.	cdc89534-8128-4798-af22-6bcbb1aadfea	\N
2025-05-18 04:03:51.437416+00	2025-05-18 04:12:50.138475+00	f	\N	2be37f0a-f6cc-45ee-ac65-8a99e25c1fff	Taller de cálculo del VaR de un activo y de un portafolio	cdc89534-8128-4798-af22-6bcbb1aadfea	\N
2025-05-18 04:03:51.439863+00	2025-05-18 04:12:50.141651+00	f	\N	e113f3cd-059d-4283-9742-8a076530423f	VaR de Bonos 	f1f1ed81-9409-44c8-9e19-1b4f4d079f3d	\N
2025-05-18 04:03:51.440613+00	2025-05-18 04:12:50.142362+00	f	\N	edb81dda-edc0-46d2-acd8-53e251be5919	VaR de Acciones 	f1f1ed81-9409-44c8-9e19-1b4f4d079f3d	\N
2025-05-18 04:03:51.441238+00	2025-05-18 04:12:50.143068+00	f	\N	f1abf329-1e9e-47ee-bfff-eb0999d24b50	VaR de Derivados 	f1f1ed81-9409-44c8-9e19-1b4f4d079f3d	\N
2025-05-18 04:03:51.443364+00	2025-05-18 04:12:50.145809+00	f	\N	38cccfc5-0c4b-4565-bf61-af189d4d37b0	Técnicas de validación de modelos	9ab3e919-2090-4989-820e-db76a89c464a	\N
2025-05-18 04:03:51.443998+00	2025-05-18 04:12:50.146557+00	f	\N	88f8ad65-3bfa-4f1e-94c0-045cba3c34fc	Pruebas de Kupiec	9ab3e919-2090-4989-820e-db76a89c464a	\N
2025-05-18 04:03:51.444689+00	2025-05-18 04:12:50.147316+00	f	\N	0b58b961-8aac-49a0-9adf-d1dcb3b172f9	Escenarios de estrés histórico e hipotético	9ab3e919-2090-4989-820e-db76a89c464a	\N
2025-05-18 04:03:51.448939+00	2025-05-18 04:12:50.152187+00	f	\N	4a839d7b-3609-414f-81c9-1cb9a26a16e2	Basilea III: CAR, provisiones	3a492f23-6dc0-4367-8941-49742f0b5269	\N
2025-05-18 04:03:51.449571+00	2025-05-18 04:12:50.152963+00	f	\N	f541c248-65c7-44f8-b957-30b3e4cd61bd	IFRS 9: ECL	3a492f23-6dc0-4367-8941-49742f0b5269	\N
2025-05-18 04:03:51.451807+00	2025-05-18 04:12:50.155488+00	f	\N	af8294f4-eafb-4038-a82d-23d1a51500f4	Pérdida esperada y pérdida extrema	2909797d-4978-46c6-b290-1dbc8b992e8c	\N
2025-05-18 04:03:51.452397+00	2025-05-18 04:12:50.156186+00	f	\N	d5f6dc83-a042-422e-9b01-05284b83c638	Matrices de Transición	2909797d-4978-46c6-b290-1dbc8b992e8c	\N
2025-05-18 04:03:51.453024+00	2025-05-18 04:12:50.156996+00	f	\N	837b9e62-a707-483e-8ac3-7b391a413247	Credit Scoring	2909797d-4978-46c6-b290-1dbc8b992e8c	\N
2025-02-28 16:46:46.169561+00	2025-02-28 16:46:46.16957+00	f	\N	2a39e885-973c-4df2-b53a-93f9266a07fe	Matemática 1 (Cálculo diferencial)	dbad2cf4-18ba-4af9-b183-33ca10ff0ac2	\N
2025-02-28 16:46:46.170266+00	2025-02-28 16:46:46.170275+00	f	\N	aa380de1-aca7-416f-8ffa-d4b0fdef2303	Matemática 2 (Cálculo integral)	dbad2cf4-18ba-4af9-b183-33ca10ff0ac2	\N
2025-02-28 16:46:46.172839+00	2025-02-28 16:46:46.172848+00	f	\N	2125a889-1360-445d-9a40-4ead5b3d228c	Distribuciones de datos gráficos	55a006c0-d0a5-4f8e-87e7-a24d7d4822be	\N
2025-02-28 16:46:46.173472+00	2025-02-28 16:46:46.173481+00	f	\N	6607e317-6c6f-4b1c-a240-6cc7fa1e1851	Análisis básico Probabilidad	55a006c0-d0a5-4f8e-87e7-a24d7d4822be	\N
2025-02-28 16:46:46.17415+00	2025-02-28 16:46:46.174162+00	f	\N	b45a98cb-7e2a-46f9-9106-bd65ae956cb7	Intervalos de confianza	55a006c0-d0a5-4f8e-87e7-a24d7d4822be	\N
2025-02-28 16:46:46.174853+00	2025-02-28 16:46:46.17486+00	f	\N	e4b281b7-f856-48ce-9f97-b0f00f432b23	Pruebas de hipótesis	55a006c0-d0a5-4f8e-87e7-a24d7d4822be	\N
2025-02-28 16:46:46.175582+00	2025-02-28 16:46:46.17559+00	f	\N	e0a7f28a-50b9-450d-b6c0-c622ed2d3ddc	Regresión básica	55a006c0-d0a5-4f8e-87e7-a24d7d4822be	\N
2025-02-28 16:46:46.180281+00	2025-02-28 16:46:46.180289+00	f	\N	4abcf8d6-c1da-4717-b12c-312809c141cf	Principios básicos	dfe6a620-c81e-4723-a35d-22754737ce6f	\N
2025-02-28 16:46:46.180978+00	2025-02-28 16:46:46.180985+00	f	\N	8c7dc7e7-bba0-4c43-8703-f85e232839f2	Funcionamiento de la economía	dfe6a620-c81e-4723-a35d-22754737ce6f	\N
2025-02-28 16:46:46.183241+00	2025-02-28 16:46:46.18325+00	f	\N	2c0b884b-624c-417b-ba39-634dc68c1686	Oferta	521a9dee-5110-442a-9a72-e4324a1190ff	\N
2025-02-28 16:46:46.183838+00	2025-02-28 16:46:46.183846+00	f	\N	32977fd1-feba-44b7-92db-0582264929aa	Demanda	521a9dee-5110-442a-9a72-e4324a1190ff	\N
2025-02-28 16:46:46.184438+00	2025-02-28 16:46:46.184449+00	f	\N	48025b87-b802-4c56-8add-4117d5d8766a	Elasticidad	521a9dee-5110-442a-9a72-e4324a1190ff	\N
2025-02-28 16:46:46.185074+00	2025-02-28 16:46:46.185111+00	f	\N	8c4159af-52cf-455d-bec5-53759ff93d15	Costos de producción	521a9dee-5110-442a-9a72-e4324a1190ff	\N
2025-02-28 16:46:46.185791+00	2025-02-28 16:46:46.185799+00	f	\N	1ddef9ae-a7c6-4bea-8904-1d4532e71e3e	Competencia perfecta	521a9dee-5110-442a-9a72-e4324a1190ff	\N
2025-02-28 16:46:46.1864+00	2025-02-28 16:46:46.186408+00	f	\N	11d941f5-02c3-49ff-85d5-cdd24935f34f	Estructuras de mercado	521a9dee-5110-442a-9a72-e4324a1190ff	\N
2025-02-28 16:46:46.18917+00	2025-02-28 16:46:46.189178+00	f	\N	4b3297f4-1cdf-4b25-978c-f1cbe18b8f44	Indicadores macroeconómicos	ccb813a5-3a99-403e-8b45-d95d5f322ecd	\N
2025-02-28 16:46:46.189763+00	2025-02-28 16:46:46.189771+00	f	\N	a4820458-cd49-4ead-ac60-359a2d4f1a7a	PIB	ccb813a5-3a99-403e-8b45-d95d5f322ecd	\N
2025-02-28 16:46:46.190368+00	2025-02-28 16:46:46.190376+00	f	\N	fdd6ddaf-3ca7-49b2-9ffd-ed7d4a8c3e39	Inflación	ccb813a5-3a99-403e-8b45-d95d5f322ecd	\N
2025-02-28 16:46:46.191015+00	2025-02-28 16:46:46.191022+00	f	\N	8fdbf447-6fa7-46fd-a2eb-bad2d5b3cef8	Desempleo	ccb813a5-3a99-403e-8b45-d95d5f322ecd	\N
2025-02-28 16:46:46.191623+00	2025-02-28 16:46:46.19163+00	f	\N	01dd7495-e937-4e7c-8f92-ba12797087b2	Modelos económicos	ccb813a5-3a99-403e-8b45-d95d5f322ecd	\N
2025-02-28 16:46:46.192159+00	2025-02-28 16:46:46.192165+00	f	\N	31fd67ab-5e2f-4eac-aef4-35347afb0a35	Política fiscal y monetaria	ccb813a5-3a99-403e-8b45-d95d5f322ecd	\N
2025-02-28 16:46:46.194341+00	2025-02-28 16:46:46.194349+00	f	\N	3cd8ce18-4431-4c5d-b313-69a4f1666afa	Modelos de regresión lineal	84601331-8b68-44bd-b573-5ac2a7411452	\N
2025-02-28 16:46:46.198119+00	2025-02-28 16:46:46.198126+00	f	\N	189de2ab-b18e-4255-b062-59727434f928	Funciones	3d389381-9829-4c80-98d9-00ee76576088	\N
2025-02-28 16:46:46.19872+00	2025-02-28 16:46:46.198727+00	f	\N	eb670600-c054-406b-b216-7cfcbe77dd12	Evolución	3d389381-9829-4c80-98d9-00ee76576088	\N
2025-02-28 16:46:46.199306+00	2025-02-28 16:46:46.199313+00	f	\N	8fc8be7e-8e4d-4e77-9cfc-706a4c243c7c	Tendencias modernas en administración	3d389381-9829-4c80-98d9-00ee76576088	\N
2025-02-28 16:46:46.201558+00	2025-02-28 16:46:46.201565+00	f	\N	653e6cbc-d31a-40d8-8e95-fa74395c8bf7	Planeación	be5be351-556e-4d5e-924d-abf7b5ee445c	\N
2025-02-28 16:46:46.202166+00	2025-02-28 16:46:46.202172+00	f	\N	c6cb7b03-e815-46ca-ab6c-d6ab7cfedf41	organización	be5be351-556e-4d5e-924d-abf7b5ee445c	\N
2025-02-28 16:46:46.202776+00	2025-02-28 16:46:46.202784+00	f	\N	f7ca792b-4cca-4383-bea5-bca00da9fb97	Dirección y control	be5be351-556e-4d5e-924d-abf7b5ee445c	\N
2025-02-28 16:46:46.203439+00	2025-02-28 16:46:46.203447+00	f	\N	e8a075fa-c369-4bd9-847c-4663c09cd4e2	Modelo clásico de gestión	be5be351-556e-4d5e-924d-abf7b5ee445c	\N
2025-02-28 16:46:46.206013+00	2025-02-28 16:46:46.206021+00	f	\N	6d36f37c-870f-4a42-8c56-54f98bbb9846	Diseño de estrategias	e280910c-d464-4b0b-aab9-a8b15104e778	\N
2025-02-28 16:46:46.206637+00	2025-02-28 16:46:46.206645+00	f	\N	05f37502-48d0-4c29-9985-1dd684d9b921	Modelos de negocio eficientes	e280910c-d464-4b0b-aab9-a8b15104e778	\N
2025-02-28 16:46:46.208992+00	2025-02-28 16:46:46.208999+00	f	\N	22c7bf3c-2ce3-4c42-96ed-d883c857d226	Business Model Canvas	02b9b6a0-bf72-4887-a897-5381d96887fb	\N
2025-02-28 16:46:46.209721+00	2025-02-28 16:46:46.209728+00	f	\N	770dacd0-f51b-42b6-9dc9-a31fb1203ee5	Lean Startup	02b9b6a0-bf72-4887-a897-5381d96887fb	\N
2025-02-28 16:46:46.21028+00	2025-02-28 16:46:46.210287+00	f	\N	3498390c-01bb-4f8c-ad50-ab0f58a6a997	Estructura organizacional moderna (útil para quienes quieren crear su propia empresa)	02b9b6a0-bf72-4887-a897-5381d96887fb	\N
2025-02-28 16:46:46.212571+00	2025-02-28 16:46:46.212581+00	f	\N	040d7748-d1b8-434e-a07d-12e24ce69ab2	Estados financieros	ff77038c-9c45-493e-8ceb-7d3d9fe3326a	\N
2025-02-28 16:46:46.213177+00	2025-02-28 16:46:46.213184+00	f	\N	3404db51-4d1e-4764-83b7-d43eff4accb7	Activos, pasivos	ff77038c-9c45-493e-8ceb-7d3d9fe3326a	\N
2025-02-28 16:46:46.213804+00	2025-02-28 16:46:46.213811+00	f	\N	5d28e41d-53a5-4e18-82f0-5e2e719227d6	Patrimonio	ff77038c-9c45-493e-8ceb-7d3d9fe3326a	\N
2025-02-28 16:46:46.214414+00	2025-02-28 16:46:46.214421+00	f	\N	942c9afb-901d-487d-a348-57e4f44d68e4	NIIF básicas	ff77038c-9c45-493e-8ceb-7d3d9fe3326a	\N
2025-02-28 16:46:46.216615+00	2025-02-28 16:46:46.216623+00	f	\N	c4d0ed4d-d0f9-460e-8650-913c45180877	Gestión de costos	34a6fd26-b390-40df-92fd-96056de36c79	\N
2025-02-28 16:46:46.21725+00	2025-02-28 16:46:46.217257+00	f	\N	bf04d7f5-f980-469e-8db9-2232195b2984	Punto de equilibrio	34a6fd26-b390-40df-92fd-96056de36c79	\N
2025-02-28 16:46:46.217784+00	2025-02-28 16:46:46.217791+00	f	\N	8ecab52b-3953-4681-a4a5-2d2674332930	Presupuestos operativos y financieros)	34a6fd26-b390-40df-92fd-96056de36c79	\N
2025-02-28 16:46:46.219843+00	2025-02-28 16:46:46.219851+00	f	\N	2b320b8f-9c84-4c4d-a7c6-b14fb104d585	Movimientos de capital	4126e870-1a93-4d64-8cb0-769d06c1eadd	\N
2025-02-28 16:46:46.220532+00	2025-02-28 16:46:46.22054+00	f	\N	6bb21752-97f6-44c9-978e-b094e05e85ce	Tasas de interés	4126e870-1a93-4d64-8cb0-769d06c1eadd	\N
2025-02-28 16:46:46.221089+00	2025-02-28 16:46:46.221096+00	f	\N	bf0d3597-86f8-4a35-9792-1edd3eb978fc	Valor presente y futuro)	4126e870-1a93-4d64-8cb0-769d06c1eadd	\N
2025-02-28 16:46:46.223333+00	2025-02-28 16:46:46.223341+00	f	\N	2d04ec3f-f6b5-460e-ae6a-70e6bd185e4a	 Análisis financiero	f94a3416-2d19-4110-983e-e62b0047c40b	\N
2025-02-28 16:46:46.224098+00	2025-02-28 16:46:46.224105+00	f	\N	51101165-e278-4d34-853e-f614b612df95	Gestión del capital	f94a3416-2d19-4110-983e-e62b0047c40b	\N
2025-02-28 16:46:46.224763+00	2025-02-28 16:46:46.22477+00	f	\N	3bdcbbbd-798f-42e7-bad7-d429ad124ec8	Estructura de financiamiento	f94a3416-2d19-4110-983e-e62b0047c40b	\N
2025-02-28 16:46:46.225407+00	2025-02-28 16:46:46.225414+00	f	\N	eab77f19-efa1-4034-aa22-f65b2cd388d9	Clave para administración y economía	f94a3416-2d19-4110-983e-e62b0047c40b	\N
2025-03-03 23:10:05.047849+00	2025-03-03 23:10:05.04786+00	f	\N	c81e03ef-9b63-4233-b37e-b9a5f3365ae5	Teoría de consumidor	54b12722-f668-43d4-97b7-7658cb10caf2	\N
2025-03-03 23:10:05.048693+00	2025-03-03 23:10:05.048702+00	f	\N	b097db61-7dbf-4f13-9793-16bb488b8399	Teoría del productor	54b12722-f668-43d4-97b7-7658cb10caf2	\N
2025-03-03 23:10:05.049434+00	2025-03-03 23:10:05.049442+00	f	\N	0646556c-9222-4bf4-8926-14c7b3136724	Competencia perfecta	54b12722-f668-43d4-97b7-7658cb10caf2	\N
2025-03-03 23:10:05.050083+00	2025-03-03 23:10:05.05009+00	f	\N	7b01b3bf-90f3-4805-b962-53a18687d0d2	Fallas de mercado	54b12722-f668-43d4-97b7-7658cb10caf2	\N
2025-03-03 23:10:05.052262+00	2025-03-03 23:10:05.05227+00	f	\N	ae7fcf7e-72c8-4b5a-8d88-71121012609d	Monopolio	4def8daf-4f8d-4efb-9986-17d650b583b9	\N
2025-03-03 23:10:05.052847+00	2025-03-03 23:10:05.052854+00	f	\N	501dc6cc-72be-4072-8720-83dc96cf6694	Oligopolio y teoría de juego	4def8daf-4f8d-4efb-9986-17d650b583b9	\N
2025-03-03 23:10:05.053409+00	2025-03-03 23:10:05.053416+00	f	\N	c4cb9665-1c5f-4989-bf64-4a8fdd8929be	Externalidades y Bienes Públicos	4def8daf-4f8d-4efb-9986-17d650b583b9	\N
2025-03-03 23:10:05.053957+00	2025-03-03 23:10:05.053981+00	f	\N	c0ee8678-841a-432b-bcf9-fe063123b973	Teoría de Incertidumbre, Contratos	4def8daf-4f8d-4efb-9986-17d650b583b9	\N
2025-03-03 23:10:05.054541+00	2025-03-03 23:10:05.054552+00	f	\N	b12605f3-33c6-4994-86d1-d2e512a74184	Mercado de Factores	4def8daf-4f8d-4efb-9986-17d650b583b9	\N
2025-03-03 23:10:05.055097+00	2025-03-03 23:10:05.055104+00	f	\N	fe18a272-3aab-45b3-983f-41bf156f5025	Equilibrio general y Regulación Económica	4def8daf-4f8d-4efb-9986-17d650b583b9	\N
2025-03-03 23:10:05.058975+00	2025-03-03 23:10:05.058983+00	f	\N	ef5b56fe-638b-4a61-834d-a0c345ab5ed5	Programación Financiera	d6106b57-a177-41fe-90df-4c8e31b96875	\N
2025-03-03 23:10:05.059646+00	2025-03-03 23:10:05.059655+00	f	\N	3f96ab74-f2ee-4165-939a-de596a3427cb	Modelo Ahorro-Inversión	d6106b57-a177-41fe-90df-4c8e31b96875	\N
2025-03-03 23:10:05.060219+00	2025-03-03 23:10:05.060228+00	f	\N	b25f8399-c35c-47ef-b4b3-241195fd8c35	Modelo Neoclásico	d6106b57-a177-41fe-90df-4c8e31b96875	\N
2025-03-03 23:10:05.06086+00	2025-03-03 23:10:05.060867+00	f	\N	84dde77f-ed1a-4908-99cf-5dd924414e80	Modelo Keynesiano	d6106b57-a177-41fe-90df-4c8e31b96875	\N
2025-03-03 23:10:05.061424+00	2025-03-03 23:10:05.061431+00	f	\N	9584b8fb-6928-49c2-83f4-9592655e8047	Modelo IS-LM economía cerrada	d6106b57-a177-41fe-90df-4c8e31b96875	\N
2025-03-03 23:10:05.0621+00	2025-03-03 23:10:05.062107+00	f	\N	7f067cb0-0690-4479-8d6c-c2cac0760eae	Modelo IS-LM economía abierta	d6106b57-a177-41fe-90df-4c8e31b96875	\N
2025-03-03 23:10:05.064473+00	2025-03-03 23:10:05.064483+00	f	\N	30bcdd55-9be4-4cff-8243-d00f6d73f848	Modelos de Crecimiento Económico 	2aaa6c7e-d216-47d6-b48d-281ce08e3e7a	\N
2025-03-03 23:10:05.06513+00	2025-03-03 23:10:05.06514+00	f	\N	9135b956-a7a8-4835-8eac-83e648b1de75	Microfundamentos de la macroeconomía	2aaa6c7e-d216-47d6-b48d-281ce08e3e7a	\N
2025-03-03 23:10:05.065783+00	2025-03-03 23:10:05.06579+00	f	\N	780e2e6d-75b6-40c7-9296-b337ad9d1d38	Política Fiscal 	2aaa6c7e-d216-47d6-b48d-281ce08e3e7a	\N
2025-03-03 23:10:05.066396+00	2025-03-03 23:10:05.066403+00	f	\N	19f375c4-6619-4b3f-b092-9dc70f605bc5	Política Monetaria	2aaa6c7e-d216-47d6-b48d-281ce08e3e7a	\N
2025-03-03 23:10:05.067015+00	2025-03-03 23:10:05.067022+00	f	\N	39b5168c-ca48-4579-b992-8f927289e7f2	Economía Internacional	2aaa6c7e-d216-47d6-b48d-281ce08e3e7a	\N
2025-03-03 23:10:05.070803+00	2025-03-03 23:10:05.07081+00	f	\N	24b95814-68ae-4d8a-8639-6afd78080c33	Matemática Financiera	5fc3be80-7500-4628-a75d-315b133f44c8	\N
2025-03-03 23:10:05.07138+00	2025-03-03 23:10:05.07139+00	f	\N	819dccf1-1feb-435c-a492-753f1756f20b	Contabilidad	5fc3be80-7500-4628-a75d-315b133f44c8	\N
2025-03-03 23:10:05.072017+00	2025-03-03 23:10:05.072025+00	f	\N	29d0faed-b2f2-4eca-93c9-046889548bc6	Análisis de Estados Financieros	5fc3be80-7500-4628-a75d-315b133f44c8	\N
2025-03-03 23:10:05.072617+00	2025-03-03 23:10:05.072624+00	f	\N	5e851a55-34f2-46ca-acde-ac1b4dd671ca	Finanzas Corporativas	5fc3be80-7500-4628-a75d-315b133f44c8	\N
2025-03-03 23:10:05.073177+00	2025-03-03 23:10:05.073184+00	f	\N	a31c0c69-b851-4ce1-bfc4-9c0357cb3e7c	Valorización de Empresas	5fc3be80-7500-4628-a75d-315b133f44c8	\N
2025-03-03 23:10:05.073759+00	2025-03-03 23:10:05.073766+00	f	\N	6336fc38-911f-498b-94aa-e16bee22db7c	Mercados Financieros	5fc3be80-7500-4628-a75d-315b133f44c8	\N
2025-03-03 23:10:05.076162+00	2025-03-03 23:10:05.076172+00	f	\N	3171f5ac-fb03-4cb4-9ab0-854dd318b0d2	Renta Fija	0580d4be-5a3c-4dac-a411-8af4e8ede5bb	\N
2025-03-03 23:10:05.07689+00	2025-03-03 23:10:05.076899+00	f	\N	78cc89e6-b60c-43b7-ae1a-787b898cb877	Renta Variable	0580d4be-5a3c-4dac-a411-8af4e8ede5bb	\N
2025-03-03 23:10:05.077474+00	2025-03-03 23:10:05.077482+00	f	\N	fd5a30e4-ead7-4281-884e-e11185e4177f	Derivados	0580d4be-5a3c-4dac-a411-8af4e8ede5bb	\N
2025-03-03 23:10:05.078112+00	2025-03-03 23:10:05.078122+00	f	\N	eaab19ba-8c15-4c93-b1ad-6207866c4634	Ingeniería Financiera	0580d4be-5a3c-4dac-a411-8af4e8ede5bb	\N
2025-03-03 23:10:05.078706+00	2025-03-03 23:10:05.078713+00	f	\N	a7b443ac-53c9-41e1-a31e-71bed9a606f0	Teoría de portafolio	0580d4be-5a3c-4dac-a411-8af4e8ede5bb	\N
2025-03-03 23:10:05.079251+00	2025-03-03 23:10:05.079258+00	f	\N	f7e94144-aee5-4e36-942e-c39430ec3302	Gestión de riesgos	0580d4be-5a3c-4dac-a411-8af4e8ede5bb	\N
2025-03-03 23:10:05.083103+00	2025-03-03 23:10:05.083112+00	f	\N	ef55c9ea-462c-4780-8bf8-7a4e5865a79d	Matemática 1	65eff931-d382-4748-a7f3-6323094cf656	\N
2025-03-03 23:10:05.083701+00	2025-03-03 23:10:05.083708+00	f	\N	82e1ba2a-f45b-443a-be88-2d917d52a53f	Matemática 2	65eff931-d382-4748-a7f3-6323094cf656	\N
2025-03-03 23:10:05.085783+00	2025-03-03 23:10:05.08579+00	f	\N	4d848c80-3b3f-480e-9c9b-040af5fd1f8a	Estadística descriptiva	27fadcdd-9e29-4663-ad23-15cbdd28f496	\N
2025-03-03 23:10:05.086361+00	2025-03-03 23:10:05.086369+00	f	\N	8cd399ca-3f6d-41ed-9aab-6c76f7c5ed17	Estadística inferencial	27fadcdd-9e29-4663-ad23-15cbdd28f496	\N
2025-03-03 23:10:05.088395+00	2025-03-03 23:10:05.088403+00	f	\N	42592cf9-280e-46f0-8361-d913d59ab4a4	Modelo de Regresión Lineal	309ac93f-c233-4885-be40-313bcb23fcee	\N
2025-03-03 23:10:05.089205+00	2025-03-03 23:10:05.089223+00	f	\N	5f4eb190-6afd-458f-b8d0-54eb78f4cb7e	Modelo de Regresión no Lineal	309ac93f-c233-4885-be40-313bcb23fcee	\N
2025-03-03 23:10:05.090027+00	2025-03-03 23:10:05.090038+00	f	\N	905bc9c9-0ecf-4e88-a871-7aad1c1ae1c1	Modelos de Panel data	309ac93f-c233-4885-be40-313bcb23fcee	\N
2025-03-03 23:10:05.092673+00	2025-03-03 23:10:05.092682+00	f	\N	13a706a1-2a2c-4003-bc6f-3288a05f0a6d	Modelos de Series de tiempo	bfc77aa5-f1e3-493a-84d1-3fb3c3b1f588	\N
2025-05-17 03:39:01.827103+00	2025-05-18 01:21:40.465925+00	f	\N	ad8198cc-e718-4013-ab00-71ecac72955f	Estadística descriptiva e inferencial	477aca90-**************-f5f4b9646157	\N
2025-05-17 03:39:01.828049+00	2025-05-18 01:21:40.466603+00	f	\N	c81d8cef-bd9b-41bf-bd66-f5c4d7ffc548	Estadística paramétrica y no paramétrica	477aca90-**************-f5f4b9646157	\N
2025-05-17 03:39:01.82896+00	2025-05-18 01:21:40.467374+00	f	\N	9e4c4cbd-ae3d-4451-9458-da86c97a08a2	Estadística bayesiana	477aca90-**************-f5f4b9646157	\N
2025-05-17 03:39:01.83623+00	2025-05-18 01:21:40.473041+00	f	\N	b9c2f5fc-f2ee-4a42-ab44-da6741d5bf02	Identificación y tratamiento de base de datos	9d7773ff-f660-4547-a0fa-4e03e3a627f6	\N
2025-05-17 03:39:01.837319+00	2025-05-18 01:21:40.473796+00	f	\N	88975631-715e-418e-9aa7-f60e8e2eb097	Manejo de datos con Python	9d7773ff-f660-4547-a0fa-4e03e3a627f6	\N
2025-05-17 03:39:01.840338+00	2025-05-18 01:21:40.476354+00	f	\N	4a1adeee-9710-4216-89ad-fb172896c617	Análisis Exploratorio de Datos (EDA) avanzado	20f262eb-0cc9-42c7-97ad-1008d2cebec5	\N
2025-05-17 03:39:01.841108+00	2025-05-18 01:21:40.47709+00	f	\N	67d10adb-e762-49c2-a411-9c556a553e81	Visualización con Matplotlib, Seaborn, Plotly	20f262eb-0cc9-42c7-97ad-1008d2cebec5	\N
2025-05-17 03:39:01.843964+00	2025-05-18 01:21:40.47997+00	f	\N	fa5ef013-ccae-4757-b161-df978726d8da	Técnicas para comunicar efectivamente resultados analíticos	6e91278d-2b43-4c1e-a109-fb15d063f6d5	\N
2025-05-17 03:39:01.846736+00	2025-05-18 01:21:40.483399+00	f	\N	2faea0bf-0217-47ea-8ad7-4a9cd0e00245	Análisis exploratorio de datos financieros para evaluación crediticia.	75a8561a-9b4d-420e-a73e-164e277df1a0	\N
2025-05-17 03:39:01.853728+00	2025-05-18 01:21:40.4904+00	f	\N	bcdde654-a7e1-415b-a30d-08907d28333b	Train-test, cross-validation, balanceo	1e54fd01-fe03-4b16-be82-de7781799c67	\N
2025-05-17 03:39:01.856451+00	2025-05-18 01:21:40.493245+00	f	\N	f0cbc735-6e27-463e-a3a9-a2a17799fa2a	Lineal, Logística, Ridge, Lasso, ElasticNet, Probit	91c3932d-adca-41bb-a25a-99da689849bb	\N
2025-05-17 03:39:01.861781+00	2025-05-18 01:21:40.497477+00	f	\N	57471c03-f103-423b-b7fb-5ccb00a6bfa4	Árboles de decisión, Random Forest, KNN, Naive Bayes, SVM	507fbc93-3b4c-4bcf-a23b-63226a2584ac	\N
2025-05-17 03:39:01.865052+00	2025-05-18 01:21:40.500225+00	f	\N	cc8c22f7-a9e5-445f-9538-f376c2b85dfc	Bagging, Boosting, Stacking	f008f0e6-7fad-4b25-84a3-5fe143f53ae7	\N
2025-05-17 03:39:01.8677+00	2025-05-18 01:21:40.503451+00	f	\N	d200e690-8326-4efc-8d15-9a97b3c0f513	GridSearchCV, métricas avanzadas	9c989570-6a29-45d0-9a3b-fbb830ff98b4	\N
2025-05-17 03:39:01.823812+00	2025-05-18 01:21:40.463199+00	f	\N	6b4de789-3eef-47ed-826c-9c73f5fa2ca3	Álgebra lineal, Cálculo diferencial básico	69a8ec97-45c3-4ee6-a75a-e423b798cb81	\N
2025-05-18 04:03:51.398292+00	2025-05-18 04:12:50.092321+00	f	\N	eb532dde-09e5-4ecd-b740-ad6304720ac5	Mercado de Valores	710b6f7b-9b40-4120-bda0-b5de474f6972	\N
2025-03-28 23:54:51.233671+00	2025-05-16 21:34:45.51905+00	f	\N	0d142878-d235-4db2-a2c7-05a9f6e15147	Modelos de optimización de portafolios	2ebc0a92-f809-46ff-b7fe-a4f60cc14a4c	\N
2025-03-28 23:54:51.234388+00	2025-05-16 21:34:45.519756+00	f	\N	ccd3da78-deec-476f-ad90-7f87c81e2d93	Valorización de Activos	2ebc0a92-f809-46ff-b7fe-a4f60cc14a4c	\N
2025-03-28 23:54:51.244095+00	2025-05-16 21:34:45.523998+00	f	\N	4927cf91-14b8-487a-a0a2-75eae2104fbd	Introducción a los riesgos financieros	623a9f69-2f13-4c0b-bfd1-1f63cde501a5	\N
2025-03-28 23:54:51.244695+00	2025-05-16 21:34:45.524661+00	f	\N	3e0f02a4-7656-4cd1-968b-40af77edb941	Tipos de riesgos	623a9f69-2f13-4c0b-bfd1-1f63cde501a5	\N
2025-03-28 23:54:51.245311+00	2025-05-16 21:34:45.525308+00	f	\N	30db8077-981d-42d3-af41-ad6a1f0cf2e6	Regulación financiera	623a9f69-2f13-4c0b-bfd1-1f63cde501a5	\N
2025-03-28 23:54:51.245997+00	2025-05-16 21:34:45.525955+00	f	\N	2fe103f6-2193-4078-b497-4b91e3be6fb7	Crisis financieras	623a9f69-2f13-4c0b-bfd1-1f63cde501a5	\N
2025-03-28 23:54:51.248662+00	2025-05-16 21:34:45.528385+00	f	\N	2dff5213-a9c0-4710-9cba-77c4b840409a	Factores de Riesgo de Mercado	6596f595-6107-473c-b820-a1c1d5c40e79	\N
2025-03-28 23:54:51.249467+00	2025-05-16 21:34:45.52907+00	f	\N	44a59442-53aa-40e9-b800-19b8b786a5d3	VaR de instrumentos financieros	6596f595-6107-473c-b820-a1c1d5c40e79	\N
2025-03-28 23:54:51.250026+00	2025-05-16 21:34:45.529708+00	f	\N	36a16958-bddc-42bc-b051-d643506efa84	Modelos de Riesgos de Mercados	6596f595-6107-473c-b820-a1c1d5c40e79	\N
2025-03-28 23:54:51.250572+00	2025-05-16 21:34:45.530364+00	f	\N	915489aa-3bc8-4e66-a3aa-61874c769930	Backtesting y stresstesting	6596f595-6107-473c-b820-a1c1d5c40e79	\N
2025-03-28 23:54:51.251105+00	2025-05-16 21:34:45.531098+00	f	\N	7f93fcae-c78c-4a86-9b36-da3916fe4863	Medias complementarias al VaR	6596f595-6107-473c-b820-a1c1d5c40e79	\N
2025-03-28 23:54:51.253628+00	2025-05-16 21:34:45.534071+00	f	\N	22520244-8f92-433e-8d33-542992b857ef	Factores de Riesgo de Crédito	4d3849ac-ca85-497d-bda8-3e25c136a19d	\N
2025-03-28 23:54:51.254307+00	2025-05-16 21:34:45.534911+00	f	\N	ab528c73-6641-4942-8721-322c6934b4cc	Derivados de riesgo de crédito	4d3849ac-ca85-497d-bda8-3e25c136a19d	\N
2025-03-28 23:54:51.258615+00	2025-05-16 21:34:45.539565+00	f	\N	59a5b52f-1d68-4d70-972c-10c1763ff21d	Factores de Riesgo de tasa de interés	75a24883-948a-4e82-94b1-285596388a18	\N
2025-03-28 23:54:51.25932+00	2025-05-16 21:34:45.540312+00	f	\N	8c46d49e-3f46-4e8e-9fb4-f7229155a6db	Modelos de Riesgos de Mercados	75a24883-948a-4e82-94b1-285596388a18	\N
2025-03-28 23:54:51.261907+00	2025-05-16 21:34:45.54276+00	f	\N	5d4577f0-9424-44dc-985c-899b94b363b9	Factores de riesgo operacional	8432bac8-a0aa-4959-b056-b1037f82a363	\N
2025-03-28 23:54:51.262499+00	2025-05-16 21:34:45.543495+00	f	\N	a4e4fec9-32bc-4007-ac41-0f3343bfcc3d	Modelos cualitativos de GRO	8432bac8-a0aa-4959-b056-b1037f82a363	\N
2025-03-28 23:54:51.263098+00	2025-05-16 21:34:45.544182+00	f	\N	22d35d8d-9a59-45ae-bc8f-a121ec727b08	Requerimiento de patrimonio efectivo	8432bac8-a0aa-4959-b056-b1037f82a363	\N
2025-05-17 03:39:01.882548+00	2025-05-18 01:21:40.516393+00	f	\N	26e62d94-0894-4e88-9e40-16e14563764f	K-Means, DBSCAN, Mezcla Gaussiana, BIRCH	1580aecb-7a37-4be1-834f-081035561170	\N
2025-05-17 03:39:01.870287+00	2025-05-18 01:21:40.506151+00	f	\N	de960f7b-d67c-4f7a-abc4-6070b0d6236b	Modelado predictivo para scoring crediticio y riesgo financiero.	e511d26a-3233-4f86-b662-4829cc4754dd	\N
2025-03-28 23:54:51.189765+00	2025-05-16 21:34:45.474863+00	f	\N	104c3229-14af-4d54-b35d-420a726330e5	Valor de dinero en el tiempo	db0cf6de-2c1b-4cff-85b0-fb6e2f3e0e5e	\N
2025-03-28 23:54:51.190397+00	2025-05-16 21:34:45.475846+00	f	\N	09441798-ac6f-4a0f-a875-6015027395ff	Anualidades y Amortizaciones	db0cf6de-2c1b-4cff-85b0-fb6e2f3e0e5e	\N
2025-03-28 23:54:51.192832+00	2025-05-16 21:34:45.478972+00	f	\N	a85e706d-0609-484a-90cc-ce76a8ef9dd2	Estados Financieros	76a273e0-487c-4e6b-9de0-0d0e627ceeb2	\N
2025-03-28 23:54:51.193502+00	2025-05-16 21:34:45.479792+00	f	\N	7ee0cee7-fb5e-4c21-bcbc-3bb44b765cd1	Análisis de Estados Financieros	76a273e0-487c-4e6b-9de0-0d0e627ceeb2	\N
2025-03-28 23:54:51.195805+00	2025-05-16 21:34:45.482632+00	f	\N	694ca478-ca23-4c0d-9ac0-487ba3b3317a	Gestión de la Empresa	966dfe06-9936-4296-93bd-5094423aec67	\N
2025-03-28 23:54:51.196467+00	2025-05-16 21:34:45.483398+00	f	\N	d73f1679-7d1a-4631-abd6-d9edf7430e86	Gestión de Financiamiento	966dfe06-9936-4296-93bd-5094423aec67	\N
2025-03-28 23:54:51.199096+00	2025-05-16 21:34:45.485996+00	f	\N	071fcd00-abf3-4b3b-a7e2-836a8a6e4f52	Financiamientos Directos 	1f2b6af8-6002-4f25-9ac3-0f668e1f1221	\N
2025-03-28 23:54:51.199747+00	2025-05-16 21:34:45.486881+00	f	\N	c5732820-4998-49f7-8dfe-225ec198b324	Financiamiento Indirecto	1f2b6af8-6002-4f25-9ac3-0f668e1f1221	\N
2025-03-28 23:54:51.202457+00	2025-05-16 21:34:45.490123+00	f	\N	e9f61672-08ba-48a4-aadb-f9331207d277	Formulación de Proyectos de Inversión	25de881c-128b-4e18-ba5b-da740e361f48	\N
2025-03-28 23:54:51.203302+00	2025-05-16 21:34:45.490903+00	f	\N	a52ef515-2cf5-4c2d-8e41-b23bd9b0cdb4	Evaluación de Proyectos de Inversion	25de881c-128b-4e18-ba5b-da740e361f48	\N
2025-03-28 23:54:51.204087+00	2025-05-16 21:34:45.491646+00	f	\N	c521597c-67c6-4935-8d77-d2e630c57648	Análisis de Sensibilidad de Proyectos de Inversión	25de881c-128b-4e18-ba5b-da740e361f48	\N
2025-03-28 23:54:51.204776+00	2025-05-16 21:34:45.492359+00	f	\N	5fd188ed-acb1-445e-a904-f0af87ac6c5b	Métodos de Valorización de Empresas	25de881c-128b-4e18-ba5b-da740e361f48	\N
2025-03-28 23:54:51.216509+00	2025-05-16 21:34:45.498727+00	f	\N	278700c8-85f5-459f-8115-6bd0ee789829	Panorama del Sistema Financiero Actual	83e6da25-2397-4cea-abdc-69890aacb8de	\N
2025-03-28 23:54:51.217148+00	2025-05-16 21:34:45.499479+00	f	\N	384e8d4c-11eb-4dfb-bea2-142acff78009	Intermediación financiera indirecta y directa	83e6da25-2397-4cea-abdc-69890aacb8de	\N
2025-03-28 23:54:51.217715+00	2025-05-16 21:34:45.500276+00	f	\N	1b7c1ddc-281c-496c-a8b3-32df134d4336	Herramientas Financieras	83e6da25-2397-4cea-abdc-69890aacb8de	\N
2025-03-28 23:54:51.218268+00	2025-05-16 21:34:45.501033+00	f	\N	15961535-895c-4d26-8127-814c6c1cd765	Instrumentos Financieros	83e6da25-2397-4cea-abdc-69890aacb8de	\N
2025-03-28 23:54:51.220335+00	2025-05-16 21:34:45.503563+00	f	\N	28ec0797-914b-4ceb-a1c7-63290705fe5d	Mercado de bonos	3d4aa98d-71d2-4822-a465-5f2e5375426d	\N
2025-03-28 23:54:51.220926+00	2025-05-16 21:34:45.504352+00	f	\N	f50bf718-0fdb-4394-bed9-653b9910c158	Valorización de Bonos Sin Opcionalidad	3d4aa98d-71d2-4822-a465-5f2e5375426d	\N
2025-03-28 23:54:51.221478+00	2025-05-16 21:34:45.505037+00	f	\N	0be8e07f-662c-4cf4-bdef-4ce1762aee84	Valoración de Bonos con Opcionalidad	3d4aa98d-71d2-4822-a465-5f2e5375426d	\N
2025-03-28 23:54:51.222075+00	2025-05-16 21:34:45.505715+00	f	\N	04dc4a58-72a5-4204-9ac1-3df19512cd8f	Medidas de Riesgos de Bonos	3d4aa98d-71d2-4822-a465-5f2e5375426d	\N
2025-03-28 23:54:51.224468+00	2025-05-16 21:34:45.50859+00	f	\N	966a1d38-a557-48fb-9c10-7428a3ef8d19	Mercado de acciones	8d4bd964-5935-4ef2-8dd5-97c350c9620e	\N
2025-03-28 23:54:51.22511+00	2025-05-16 21:34:45.509303+00	f	\N	430d18a7-6aef-4921-98a1-16ba7d805d71	Valorización de renta variable	8d4bd964-5935-4ef2-8dd5-97c350c9620e	\N
2025-03-28 23:54:51.225832+00	2025-05-16 21:34:45.510067+00	f	\N	70ca9256-5a50-4d55-beab-4d4aebc72268	Modelos de valor relativo	8d4bd964-5935-4ef2-8dd5-97c350c9620e	\N
2025-03-28 23:54:51.226541+00	2025-05-16 21:34:45.510764+00	f	\N	7851a208-26e8-41fa-8ce5-d84469b56ec5	Operaciones en el Mercado de valores	8d4bd964-5935-4ef2-8dd5-97c350c9620e	\N
2025-03-28 23:54:51.229023+00	2025-05-16 21:34:45.514187+00	f	\N	4ba831c3-ec2b-4d10-a77a-ecde7fa07401	Mercado de Derivados	be1dc3df-63c9-4cea-8b8e-80f59a7bd9ea	\N
2025-03-28 23:54:51.229631+00	2025-05-16 21:34:45.514915+00	f	\N	ab050424-5c0c-449c-bc94-5d2881d24128	Derivados Lineales	be1dc3df-63c9-4cea-8b8e-80f59a7bd9ea	\N
2025-03-28 23:54:51.230227+00	2025-05-16 21:34:45.515765+00	f	\N	d781db3a-d3f8-4309-b3e3-8fe2e9669331	Derivados no Lineales	be1dc3df-63c9-4cea-8b8e-80f59a7bd9ea	\N
2025-03-28 23:54:51.232922+00	2025-05-16 21:34:45.518206+00	f	\N	d791d6b7-a4fe-4654-ae73-59ffcba9907e	Teoría de Portafolio	2ebc0a92-f809-46ff-b7fe-a4f60cc14a4c	\N
2025-03-28 23:54:51.254892+00	2025-05-16 21:34:45.53565+00	f	\N	24771d76-c927-4621-92eb-94e95926ddd6	Modelos de Rating	4d3849ac-ca85-497d-bda8-3e25c136a19d	\N
2025-03-28 23:54:51.255466+00	2025-05-16 21:34:45.536354+00	f	\N	4ea73252-fe9a-4313-8f10-866c239dc59c	Modelo Scoring	4d3849ac-ca85-497d-bda8-3e25c136a19d	\N
2025-03-28 23:54:51.256106+00	2025-05-16 21:34:45.537017+00	f	\N	75ea0e73-6a02-4e8e-bb28-db2734324634	Modelos estructurales: Merton y KMV	4d3849ac-ca85-497d-bda8-3e25c136a19d	\N
2025-05-17 03:39:01.891537+00	2025-05-18 01:21:40.522497+00	f	\N	f758f864-0f7f-4e27-a1c2-096a27388960	Segmentación avanzada de clientes en servicios financieros	92b2092b-ad28-49eb-96d1-357257c414c8	\N
2025-05-17 03:39:01.898845+00	2025-05-18 01:21:40.528837+00	f	\N	5aaca2f7-f84b-4fea-8515-30d7d29c2867	Redes neuronales artificiales (Perceptrón, redes multicapa)	a31e5fd2-c29e-4b11-a9c1-642bcdba4d9e	\N
2025-05-17 03:39:01.8996+00	2025-05-18 01:21:40.529534+00	f	\N	b89061c9-4d1e-43b0-a96e-856f8c9e53d1	Redes neuronales convolucionales (CNN)	a31e5fd2-c29e-4b11-a9c1-642bcdba4d9e	\N
2025-05-17 03:39:01.900474+00	2025-05-18 01:21:40.530244+00	f	\N	df12fbda-26be-42e6-b63f-f7ece2e8e30c	Redes recurrentes (RNN)	a31e5fd2-c29e-4b11-a9c1-642bcdba4d9e	\N
2025-05-17 03:39:01.907395+00	2025-05-18 01:21:40.537035+00	f	\N	8be42514-e818-488c-8888-69dd3015f27c	Predicción de tendencias de mercado usando Deep Learning.	6b9db606-ef71-47d1-a7f6-ddcebd9e1ec6	\N
2025-05-18 04:03:51.40418+00	2025-05-18 04:12:50.098358+00	f	\N	09f0048b-132e-4340-a45b-c68b7fba24a0	Funciones de los Bancos	624daccd-d2b6-4606-938f-69be0fcfb4e3	\N
2025-05-18 04:03:51.404767+00	2025-05-18 04:12:50.0992+00	f	\N	b9c11e27-33d6-4998-91f4-532f7925e25a	Regulación Financiera	624daccd-d2b6-4606-938f-69be0fcfb4e3	\N
2025-05-18 04:03:51.405398+00	2025-05-18 04:12:50.099859+00	f	\N	f2469df9-1096-4569-81c0-da090706a488	Introducción a la Gestión de Riesgos	624daccd-d2b6-4606-938f-69be0fcfb4e3	\N
2025-05-18 04:03:51.407737+00	2025-05-18 04:12:50.102387+00	f	\N	660fc6a0-48db-456b-8631-ac07e229c18d	Sintaxis, funciones, estructuras de datos, NumPy, Pandas	5db22dfe-e2a0-4706-8358-02e4eb3c6baf	\N
2025-05-18 04:03:51.408433+00	2025-05-18 04:12:50.103117+00	f	\N	4d7e4cb7-1703-454f-94b3-f679fcae3961	Manejo de datos con Python	5db22dfe-e2a0-4706-8358-02e4eb3c6baf	\N
2025-05-18 04:03:51.409153+00	2025-05-18 04:12:50.103852+00	f	\N	5aeb0a77-9891-4a70-89d1-0f502baa0de3	Visualización con Matplotlib, Seaborn, Plotly	5db22dfe-e2a0-4706-8358-02e4eb3c6baf	\N
2025-05-18 04:03:51.411499+00	2025-05-18 04:12:50.106493+00	f	\N	c135673b-c89e-4bc2-83dd-ed1e36eef63a	Renta Fija	fbbe4664-41cd-4bcc-a338-cfb7bacfe426	\N
2025-05-18 04:03:51.412158+00	2025-05-18 04:12:50.107192+00	f	\N	dcc104d0-4092-4914-a91c-ae79fdc600f8	Renta Variable	fbbe4664-41cd-4bcc-a338-cfb7bacfe426	\N
2025-05-18 04:03:51.412816+00	2025-05-18 04:12:50.107875+00	f	\N	19347aa1-a0a1-4d69-9db6-7b2fa591f48d	Derivados	fbbe4664-41cd-4bcc-a338-cfb7bacfe426	\N
2025-05-18 04:03:51.415383+00	2025-05-18 04:12:50.110481+00	f	\N	3f0117b5-7c4a-430e-9212-115b47c31e43	Modelos de series de tiempo	dff200ec-00a1-4216-9384-e6aa666e9d47	\N
2025-05-18 04:03:51.416041+00	2025-05-18 04:12:50.111247+00	f	\N	2b61ee24-9dac-49b8-932b-ba2755b05ed1	Modelos de cointegración	dff200ec-00a1-4216-9384-e6aa666e9d47	\N
2025-05-18 04:03:51.418279+00	2025-05-18 04:12:50.113888+00	f	\N	25808134-16eb-45da-8559-4f24577f583f	Calculo Estocástico	e1da0abe-d410-4987-a99d-b3503e594846	\N
2025-05-18 04:03:51.418868+00	2025-05-18 04:12:50.114633+00	f	\N	867e82e6-783f-40f3-b1e0-71af32e97e45	Economia de Black Scholes	e1da0abe-d410-4987-a99d-b3503e594846	\N
2025-05-18 04:03:51.397306+00	2025-05-18 04:12:50.091474+00	f	\N	f8476dd0-ce0a-4949-a46b-3a1e2971fd62	Sistema Financiero	710b6f7b-9b40-4120-bda0-b5de474f6972	\N
2025-05-18 04:03:51.40075+00	2025-05-18 04:12:50.094786+00	f	\N	49a3cc3e-27f3-4ec8-ae33-da7c9f6abc29	Matemática Financiera	6d7fa26d-66ba-4449-aebb-dbf4a78b7e77	\N
2025-05-18 04:03:51.401643+00	2025-05-18 04:12:50.095541+00	f	\N	1b3090bb-48e1-4315-8713-5bf8541fde14	Estadística Financiera	6d7fa26d-66ba-4449-aebb-dbf4a78b7e77	\N
2025-05-18 04:03:51.426296+00	2025-05-18 04:12:50.124004+00	f	\N	5d9afa37-6934-4c5e-a6ee-118fa766dfdd	Identificación de factores (tasas de interés, divisas, inflación, crédito)	dcca79ce-df49-41d0-bc1b-904b7934eb1f	\N
2025-05-18 04:03:51.453638+00	2025-05-18 04:12:50.157666+00	f	\N	32b3138b-cf54-43bc-9590-7fa109886d7b	Modelos estructurales: Merton and KMV	2909797d-4978-46c6-b290-1dbc8b992e8c	\N
2025-05-18 04:03:51.455863+00	2025-05-18 04:12:50.160352+00	f	\N	7e365b82-98e4-442a-af3e-3a8628a5e900	Estimación del EAD, LGD y PD	5e7e27f3-c0a3-4fb3-bb94-33900f125f1d	\N
2025-05-18 04:03:51.456506+00	2025-05-18 04:12:50.161359+00	f	\N	79b14e4b-8174-4d2f-be50-f9da8236750c	Cálculo de provisiones	5e7e27f3-c0a3-4fb3-bb94-33900f125f1d	\N
2025-05-18 04:03:51.457087+00	2025-05-18 04:12:50.162047+00	f	\N	80098563-8e33-4ee4-a23c-6b23d857aafe	Estimación de la pérdida Inesperada	5e7e27f3-c0a3-4fb3-bb94-33900f125f1d	\N
2025-05-18 04:03:51.459183+00	2025-05-18 04:12:50.164807+00	f	\N	1a5fd6e4-4726-449b-bc10-7af010061561	Modelos de Regresión Logística	2367501e-107b-49d8-bd35-c80834ca177b	\N
2025-05-18 04:03:51.459738+00	2025-05-18 04:12:50.165578+00	f	\N	28f2c0e4-1740-43f3-b078-7324496dc41e	Taller de construcción de modelos scoring	2367501e-107b-49d8-bd35-c80834ca177b	\N
2025-05-18 04:03:51.461975+00	2025-05-18 04:12:50.168046+00	f	\N	c4debe8d-7cdc-40d4-9507-cbcbd1fe2c86	Árboles de decisión, redes neuronales, etc	a567ecd8-aa1c-42c4-9f28-1d0b4ab7046b	\N
2025-05-18 04:03:51.462686+00	2025-05-18 04:12:50.168741+00	f	\N	389fd8cd-defc-4d61-a7d9-80a9369640b4	Taller de riesgo de crédito con machine learning	a567ecd8-aa1c-42c4-9f28-1d0b4ab7046b	\N
2025-05-18 04:03:51.464801+00	2025-05-18 04:12:50.171248+00	f	\N	ef2df607-1e38-4a11-8abf-231e50224c48	Árboles de decisión, redes neuronales	be311ea4-8eea-45fa-ac9f-64b7391698d1	\N
2025-05-18 04:03:51.465451+00	2025-05-18 04:12:50.171986+00	f	\N	a0cd9595-abe0-4ea6-a75b-770788c34a30	Clustering y análisis de big data en crédito	be311ea4-8eea-45fa-ac9f-64b7391698d1	\N
2025-05-18 04:03:51.467714+00	2025-05-18 04:12:50.17469+00	f	\N	2de67659-0e01-459b-9b09-8548fd566a0d	Validación de modelos ECL	336f2087-da81-4988-ace2-69179fed40f6	\N
2025-05-18 04:03:51.46831+00	2025-05-18 04:12:50.175377+00	f	\N	8cc65250-b561-4b0c-9ec0-4b27a39d5240	Simulación de escenarios de crisis	336f2087-da81-4988-ace2-69179fed40f6	\N
2025-05-18 04:03:51.472106+00	2025-05-18 04:12:50.179805+00	f	\N	35f358ee-e6c9-4547-9e67-9345fd4cb475	Basilea III: LCR (Liquidity Coverage Ratio) y NSFR (Net Stable Funding Ratio)	ce2290ef-959f-495d-8329-ae2210587813	\N
2025-05-18 04:03:51.472708+00	2025-05-18 04:12:50.180616+00	f	\N	b920fee6-3c9e-4a30-8910-251cc51ba0cc	Rol del regulador y reportes regulatorios	ce2290ef-959f-495d-8329-ae2210587813	\N
2025-05-18 04:03:51.474933+00	2025-05-18 04:12:50.18315+00	f	\N	7173849e-d7e3-4576-afc0-d81477d99433	Ratio de Cobertura de Liquidez	7b6c535f-be8a-403a-b30c-d12d42835cdb	\N
2025-05-18 04:03:51.475583+00	2025-05-18 04:12:50.183865+00	f	\N	7a7d23db-3222-4b93-9d8b-d3b626ddd2fc	Ratio de Fondeo Neto Estable	7b6c535f-be8a-403a-b30c-d12d42835cdb	\N
2025-05-18 04:03:51.47612+00	2025-05-18 04:12:50.1846+00	f	\N	d38cdf42-4381-4e0c-ab1c-248ab3b09d01	Loan to Deposit	7b6c535f-be8a-403a-b30c-d12d42835cdb	\N
2025-05-18 04:03:51.476695+00	2025-05-18 04:12:50.185448+00	f	\N	13cda45b-e6f4-4fdd-b868-d70f93f881c2	Métricas de valor en riesgo de liquidez (LVaR)	7b6c535f-be8a-403a-b30c-d12d42835cdb	\N
2025-05-18 04:03:51.478954+00	2025-05-18 04:12:50.188052+00	f	\N	53d49ccf-a721-4fde-8a59-87f7ad38a1fa	Escenarios de estrés para crisis de liquidez	d85f3cc1-787a-47c3-941c-6d034f878c1e	\N
2025-05-18 04:03:51.47961+00	2025-05-18 04:12:50.189046+00	f	\N	017c0225-f462-4745-8c8e-8f434e75a7d9	Taller de Riesgo de Liquidez	d85f3cc1-787a-47c3-941c-6d034f878c1e	\N
2025-05-18 04:03:51.48361+00	2025-05-18 04:12:50.193207+00	f	\N	9b166be8-cd1b-4f8f-8bd4-9fdb77a22907	Basilea III	4a506d0a-008d-4ac1-84c6-d84448979663	\N
2025-05-18 04:03:51.484267+00	2025-05-18 04:12:50.19395+00	f	\N	5a229593-f738-4fa2-be72-8641206892a9	Rol del regulador y reportes regulatorios	4a506d0a-008d-4ac1-84c6-d84448979663	\N
2025-05-18 04:03:51.486617+00	2025-05-18 04:12:50.196297+00	f	\N	7bc5883c-3eed-443a-8d83-4fc89a7e1c55	Sensibilidad del Margen Financiero	362a65e9-b0f4-4fa5-b825-3a125dc6f667	\N
2025-05-18 04:03:51.487202+00	2025-05-18 04:12:50.196967+00	f	\N	c88088b4-e352-4a70-9ad3-ec2563ee5469	Sensibilidad del Valor Económico	362a65e9-b0f4-4fa5-b825-3a125dc6f667	\N
2025-05-18 04:03:51.487748+00	2025-05-18 04:12:50.197599+00	f	\N	96993c72-7ceb-4adf-a349-c57bcfff77a7	Taller de Riesgo de ALM	362a65e9-b0f4-4fa5-b825-3a125dc6f667	\N
2025-05-18 04:03:51.491571+00	2025-05-18 04:12:50.201935+00	f	\N	170cdd01-bbe6-4844-b693-f80a69fa0cd9	Clasificación de los derivados de crédito	4caed78f-fb23-4d1a-90e8-accc4a443dcb	\N
2025-05-18 04:03:51.492139+00	2025-05-18 04:12:50.202644+00	f	\N	b3df49d5-c453-4761-babe-374053181947	CDS, TRS, CLN	4caed78f-fb23-4d1a-90e8-accc4a443dcb	\N
2025-05-18 04:03:51.494178+00	2025-05-18 04:12:50.205168+00	f	\N	f8b2c379-2667-4e33-ad14-d8c5603de647	Valoración y PD riesgo neutral	7a17f49a-4921-4eac-b97e-5d3d70ef9315	\N
2025-05-18 04:03:51.49473+00	2025-05-18 04:12:50.20601+00	f	\N	1b4fdf1c-fa45-40fd-a23a-f2fecf8b2a3a	Modelos de probabilidad para valoración de derivados de crédito	7a17f49a-4921-4eac-b97e-5d3d70ef9315	\N
2025-05-18 04:03:51.495267+00	2025-05-18 04:12:50.206782+00	f	\N	1a676dbb-8cea-4113-93e2-bd5f8c2451eb	Ajuste por riesgo de contraparte	7a17f49a-4921-4eac-b97e-5d3d70ef9315	\N
2025-05-18 04:03:51.495798+00	2025-05-18 04:12:50.20763+00	f	\N	18c4cf44-b4ef-45c1-a0c1-0521f513c3b3	Ajuste por riesgo de contraparte XVAs (CVA, DVA, MVA, FMA, KVA)	7a17f49a-4921-4eac-b97e-5d3d70ef9315	\N
2025-05-18 04:03:51.496339+00	2025-05-18 04:12:50.208481+00	f	\N	832d3583-3163-4941-aa1e-a5507d176c22	Taller de Riesgo de Contraparte	7a17f49a-4921-4eac-b97e-5d3d70ef9315	\N
2025-05-18 04:03:51.500091+00	2025-05-18 04:12:50.212814+00	f	\N	60c2f2c2-1968-459a-8ef2-9f788ef08d91	Marco regulatorio (Basilea II y III).	a5e7ab1f-42d0-4f2f-876f-766c218554db	\N
2025-05-18 04:03:51.500712+00	2025-05-18 04:12:50.213912+00	f	\N	c9356edf-9d92-430c-9dc2-e664baa43b5c	Indicadores clave de riesgo (KRIs) en RO	a5e7ab1f-42d0-4f2f-876f-766c218554db	\N
2025-05-18 04:03:51.502825+00	2025-05-18 04:12:50.216598+00	f	\N	dd4e9cb5-bdc1-4bfa-b12a-eec3a9499b71	Mapas de calor: Matrices de probabilidad e impacto	9fffd7ac-261f-41da-9483-5f608b1e9f73	\N
2025-05-18 04:03:51.503434+00	2025-05-18 04:12:50.2173+00	f	\N	d0d76e8c-4493-4d8f-9be7-4fbffab87a89	Análisis de eventos de pérdida operacional y su categorización	9fffd7ac-261f-41da-9483-5f608b1e9f73	\N
2025-05-18 04:03:51.505798+00	2025-05-18 04:12:50.21975+00	f	\N	edd0b005-77a4-489a-a520-66fd436bcca5	Análisis de procesos (BPM) y evaluación de controles	df9ed1d8-5c7b-4a7d-9b43-b9d71e6a569d	\N
2025-05-18 04:03:51.506387+00	2025-05-18 04:12:50.220467+00	f	\N	ee328855-5dcf-4a9d-94ee-0b0606b4b33e	Entrevistas, talleres y encuestas de riesgo operacional	df9ed1d8-5c7b-4a7d-9b43-b9d71e6a569d	\N
2025-05-18 04:03:51.508447+00	2025-05-18 04:12:50.222939+00	f	\N	3a30c115-64ac-4969-88e3-5c4ae9a65042	Distribuciones de pérdidas: Modelos LDA (Loss Distribution Approach)	a8fb014c-ff72-4781-828b-2a42d732894b	\N
2025-05-18 04:03:51.509064+00	2025-05-18 04:12:50.223678+00	f	\N	dbe407b5-c830-4402-9ac2-329c39c3e859	Modelos AMA (Advanced Measurement Approach)	a8fb014c-ff72-4781-828b-2a42d732894b	\N
2025-05-18 04:03:51.509644+00	2025-05-18 04:12:50.224358+00	f	\N	2751ef9d-94d9-4ede-a635-c76a7c01b85e	Aplicación de machine learning para predicción de pérdidas operativas	a8fb014c-ff72-4781-828b-2a42d732894b	\N
2025-05-18 04:03:51.51354+00	2025-05-18 04:12:50.228388+00	f	\N	a8e29dce-3528-4f56-92e2-d1c0164788ae	Taller de empleabilidad laboral	1af226f0-902c-4acb-8f13-020ee77b5356	\N
2025-05-18 04:03:51.51428+00	2025-05-18 04:12:50.229052+00	f	\N	7803cbcc-a4eb-486b-bab6-876f8e2ae994	Taller de Riesgos Emergentes	1af226f0-902c-4acb-8f13-020ee77b5356	\N
2025-05-18 04:03:51.51504+00	2025-05-18 04:12:50.22967+00	f	\N	1f083093-34cf-416f-9fdf-7e8d181130b8	Taller de Gestión Integral de riesgos	1af226f0-902c-4acb-8f13-020ee77b5356	\N
2025-05-21 20:49:35.46849+00	2025-05-21 20:49:35.468498+00	f	\N	bb3713d3-f69c-4fe1-97b3-fbb009fa1f8e	Valor del dinero en el tiempo, tasas de interés, amortizaciones	4b6d3529-8e5e-47be-8e64-5e808c4aad34	\N
2025-05-21 20:49:35.471076+00	2025-05-21 20:49:35.471088+00	f	\N	d7f98f65-1505-4de7-95c8-4d8fb55267e2	Cuentas Constables y Estados financieros básicos	50dccf87-1e25-4478-8e8b-200fa3111584	\N
2025-05-21 20:49:35.473981+00	2025-05-21 20:49:35.473993+00	f	\N	de493cd7-84e2-4a41-b81d-ae845863c49b	Análisis vertical y horizontal, ratios financieros clave, interpretación  y práctica	67b87df1-57b4-432c-b34c-560b48c79501	\N
2025-05-21 20:49:35.47698+00	2025-05-21 20:49:35.476992+00	f	\N	bfee6bfe-d5ab-46c5-9fe2-ac5dbf2e375b	Tipos de costos, métodos presupuestarios, gestión de costos.	c9fa4165-ee46-4e7d-840d-765f743e414e	\N
2025-05-21 20:49:35.479564+00	2025-05-21 20:49:35.479572+00	f	\N	9aad40e8-5e48-4876-9194-c3ae757a1f7c	Evaluación financiera (VAN, TIR), flujo de caja, análisis de sensibilidad	c398b8e0-8849-469f-9211-88b1bc42d08c	\N
2025-05-21 20:49:35.481914+00	2025-05-21 20:49:35.481922+00	f	\N	4e054640-779d-4dfa-81fe-9c693e023b86	Modelación financiera en Excel, automatización con VBA, análisis cuantitativo con Python e Introducción a Bloomberg	5c45f295-755b-4665-869d-c739c5919b4a	\N
2025-05-21 20:49:35.485894+00	2025-05-21 20:49:35.485902+00	f	\N	a23100a7-9f74-43a2-91a0-71f213bd36e6	Gestión de capital de trabajo, administración de inventarios, liquidez operativa	836331e5-32e8-44c4-b7a4-000e9e43ba7a	\N
2025-05-21 20:49:35.488219+00	2025-05-21 20:49:35.488227+00	f	\N	ceea0bec-249f-4adc-aca9-cf61db80fa3f	Estructura óptima de capital, costo promedio ponderado de capital (WACC), decisiones de financiamiento	a30feee7-c3e1-401d-9451-0cc64b1c11b3	\N
2025-05-21 20:49:35.490267+00	2025-05-21 20:49:35.490275+00	f	\N	d101cb60-6155-44c0-b61f-0b1c6c1930af	Método de flujos descontados (DCF), múltiplos comparativos, valor económico agregado (EVA)	485e44e7-aff9-4993-a073-b96a0c1330ad	\N
2025-05-21 20:49:35.492883+00	2025-05-21 20:49:35.49289+00	f	\N	c2dba2cf-4ecd-487b-bc71-0014be14f3ee	Due diligence financiero, negociación de M&A, integración post-adquisición	93391abd-3e8b-4bbd-9e68-7809ee5b90cd	\N
2025-05-21 20:49:35.496852+00	2025-05-21 20:49:35.49686+00	f	\N	aae31598-47a5-49db-8cfb-27ad82fc8d42	Instituciones financieras, intermediación financiera, regulación financiera	df092030-675f-4907-bb82-3af758b3d5ab	\N
2025-05-21 20:49:35.499154+00	2025-05-21 20:49:35.499162+00	f	\N	e1553363-a9cb-4de0-921e-096b1991e82e	Bolsa de valores, instrumentos negociables, operaciones bursátiles	8d894f9b-73b3-4fbf-8743-0abfbbf119c3	\N
2025-05-21 20:49:35.501444+00	2025-05-21 20:49:35.501452+00	f	\N	c0e89775-d71e-403e-bb6a-910b19ba04aa	Financiamiento directo e indirecto, productos estructurados, leasing y factoring	793f908e-650e-434c-a3d2-13ea96b374e0	\N
2025-05-21 20:49:35.503851+00	2025-05-21 20:49:35.503862+00	f	\N	5a7661b6-ce48-4d9f-bf15-ed13b99b9d30	Mercado y Valoración de Bonos, Acciones y Derivados	2d72a744-18a9-498f-a712-e3cf4c9f653e	\N
2025-05-21 20:49:35.506035+00	2025-05-21 20:49:35.506043+00	f	\N	5a65c424-a209-4f61-aacd-c43c000746b5	Teoría moderna del portafolio, diversificación y riesgo, rendimiento esperado	8f310cd6-ec84-4580-90c4-36060dc2a5ef	\N
2025-05-21 20:49:35.510008+00	2025-05-21 20:49:35.510018+00	f	\N	2acdf769-e761-4f7e-86df-ae2276b01342	Estrategia financiera, presupuestos operativos y financieros, Balanced Scorecard.	fc71c1b7-f97c-402b-9fdd-bad6dc2e9447	\N
2025-05-21 20:49:35.512358+00	2025-05-21 20:49:35.512366+00	f	\N	5d8f26f0-7cc7-480b-8079-85255488ae54	Gestión del flujo de caja, inversiones temporales, gestión de liquidez.	00aacdc8-b5f1-429c-aa8c-a466d5505474	\N
2025-05-21 20:49:35.514626+00	2025-05-21 20:49:35.514634+00	f	\N	848f3fb3-8357-4136-a6ba-6fbaaa6f86d8	Planificación fiscal corporativa, beneficios tributarios, cumplimiento normativo	b963b941-c4d2-41f6-866a-e891b2b5f127	\N
2025-05-21 20:49:35.516964+00	2025-05-21 20:49:35.516972+00	f	\N	ae3b9801-3027-4e5a-a3fb-685fd330503c	Identificación y evaluación de riesgos, gestión integral del riesgo, cobertura financiera	18c649e7-e5c6-404a-b62b-514b02a95055	\N
2025-05-21 20:49:35.51918+00	2025-05-21 20:49:35.519188+00	f	\N	e248f460-010e-4f7e-96a8-b213f05002f8	Principios y mejores prácticas: ética y responsabilidad corporativa	8518a1aa-c4dc-46a1-9817-469799f4e704	\N
2025-05-21 20:49:35.521471+00	2025-05-21 20:49:35.521479+00	f	\N	c22f6dbb-8012-4041-ba7b-7298e64352de	Casos prácticos reales, aplicación de herramientas tecnológicas, decisiones financieras estratégicas	1c4f9189-f14e-4e84-b2f5-991ead7b3a50	\N
2025-05-21 20:49:35.52382+00	2025-05-21 20:49:35.523828+00	f	\N	68823585-fbf0-4e40-a7b7-1bad867f6b1c	Finanzas Sostenibles y Criterios ESG	e64b1c63-7620-4374-9fac-33a5ea5c80bb	\N
2025-05-21 20:49:35.524535+00	2025-05-21 20:49:35.524543+00	f	\N	058b3234-5d3a-4ec9-b05a-d4e2e89cbea9	Fintech y Finanzas Digitales	e64b1c63-7620-4374-9fac-33a5ea5c80bb	\N
2025-05-21 20:49:35.52518+00	2025-05-21 20:49:35.525187+00	f	\N	9bc08e56-c572-4ef4-ae78-49834009ead9	Compliance Financiero	e64b1c63-7620-4374-9fac-33a5ea5c80bb	\N
2025-05-21 20:49:35.525788+00	2025-05-21 20:49:35.525795+00	f	\N	e9e017c0-1c25-4696-8b5b-27cd68e75c61	Innovación Financiera	e64b1c63-7620-4374-9fac-33a5ea5c80bb	\N
\.


--
-- Data for Name: core_user; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_user (password, last_login, is_superuser, username, first_name, last_name, is_staff, is_active, date_joined, created_at, updated_at, deleted, deleted_at, uid, email, phone_number, id_number, deleted_by_id, profile_photo_id, company, google_contact_id, last_google_sync, ocupation, role, educational_institution_id, major_id, term_id, city, country) FROM stdin;
pbkdf2_sha256$720000$AhV7CoI8Notz2diS02jluQ$znZKYXocnHgA17iMj0fFA/bhSlLnWl4NX8VM7vG45s0=	\N	f	mirella	Mirella	CEU	t	t	2025-02-27 22:57:06+00	2025-02-27 22:57:06.545117+00	2025-02-27 22:57:48.774022+00	f	\N	c41e21f9-525a-44f2-9b07-9df22fab5157	<EMAIL>	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N
pbkdf2_sha256$720000$1UewFXWtGbUUYvpnz9LwJf$Dk8YfZ8VHFa6Dhn4jshPSOun5AQAfRICoc+u+0I1OQA=	\N	f	tania	Tania		t	t	2025-02-27 23:00:20+00	2025-02-27 23:00:20.85193+00	2025-02-27 23:00:38.900555+00	f	\N	7307e0aa-9cd7-44db-92c4-f833641606a5	<EMAIL>	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N
pbkdf2_sha256$720000$EJU1LTnorsgj7X2dJLqQen$HD93pwidv9lN+TIV8M6ImZ0T26D/umT/Wkm/XHif7aU=	\N	f	antonella	Antonella	Tafur	t	t	2025-02-27 23:00:53+00	2025-02-27 23:00:53.229053+00	2025-02-27 23:01:07.161598+00	f	\N	84eae7f0-ec7d-4f33-abe0-8e0eb6cffff4	<EMAIL>	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N
pbkdf2_sha256$720000$xHwrgwjudvE6ihtQ2ksatV$8cSHV4OGGUteBMyLwgsjS4brho6T9N9/YuppSfZzsTs=	\N	f	gerald	Geraldine		t	t	2025-03-28 18:44:30+00	2025-03-28 18:44:30.494021+00	2025-03-28 18:44:48.942265+00	f	\N	73899230-d542-4df9-ac5a-18d74e0d8578	<EMAIL>	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N
pbkdf2_sha256$720000$qEIeQRDiVUfUTXwGc9vu3d$iAGhJ6UG+7olGptVuHeDpL8tnjFa3fUK1UpQDinSzIw=	\N	f	hlhedgar	HERNAN EDGAR	LARICO BERMEJO	f	t	2025-03-31 15:31:46.845966+00	2025-03-31 15:31:47.027097+00	2025-03-31 15:31:47.027105+00	f	\N	26602812-27d6-4320-9d0f-0a9b8b41cc44	<EMAIL>	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N
pbkdf2_sha256$720000$9fcsUnSfk0QofK8CkT6ZT4$FK7sOqbon/rqN7/GiErijAGyhkQegvALOcZVmg3hrBU=	\N	f	dilcanaza25	LID NEIDI	CANAZA CACHICATARI	f	t	2025-04-16 00:14:15.962713+00	2025-04-16 00:14:16.138556+00	2025-04-16 00:14:16.138563+00	f	\N	2edd1b05-304c-441e-bd14-099dcb667572	<EMAIL>	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N
pbkdf2_sha256$720000$Vc0FgMMd6UgSrAlqEkuIS2$3r0NnKedqCnilz/WOogKMOJ9bTX88cD0Fi1CxNY3MKs=	\N	f	nalveal	Nestor	Alveal	f	t	2025-04-17 04:22:54.934635+00	2025-04-17 04:22:55.110684+00	2025-04-17 04:22:55.110693+00	f	\N	e8fb2887-a36a-4497-8710-6166283d2abc	<EMAIL>	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N
pbkdf2_sha256$720000$0tqmz9tJShxvbIYe7V6gR3$+2BVslIZaBSaNW2vf9nNf3SjGezbcyy5gGQ6AkHNuyM=	\N	f	seleidymamani2	Seleidy Sofia	Mamani Carcasi	f	t	2025-04-29 16:30:07.90923+00	2025-04-29 16:30:08.093527+00	2025-04-29 16:30:08.093538+00	f	\N	438fff54-df30-45b7-af07-6680975ed3f4	<EMAIL>	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N
pbkdf2_sha256$720000$kNdzwZg5kOK0MNzKu6ja63$ECr9yWpPsZTf8xHTZkppcAZefb9AiARs5kECwb2JmRM=	\N	f	laura0mdts	LAURA	MALDONADO	f	t	2025-05-17 00:55:41.169367+00	2025-05-17 00:55:41.346203+00	2025-05-17 00:55:41.346212+00	f	\N	16c8df94-b3bf-4a1d-9026-40c7dd295695	<EMAIL>	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N
pbkdf2_sha256$720000$IbDTI92KeoGYZ6HU8kClpt$bY2wXBJGUpAbzH6CDmrFy7LFoAtBAntwa70i+HZlfVo=	\N	f	doloresalmendradesjhefrymiguel	Jhefry	Dolores Almendrades	f	t	2025-05-17 19:04:49.162237+00	2025-05-17 19:04:49.347665+00	2025-05-17 19:04:49.347673+00	f	\N	196cd138-7527-4068-99d6-ef52f7ad2c17	<EMAIL>	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N
pbkdf2_sha256$720000$ZakJic1qx8CuCRSCNP760f$qh1dMYXXGXfuzHhhrIe+8HXZBZOgCBDMs1F5GjGZT+g=	\N	f	kate	Kate	Olazabal	t	t	2025-06-05 19:20:16+00	2025-06-05 19:20:16.499226+00	2025-06-05 19:21:46.702301+00	f	\N	0caeccd9-e704-4b48-a3d0-05ab8ff47d4c	<EMAIL>	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N
pbkdf2_sha256$720000$uYBp720zvLtEsYbb5vHgGG$e8zkHuX7i8+5GO2+5nz3i3DSNZXT1YG4hLmct4tiW4U=	\N	f	joel	Joel	Perca	t	t	2025-02-26 06:06:50+00	2025-02-26 06:06:50.975034+00	2025-06-05 19:23:34.40388+00	f	\N	00a5b78d-22cb-4406-b9d7-f442c38b5d40	<EMAIL>	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N
pbkdf2_sha256$720000$UJiPwdM1KzX36ZoG46jKUZ$3bsZSX1UeTzKyOGOkj505ysdl3/JqBHz+E/BcB2O4CY=	\N	f	crallaco	Vladimir	Caceres	f	t	2025-06-08 04:13:30.071302+00	2025-06-08 04:13:30.246316+00	2025-06-08 04:13:30.246323+00	f	\N	12a48c4b-f9f1-49b9-b4ba-8291bc203bb8	<EMAIL>	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N
pbkdf2_sha256$720000$teDtfVhMMIphV6N2ZtmFOi$GrHL1Z0whPeo2mfBLKL/1w1Ik2q2LP8MMQurcRui6jI=	\N	f	nmamanif1708	Berly	Concha	f	t	2025-06-14 21:35:49.126421+00	2025-06-14 21:35:49.305428+00	2025-06-14 21:35:49.305437+00	f	\N	161df90e-9a84-40e5-95ab-31757d19ec11	<EMAIL>	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N
pbkdf2_sha256$720000$Itu3bKD7IXvNa3FsbB8R6e$UfF0Cvyq5H6onSgM+0FoQqi9nMWnZ1v9ScUI2qzEGMk=	\N	f	jstiven880	jesus stiven	Quispe Ramirez	f	t	2025-07-15 06:17:38.816753+00	2025-07-15 06:17:39.005485+00	2025-07-15 06:17:39.0055+00	f	\N	0303e615-540b-47cd-8694-2735f5c0255f	<EMAIL>	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N
pbkdf2_sha256$720000$QLxDBvMzxIR0Nv7e0CNyMj$S3Tk9Kx8VPnMatQQZAqohSG7H2YWxgOwKswnwv3C4v0=	\N	f	sofia			t	t	2025-06-24 14:13:36+00	2025-06-24 14:13:36.980063+00	2025-06-24 14:14:10.029291+00	f	\N	ca38fa8b-943b-4ccd-9710-3088f9696922		\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N
pbkdf2_sha256$720000$QNeirJCdGg7qtuWwpZdeDi$7RdLYk0WbQnPaLn+hY1meDy7ATj2YThUdDLCfC4iDw4=	\N	f	automations			t	t	2025-06-28 03:53:46+00	2025-06-28 03:53:46.968525+00	2025-06-28 03:54:05.044658+00	f	\N	b5ab4d84-6187-48ce-a7be-9563d6116aa0	<EMAIL>	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N
pbkdf2_sha256$720000$6WNYP41kx5oHXa8G7ZLUxB$WeXiB+InSeWFIvqQzoecBAKbuIkCLUb0JKxwUa2OELg=	\N	f	olivermolinaobregon	Oliver Denis	Molina Obregón	f	t	2025-07-15 15:50:10.172252+00	2025-07-15 15:50:10.366842+00	2025-07-15 15:50:10.366849+00	f	\N	82f2bdb5-0a87-4983-a687-4ad056610c2f	<EMAIL>	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N
pbkdf2_sha256$720000$MNYulkQUxpsRc13dbuSwhx$EO+lHTFhQ86k50B300kzPWdJau8e+O+EHjWN9PnBP3s=	2025-06-28 03:54:31+00	t	johann	Johann	Castillo	t	t	2025-06-28 03:50:44+00	2025-06-28 03:50:45.033113+00	2025-06-28 03:55:48.526839+00	f	\N	62767bc8-1e23-4a77-a55c-ce2dc9f46c8f	<EMAIL>	+51928141807	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N
pbkdf2_sha256$720000$0CMXDXwYAZfViENvPaYcDu$dM7a7D1puYTdfE7CitDjWtEiEvvGxVd5+CTbw4qcVCM=	\N	f	Papelpilares	Pilares	Papel Alvarez	f	t	2025-07-03 17:30:22.181207+00	2025-07-03 17:30:22.354313+00	2025-07-03 17:30:22.354322+00	f	\N	2e0e215c-ed90-4bd0-a5d8-9c90005cce33	<EMAIL>	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N
pbkdf2_sha256$720000$RNBV2cefrr0qztXpqbh0T4$QDkB5jsvJY9liLLaqWpIcUZO1Ihe8LfqgMCEDVSFCcs=	\N	f	josephsebas1910	Sebastian	Peredo	f	t	2025-07-06 15:49:19.100931+00	2025-07-06 15:49:19.27848+00	2025-07-06 15:49:19.278488+00	f	\N	13aaa0c6-8361-4466-ad62-bc11db4e1c36	<EMAIL>	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N
pbkdf2_sha256$720000$GfB3xAQremM55a5sCrcbMg$aKWAIDshCyjamylwqaDyzg6vbXdZfolnZyvcPHI49XM=	\N	f	fernandokemono	ALAN FERNANDO MARTIN	MARQUINA HUAMAN	f	t	2025-07-06 20:35:24.048816+00	2025-07-06 20:35:24.221741+00	2025-07-06 20:35:24.22175+00	f	\N	f38734ba-ca79-4ef8-8a53-eea8ee2ff5af	<EMAIL>	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N
pbkdf2_sha256$720000$wUqvkIqKZsxpNPCMIP32gi$mB/jZ8hWB7ZCeLiX/diWleMwLpl7zGnxlrsr8I3xB+A=	\N	f	ronaldfabriciotr	Ronald Fabricio	Taype Reategui	f	t	2025-07-08 18:57:34.230033+00	2025-07-08 18:57:34.416777+00	2025-07-08 18:57:34.416785+00	f	\N	0f03747a-7282-4407-b93f-c6db4bf39ff2	<EMAIL>	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N
pbkdf2_sha256$720000$AvN8EbMlUQOu9xGTd2uy8m$5B/Cjps8vKd4dDjHYyrDo5XjZE6XWjGskYy26OhZ3Mg=	\N	f	gerardodrm.cv2	Gerardo David	Rojas Montero	f	t	2025-07-12 02:09:53.53598+00	2025-07-12 02:09:53.709433+00	2025-07-12 02:09:53.709442+00	f	\N	1e924469-2792-41e2-88b2-de62fd35ec8b	<EMAIL>	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N
pbkdf2_sha256$720000$jGn7Jh1Oz7PeYdB9cxBAdb$gpMipfMQhqYl63BPHgzRZj+t71MNFZVdToeiOnYW6x8=	\N	f	sebastianperedo19	Joseph	Peredo	f	t	2025-07-14 17:11:33.189073+00	2025-07-14 17:11:33.364148+00	2025-07-14 17:11:33.364156+00	f	\N	37279067-57a7-497e-8c68-377fc0d36d22	<EMAIL>	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N
pbkdf2_sha256$720000$zRepVvzcmbIwFP0qkgQ7oo$ZLn5jgSAtcggZj/IkIL00mIZw8R4OtxlWScA467MI/M=	\N	f	jasonxaviergarridolapa	Jason Xavier	Garrido Lapa	f	t	2025-07-17 04:18:10.79368+00	2025-07-17 04:18:10.973557+00	2025-07-17 04:18:10.973566+00	f	\N	cdaa2ad2-76bd-44ba-9f01-c167a4325513	<EMAIL>	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N
pbkdf2_sha256$720000$EWLXO6jlsJT7gPRvqEB3fg$5MpyHugrPpuWjrC/57TFygZDnV3UVdhFIxPWO0sseWI=	2025-07-18 22:41:28.064534+00	t	admin			t	t	2025-02-26 05:56:41.894852+00	2025-02-26 05:56:42.068773+00	2025-02-26 05:56:42.068783+00	f	\N	a9299832-2aad-4516-ade0-40bef5857143	<EMAIL>	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N
\.


--
-- Data for Name: core_user_groups; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_user_groups (id, user_id, group_id) FROM stdin;
1	26602812-27d6-4320-9d0f-0a9b8b41cc44	1
2	2edd1b05-304c-441e-bd14-099dcb667572	1
3	e8fb2887-a36a-4497-8710-6166283d2abc	1
4	438fff54-df30-45b7-af07-6680975ed3f4	1
5	16c8df94-b3bf-4a1d-9026-40c7dd295695	1
6	196cd138-7527-4068-99d6-ef52f7ad2c17	1
7	12a48c4b-f9f1-49b9-b4ba-8291bc203bb8	1
8	161df90e-9a84-40e5-95ab-31757d19ec11	1
9	2e0e215c-ed90-4bd0-a5d8-9c90005cce33	1
10	13aaa0c6-8361-4466-ad62-bc11db4e1c36	1
11	f38734ba-ca79-4ef8-8a53-eea8ee2ff5af	1
12	0f03747a-7282-4407-b93f-c6db4bf39ff2	1
13	1e924469-2792-41e2-88b2-de62fd35ec8b	1
14	37279067-57a7-497e-8c68-377fc0d36d22	1
15	0303e615-540b-47cd-8694-2735f5c0255f	1
16	82f2bdb5-0a87-4983-a687-4ad056610c2f	1
17	cdaa2ad2-76bd-44ba-9f01-c167a4325513	1
\.


--
-- Data for Name: core_user_user_permissions; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.core_user_user_permissions (id, user_id, permission_id) FROM stdin;
\.


--
-- Data for Name: django_admin_log; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.django_admin_log (id, action_time, object_id, object_repr, action_flag, change_message, content_type_id, user_id) FROM stdin;
1	2025-02-26 06:06:50.976301+00	00a5b78d-22cb-4406-b9d7-f442c38b5d40	joel	1	[{"added": {}}]	8	a9299832-2aad-4516-ade0-40bef5857143
2	2025-02-26 06:07:10.23118+00	00a5b78d-22cb-4406-b9d7-f442c38b5d40	Joel Perca	2	[{"changed": {"fields": ["First name", "Last name", "Email", "Staff status"]}}]	8	a9299832-2aad-4516-ade0-40bef5857143
3	2025-02-27 22:57:06.546338+00	c41e21f9-525a-44f2-9b07-9df22fab5157	mirella	1	[{"added": {}}]	8	a9299832-2aad-4516-ade0-40bef5857143
4	2025-02-27 22:57:48.777685+00	c41e21f9-525a-44f2-9b07-9df22fab5157	Mirella CEU	2	[{"changed": {"fields": ["First name", "Last name", "Email", "Staff status"]}}]	8	a9299832-2aad-4516-ade0-40bef5857143
5	2025-02-27 23:00:20.853201+00	7307e0aa-9cd7-44db-92c4-f833641606a5	tania	1	[{"added": {}}]	8	a9299832-2aad-4516-ade0-40bef5857143
6	2025-02-27 23:00:38.903765+00	7307e0aa-9cd7-44db-92c4-f833641606a5	Tania 	2	[{"changed": {"fields": ["First name", "Email", "Staff status"]}}]	8	a9299832-2aad-4516-ade0-40bef5857143
7	2025-02-27 23:00:53.230005+00	84eae7f0-ec7d-4f33-abe0-8e0eb6cffff4	antonella	1	[{"added": {}}]	8	a9299832-2aad-4516-ade0-40bef5857143
8	2025-02-27 23:01:07.164779+00	84eae7f0-ec7d-4f33-abe0-8e0eb6cffff4	Antonella Tafur	2	[{"changed": {"fields": ["First name", "Last name", "Email", "Staff status"]}}]	8	a9299832-2aad-4516-ade0-40bef5857143
9	2025-02-28 17:20:04.100649+00	c1075f01-f1a6-420e-b06e-b498ef3b4ec5	Gerardo Inti Lobato	3		12	a9299832-2aad-4516-ade0-40bef5857143
10	2025-03-28 18:44:30.495911+00	73899230-d542-4df9-ac5a-18d74e0d8578	gerald	1	[{"added": {}}]	8	a9299832-2aad-4516-ade0-40bef5857143
11	2025-03-28 18:44:48.94783+00	73899230-d542-4df9-ac5a-18d74e0d8578	Geraldine 	2	[{"changed": {"fields": ["First name", "Email", "Staff status"]}}]	8	a9299832-2aad-4516-ade0-40bef5857143
12	2025-04-22 04:35:40.938117+00	3424b4ac-987e-4ed4-9890-fa79603f8789	Programa de Finanzas Avanzadas	2	[{"changed": {"fields": ["Hours"]}}]	13	a9299832-2aad-4516-ade0-40bef5857143
13	2025-06-05 19:20:16.503423+00	0caeccd9-e704-4b48-a3d0-05ab8ff47d4c	kate	1	[{"added": {}}]	8	a9299832-2aad-4516-ade0-40bef5857143
14	2025-06-05 19:21:46.706776+00	0caeccd9-e704-4b48-a3d0-05ab8ff47d4c	Kate Olazabal	2	[{"changed": {"fields": ["First name", "Last name", "Email", "Staff status"]}}]	8	a9299832-2aad-4516-ade0-40bef5857143
15	2025-06-05 19:23:34.406356+00	00a5b78d-22cb-4406-b9d7-f442c38b5d40	Joel Perca	2	[{"changed": {"fields": ["password"]}}]	8	a9299832-2aad-4516-ade0-40bef5857143
16	2025-06-24 14:13:36.981638+00	ca38fa8b-943b-4ccd-9710-3088f9696922	sofia	1	[{"added": {}}]	8	a9299832-2aad-4516-ade0-40bef5857143
17	2025-06-24 14:14:10.032894+00	ca38fa8b-943b-4ccd-9710-3088f9696922	sofia	2	[{"changed": {"fields": ["Staff status"]}}]	8	a9299832-2aad-4516-ade0-40bef5857143
18	2025-06-28 03:50:45.034246+00	62767bc8-1e23-4a77-a55c-ce2dc9f46c8f	johann	1	[{"added": {}}]	8	a9299832-2aad-4516-ade0-40bef5857143
19	2025-06-28 03:52:29.014616+00	62767bc8-1e23-4a77-a55c-ce2dc9f46c8f	Johann Castillo	2	[{"changed": {"fields": ["First name", "Last name", "Email", "Phone number", "Staff status"]}}]	8	a9299832-2aad-4516-ade0-40bef5857143
20	2025-06-28 03:53:46.969604+00	b5ab4d84-6187-48ce-a7be-9563d6116aa0	automations	1	[{"added": {}}]	8	a9299832-2aad-4516-ade0-40bef5857143
21	2025-06-28 03:54:05.048603+00	b5ab4d84-6187-48ce-a7be-9563d6116aa0	automations	2	[{"changed": {"fields": ["Email", "Staff status"]}}]	8	a9299832-2aad-4516-ade0-40bef5857143
22	2025-06-28 03:55:48.530946+00	62767bc8-1e23-4a77-a55c-ce2dc9f46c8f	Johann Castillo	2	[{"changed": {"fields": ["Superuser status"]}}]	8	a9299832-2aad-4516-ade0-40bef5857143
23	2025-06-28 16:09:14.702866+00	b5ab4d84-6187-48ce-a7be-9563d6116aa0	f88e7e6bfa364cae5871623cee885149d6bef024	1	[{"added": {}}]	7	62767bc8-1e23-4a77-a55c-ce2dc9f46c8f
24	2025-06-28 16:48:38.748497+00	03021431-be41-4d87-9547-0223a09adea6	51987654321	3		8	62767bc8-1e23-4a77-a55c-ce2dc9f46c8f
\.


--
-- Data for Name: django_celery_results_chordcounter; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.django_celery_results_chordcounter (id, group_id, sub_tasks, count) FROM stdin;
\.


--
-- Data for Name: django_celery_results_groupresult; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.django_celery_results_groupresult (id, group_id, date_created, date_done, content_type, content_encoding, result) FROM stdin;
\.


--
-- Data for Name: django_celery_results_taskresult; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.django_celery_results_taskresult (id, task_id, status, content_type, content_encoding, result, date_done, traceback, meta, task_args, task_kwargs, task_name, worker, date_created, periodic_task_name) FROM stdin;
\.


--
-- Data for Name: django_content_type; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.django_content_type (id, app_label, model) FROM stdin;
1	admin	logentry
2	auth	permission
3	auth	group
4	contenttypes	contenttype
5	sessions	session
6	authtoken	token
7	authtoken	tokenproxy
8	core	user
9	core	blogcategory
10	core	blog
11	core	file
12	core	instructor
13	core	offering
14	core	event
15	core	order
16	core	student
17	core	testimonial
18	core	orderitem
19	core	enrollment
20	core	offeringmodule
21	core	modulecourse
22	core	session
23	core	sessionresource
24	core	topic
25	core	attachment
26	django_celery_results	taskresult
27	django_celery_results	chordcounter
28	django_celery_results	groupresult
29	core	benefit
30	core	blogtag
31	core	blogpost
32	core	educationalinstitution
33	core	eventschedule
34	core	eventscheduleenrollment
35	core	leadsource
36	core	major
37	core	partnership
38	core	paymentmethod
39	core	payment
40	core	template
41	core	eventreminder
42	core	broadcastmessage
43	core	term
\.


--
-- Data for Name: django_migrations; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.django_migrations (id, app, name, applied) FROM stdin;
1	contenttypes	0001_initial	2025-02-26 05:56:39.513695+00
2	contenttypes	0002_remove_content_type_name	2025-02-26 05:56:39.51756+00
3	auth	0001_initial	2025-02-26 05:56:39.53909+00
4	auth	0002_alter_permission_name_max_length	2025-02-26 05:56:39.542799+00
5	auth	0003_alter_user_email_max_length	2025-02-26 05:56:39.548398+00
6	auth	0004_alter_user_username_opts	2025-02-26 05:56:39.552563+00
7	auth	0005_alter_user_last_login_null	2025-02-26 05:56:39.55681+00
8	auth	0006_require_contenttypes_0002	2025-02-26 05:56:39.557728+00
9	auth	0007_alter_validators_add_error_messages	2025-02-26 05:56:39.561955+00
10	auth	0008_alter_user_username_max_length	2025-02-26 05:56:39.565706+00
11	auth	0009_alter_user_last_name_max_length	2025-02-26 05:56:39.569038+00
12	auth	0010_alter_group_name_max_length	2025-02-26 05:56:39.573005+00
13	auth	0011_update_proxy_permissions	2025-02-26 05:56:39.576525+00
14	auth	0012_alter_user_first_name_max_length	2025-02-26 05:56:39.579823+00
15	core	0001_initial	2025-02-26 05:56:39.754288+00
16	admin	0001_initial	2025-02-26 05:56:39.774384+00
17	admin	0002_logentry_remove_auto_add	2025-02-26 05:56:39.78755+00
18	admin	0003_logentry_add_action_flag_choices	2025-02-26 05:56:39.801061+00
19	authtoken	0001_initial	2025-02-26 05:56:39.818158+00
20	authtoken	0002_auto_20160226_1747	2025-02-26 05:56:39.864293+00
21	authtoken	0003_tokenproxy	2025-02-26 05:56:39.865898+00
22	authtoken	0004_alter_tokenproxy_options	2025-02-26 05:56:39.869215+00
23	core	0002_alter_instructor_order_alter_testimonial_order	2025-02-26 05:56:39.894814+00
24	core	0003_alter_offering_end_date_alter_offering_start_date	2025-02-26 05:56:39.919212+00
25	core	0004_enrollment_offering_enrollments_offeringmodule_and_more	2025-02-26 05:56:40.143669+00
26	core	0005_alter_offering_options_offering_objectives	2025-02-26 05:56:40.178954+00
27	core	0006_rename_frecuency_offering_frequency_and_more	2025-02-26 05:56:40.247259+00
28	core	0007_attachment_offering_instructors	2025-02-26 05:56:40.288064+00
29	core	0008_alter_offering_type	2025-02-26 05:56:40.306319+00
30	django_celery_results	0001_initial	2025-02-26 05:56:40.314385+00
31	django_celery_results	0002_add_task_name_args_kwargs	2025-02-26 05:56:40.319024+00
32	django_celery_results	0003_auto_20181106_1101	2025-02-26 05:56:40.3207+00
33	django_celery_results	0004_auto_20190516_0412	2025-02-26 05:56:40.341076+00
34	django_celery_results	0005_taskresult_worker	2025-02-26 05:56:40.34622+00
35	django_celery_results	0006_taskresult_date_created	2025-02-26 05:56:40.369106+00
36	django_celery_results	0007_remove_taskresult_hidden	2025-02-26 05:56:40.371408+00
37	django_celery_results	0008_chordcounter	2025-02-26 05:56:40.378075+00
38	django_celery_results	0009_groupresult	2025-02-26 05:56:40.424695+00
39	django_celery_results	0010_remove_duplicate_indices	2025-02-26 05:56:40.428981+00
40	django_celery_results	0011_taskresult_periodic_task_name	2025-02-26 05:56:40.431498+00
41	sessions	0001_initial	2025-02-26 05:56:40.437321+00
42	core	0009_remove_student_deleted_by_remove_student_user_and_more	2025-06-05 19:04:42.607661+00
43	core	0010_remove_offering_short_name_remove_order_paid_at_and_more	2025-06-05 19:04:43.086966+00
\.


--
-- Data for Name: django_session; Type: TABLE DATA; Schema: public; Owner: ceu-admin
--

COPY public.django_session (session_key, session_data, expire_date) FROM stdin;
vfd2thp2xvkvmwkisoaq4zztufjdbfwh	.eJxVjDkOwjAQRe_imlhexo5NSc8ZorFnhrAokbJUiLuTSCmg_e-9_1YdrkvfrTNP3Z3UWWF2OSfvGodIDQQbGyQ2DZjCElJoLXh1-s0K1icPe0sPHG6jruOwTPeid0UfdNbXkfh1Ody_gx7nfqttQkhWqkHyAsGzcQG2hURyzDZXYWNCleqKuGIhtx4LSXIxMlYg9fkCtLlBQQ:1tnAQ9:dRZWhPDKiphXJgijfl027A_S92Phid65S7wlxUAk7tU	2025-03-12 05:57:25.394129+00
fa5t8frlqnliu7gkz528kh8kxpynx9w4	.eJxVjDkOwjAQRe_imlhexo5NSc8ZorFnhrAokbJUiLuTSCmg_e-9_1YdrkvfrTNP3Z3UWWF2OSfvGodIDQQbGyQ2DZjCElJoLXh1-s0K1icPe0sPHG6jruOwTPeid0UfdNbXkfh1Ody_gx7nfqttQkhWqkHyAsGzcQG2hURyzDZXYWNCleqKuGIhtx4LSXIxMlYg9fkCtLlBQQ:1tnAYY:_PPY_VVEdCwOWx4H3tvV1cA2VttEMTQDwdvMabZZ1yI	2025-03-12 06:06:06.496984+00
99ggy6mmip0e0i8o1pk3rtz3j4matwou	.eJxVjDkOwjAQRe_imlhexo5NSc8ZorFnhrAokbJUiLuTSCmg_e-9_1YdrkvfrTNP3Z3UWWF2OSfvGodIDQQbGyQ2DZjCElJoLXh1-s0K1icPe0sPHG6jruOwTPeid0UfdNbXkfh1Ody_gx7nfqttQkhWqkHyAsGzcQG2hURyzDZXYWNCleqKuGIhtx4LSXIxMlYg9fkCtLlBQQ:1tnmmx:iysfsPb_p4vf3Rs9y6SAgX2hOD2BzOuy6MndgpYUQgI	2025-03-13 22:55:31.318677+00
i0gamuk4e6x3poxqkkrqnqwqv62voxzk	.eJxVjDkOwjAQRe_imlhexo5NSc8ZorFnhrAokbJUiLuTSCmg_e-9_1YdrkvfrTNP3Z3UWWF2OSfvGodIDQQbGyQ2DZjCElJoLXh1-s0K1icPe0sPHG6jruOwTPeid0UfdNbXkfh1Ody_gx7nfqttQkhWqkHyAsGzcQG2hURyzDZXYWNCleqKuGIhtx4LSXIxMlYg9fkCtLlBQQ:1tnnVT:yIu942SwHJAXqUwGY78DZDWLxPBoZ5FgqVKJdL8ch84	2025-03-13 23:41:31.015193+00
y9e97s51yrtsafxy1h31na7b6wokpyzx	.eJxVjDkOwjAQRe_imlhexo5NSc8ZorFnhrAokbJUiLuTSCmg_e-9_1YdrkvfrTNP3Z3UWWF2OSfvGodIDQQbGyQ2DZjCElJoLXh1-s0K1icPe0sPHG6jruOwTPeid0UfdNbXkfh1Ody_gx7nfqttQkhWqkHyAsGzcQG2hURyzDZXYWNCleqKuGIhtx4LSXIxMlYg9fkCtLlBQQ:1twu8L:WsAaweMuGqG9-nE_Zd0hzK2wYWkzmu9a-zI3nmqU3oE	2025-04-08 02:35:17.654701+00
xpcp5nbp62q6kwta9z7g3gsw5xivosrx	.eJxVjDkOwjAQRe_imlhexo5NSc8ZorFnhrAokbJUiLuTSCmg_e-9_1YdrkvfrTNP3Z3UWWF2OSfvGodIDQQbGyQ2DZjCElJoLXh1-s0K1icPe0sPHG6jruOwTPeid0UfdNbXkfh1Ody_gx7nfqttQkhWqkHyAsGzcQG2hURyzDZXYWNCleqKuGIhtx4LSXIxMlYg9fkCtLlBQQ:1u2JB1:rN2PzCGHQXXJYmNVodO-YaFoNxiVN0BhvpMFUcfWvyY	2025-04-23 00:20:23.78995+00
2d445lht9mfg3civ1e3vol8zagknzmg3	.eJxVjDkOwjAQRe_imlhexo5NSc8ZorFnhrAokbJUiLuTSCmg_e-9_1YdrkvfrTNP3Z3UWWF2OSfvGodIDQQbGyQ2DZjCElJoLXh1-s0K1icPe0sPHG6jruOwTPeid0UfdNbXkfh1Ody_gx7nfqttQkhWqkHyAsGzcQG2hURyzDZXYWNCleqKuGIhtx4LSXIxMlYg9fkCtLlBQQ:1uBwPy:V5ebCDLlOezziLNvnIhWuYxE_yaW5kTukX0CWmRtkO8	2025-05-19 14:03:38.661093+00
a1t983y8l3xu4vrwx5sxylrabu2o752z	.eJxVjDkOwjAQRe_imlhexo5NSc8ZorFnhrAokbJUiLuTSCmg_e-9_1YdrkvfrTNP3Z3UWWF2OSfvGodIDQQbGyQ2DZjCElJoLXh1-s0K1icPe0sPHG6jruOwTPeid0UfdNbXkfh1Ody_gx7nfqttQkhWqkHyAsGzcQG2hURyzDZXYWNCleqKuGIhtx4LSXIxMlYg9fkCtLlBQQ:1uLY5A:mVP4YwsVKsstmNJdpzdsOXshadV1DPNqacyjbew3paI	2025-06-15 02:05:52.053199+00
3rd2z97xeul1ytprydm1p1kvcy9tgjwq	.eJxVjDkOwjAQRe_imlhexo5NSc8ZorFnhrAokbJUiLuTSCmg_e-9_1YdrkvfrTNP3Z3UWWF2OSfvGodIDQQbGyQ2DZjCElJoLXh1-s0K1icPe0sPHG6jruOwTPeid0UfdNbXkfh1Ody_gx7nfqttQkhWqkHyAsGzcQG2hURyzDZXYWNCleqKuGIhtx4LSXIxMlYg9fkCtLlBQQ:1uNGBa:giXn8qRQONc5ZFG09XrB1tErKbM9zE_MQohFDWXitpA	2025-06-19 19:23:34.411296+00
7976zpdisqyilln8b055y11c34ogw6k5	.eJxVjDkOwjAQRe_imlhexo5NSc8ZorFnhrAokbJUiLuTSCmg_e-9_1YdrkvfrTNP3Z3UWWF2OSfvGodIDQQbGyQ2DZjCElJoLXh1-s0K1icPe0sPHG6jruOwTPeid0UfdNbXkfh1Ody_gx7nfqttQkhWqkHyAsGzcQG2hURyzDZXYWNCleqKuGIhtx4LSXIxMlYg9fkCtLlBQQ:1uTo9S:m3Sx_9x7TN0qRm5RqrnS7B4dvflot67mASBYGloKhw0	2025-07-07 20:52:26.876967+00
s099aokr2wdbl49alfrrh2ugx9a9gbth	.eJxVjDkOwjAQRe_imlhexo5NSc8ZorFnhrAokbJUiLuTSCmg_e-9_1YdrkvfrTNP3Z3UWWF2OSfvGodIDQQbGyQ2DZjCElJoLXh1-s0K1icPe0sPHG6jruOwTPeid0UfdNbXkfh1Ody_gx7nfqttQkhWqkHyAsGzcQG2hURyzDZXYWNCleqKuGIhtx4LSXIxMlYg9fkCtLlBQQ:1uVMZP:YDDGlByGf0CJyIGxm3jYFo_PGUqLeUP7ihCV2fGwPaU	2025-07-12 03:49:39.659527+00
bkv2uuy6kbpv0i0ywkk1s1uf42vqqj1o	.eJxVzDkOwjAURdG9uCYWsf09UNKzhuhPJgxKpAwVYu8QKQXU7573Mh2uS9-ts07dTczJRJdiIs5Nq843AVNqEIAbVidcaoicqzn8MkJ-6LBZueNwHS2PwzLdyG6J3dfZXkbR53lv_w56nPuvRvXgUxDhzNG1JShVnzhSEkQPpBErHQsBSBJX0eWiRLWCA5W2BvP-AD95Qto:1uVMe7:eRiWpYyNZ8YbRnFny0tli0fDHV29OH563n_oL6YvLTg	2025-07-12 03:54:31.003102+00
xjlabmqo81m6t2xcyddxnllda09z7ns2	.eJxVjDkOwjAQRe_imlhexo5NSc8ZorFnhrAokbJUiLuTSCmg_e-9_1YdrkvfrTNP3Z3UWWF2OSfvGodIDQQbGyQ2DZjCElJoLXh1-s0K1icPe0sPHG6jruOwTPeid0UfdNbXkfh1Ody_gx7nfqttQkhWqkHyAsGzcQG2hURyzDZXYWNCleqKuGIhtx4LSXIxMlYg9fkCtLlBQQ:1uctlg:GrGvyaGaLHVwDBPJkWFrpybkcoIOyh6BmCd3Ni5qAbQ	2025-08-01 22:41:28.066128+00
\.


--
-- Name: auth_group_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ceu-admin
--

SELECT pg_catalog.setval('public.auth_group_id_seq', 1, true);


--
-- Name: auth_group_permissions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ceu-admin
--

SELECT pg_catalog.setval('public.auth_group_permissions_id_seq', 1, false);


--
-- Name: auth_permission_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ceu-admin
--

SELECT pg_catalog.setval('public.auth_permission_id_seq', 172, true);


--
-- Name: core_blogpost_authors_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ceu-admin
--

SELECT pg_catalog.setval('public.core_blogpost_authors_id_seq', 1, true);


--
-- Name: core_blogpost_categories_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ceu-admin
--

SELECT pg_catalog.setval('public.core_blogpost_categories_id_seq', 1, true);


--
-- Name: core_blogpost_tags_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ceu-admin
--

SELECT pg_catalog.setval('public.core_blogpost_tags_id_seq', 1, true);


--
-- Name: core_eventschedule_partnerships_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ceu-admin
--

SELECT pg_catalog.setval('public.core_eventschedule_partnerships_id_seq', 1, false);


--
-- Name: core_eventscheduleenrollment_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ceu-admin
--

SELECT pg_catalog.setval('public.core_eventscheduleenrollment_id_seq', 1, false);


--
-- Name: core_order_benefits_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ceu-admin
--

SELECT pg_catalog.setval('public.core_order_benefits_id_seq', 1, false);


--
-- Name: core_order_lead_sources_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ceu-admin
--

SELECT pg_catalog.setval('public.core_order_lead_sources_id_seq', 1, false);


--
-- Name: core_orderitem_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ceu-admin
--

SELECT pg_catalog.setval('public.core_orderitem_id_seq', 1, false);


--
-- Name: core_user_groups_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ceu-admin
--

SELECT pg_catalog.setval('public.core_user_groups_id_seq', 17, true);


--
-- Name: core_user_user_permissions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ceu-admin
--

SELECT pg_catalog.setval('public.core_user_user_permissions_id_seq', 1, false);


--
-- Name: django_admin_log_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ceu-admin
--

SELECT pg_catalog.setval('public.django_admin_log_id_seq', 24, true);


--
-- Name: django_celery_results_chordcounter_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ceu-admin
--

SELECT pg_catalog.setval('public.django_celery_results_chordcounter_id_seq', 1, false);


--
-- Name: django_celery_results_groupresult_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ceu-admin
--

SELECT pg_catalog.setval('public.django_celery_results_groupresult_id_seq', 1, false);


--
-- Name: django_celery_results_taskresult_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ceu-admin
--

SELECT pg_catalog.setval('public.django_celery_results_taskresult_id_seq', 1, false);


--
-- Name: django_content_type_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ceu-admin
--

SELECT pg_catalog.setval('public.django_content_type_id_seq', 43, true);


--
-- Name: django_migrations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ceu-admin
--

SELECT pg_catalog.setval('public.django_migrations_id_seq', 43, true);


--
-- Name: auth_group auth_group_name_key; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.auth_group
    ADD CONSTRAINT auth_group_name_key UNIQUE (name);


--
-- Name: auth_group_permissions auth_group_permissions_group_id_permission_id_0cd325b0_uniq; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.auth_group_permissions
    ADD CONSTRAINT auth_group_permissions_group_id_permission_id_0cd325b0_uniq UNIQUE (group_id, permission_id);


--
-- Name: auth_group_permissions auth_group_permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.auth_group_permissions
    ADD CONSTRAINT auth_group_permissions_pkey PRIMARY KEY (id);


--
-- Name: auth_group auth_group_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.auth_group
    ADD CONSTRAINT auth_group_pkey PRIMARY KEY (id);


--
-- Name: auth_permission auth_permission_content_type_id_codename_01ab375a_uniq; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.auth_permission
    ADD CONSTRAINT auth_permission_content_type_id_codename_01ab375a_uniq UNIQUE (content_type_id, codename);


--
-- Name: auth_permission auth_permission_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.auth_permission
    ADD CONSTRAINT auth_permission_pkey PRIMARY KEY (id);


--
-- Name: authtoken_token authtoken_token_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.authtoken_token
    ADD CONSTRAINT authtoken_token_pkey PRIMARY KEY (key);


--
-- Name: authtoken_token authtoken_token_user_id_key; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.authtoken_token
    ADD CONSTRAINT authtoken_token_user_id_key UNIQUE (user_id);


--
-- Name: broadcast_message broadcast_message_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.broadcast_message
    ADD CONSTRAINT broadcast_message_pkey PRIMARY KEY (mid);


--
-- Name: core_attachment core_attachment_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_attachment
    ADD CONSTRAINT core_attachment_pkey PRIMARY KEY (aid);


--
-- Name: core_benefit core_benefit_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_benefit
    ADD CONSTRAINT core_benefit_pkey PRIMARY KEY (bid);


--
-- Name: core_blogcategory core_blogcategory_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_blogcategory
    ADD CONSTRAINT core_blogcategory_pkey PRIMARY KEY (bcid);


--
-- Name: core_blogcategory core_blogcategory_slug_key; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_blogcategory
    ADD CONSTRAINT core_blogcategory_slug_key UNIQUE (slug);


--
-- Name: core_blogpost_authors core_blogpost_authors_blogpost_id_instructor_id_b851125d_uniq; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_blogpost_authors
    ADD CONSTRAINT core_blogpost_authors_blogpost_id_instructor_id_b851125d_uniq UNIQUE (blogpost_id, instructor_id);


--
-- Name: core_blogpost_authors core_blogpost_authors_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_blogpost_authors
    ADD CONSTRAINT core_blogpost_authors_pkey PRIMARY KEY (id);


--
-- Name: core_blogpost_categories core_blogpost_categories_blogpost_id_blogcategory_84ec2911_uniq; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_blogpost_categories
    ADD CONSTRAINT core_blogpost_categories_blogpost_id_blogcategory_84ec2911_uniq UNIQUE (blogpost_id, blogcategory_id);


--
-- Name: core_blogpost_categories core_blogpost_categories_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_blogpost_categories
    ADD CONSTRAINT core_blogpost_categories_pkey PRIMARY KEY (id);


--
-- Name: core_blogpost core_blogpost_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_blogpost
    ADD CONSTRAINT core_blogpost_pkey PRIMARY KEY (bid);


--
-- Name: core_blogpost core_blogpost_slug_key; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_blogpost
    ADD CONSTRAINT core_blogpost_slug_key UNIQUE (slug);


--
-- Name: core_blogpost_tags core_blogpost_tags_blogpost_id_blogtag_id_f2146e6a_uniq; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_blogpost_tags
    ADD CONSTRAINT core_blogpost_tags_blogpost_id_blogtag_id_f2146e6a_uniq UNIQUE (blogpost_id, blogtag_id);


--
-- Name: core_blogpost_tags core_blogpost_tags_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_blogpost_tags
    ADD CONSTRAINT core_blogpost_tags_pkey PRIMARY KEY (id);


--
-- Name: core_blogtag core_blogtag_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_blogtag
    ADD CONSTRAINT core_blogtag_pkey PRIMARY KEY (btid);


--
-- Name: core_blogtag core_blogtag_slug_key; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_blogtag
    ADD CONSTRAINT core_blogtag_slug_key UNIQUE (slug);


--
-- Name: core_educationalinstitution core_educationalinstitution_name_key; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_educationalinstitution
    ADD CONSTRAINT core_educationalinstitution_name_key UNIQUE (name);


--
-- Name: core_educationalinstitution core_educationalinstitution_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_educationalinstitution
    ADD CONSTRAINT core_educationalinstitution_pkey PRIMARY KEY (eiid);


--
-- Name: core_enrollment core_enrollment_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_enrollment
    ADD CONSTRAINT core_enrollment_pkey PRIMARY KEY (eid);


--
-- Name: core_event core_event_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_event
    ADD CONSTRAINT core_event_pkey PRIMARY KEY (eid);


--
-- Name: core_eventreminder core_eventreminder_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_eventreminder
    ADD CONSTRAINT core_eventreminder_pkey PRIMARY KEY (rid);


--
-- Name: core_eventschedule_partnerships core_eventschedule_partn_eventschedule_id_partner_8cf9d3fe_uniq; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_eventschedule_partnerships
    ADD CONSTRAINT core_eventschedule_partn_eventschedule_id_partner_8cf9d3fe_uniq UNIQUE (eventschedule_id, partnership_id);


--
-- Name: core_eventschedule_partnerships core_eventschedule_partnerships_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_eventschedule_partnerships
    ADD CONSTRAINT core_eventschedule_partnerships_pkey PRIMARY KEY (id);


--
-- Name: core_eventschedule core_eventschedule_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_eventschedule
    ADD CONSTRAINT core_eventschedule_pkey PRIMARY KEY (esid);


--
-- Name: core_eventscheduleenrollment core_eventscheduleenrollment_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_eventscheduleenrollment
    ADD CONSTRAINT core_eventscheduleenrollment_pkey PRIMARY KEY (id);


--
-- Name: core_file core_file_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_file
    ADD CONSTRAINT core_file_pkey PRIMARY KEY (fid);


--
-- Name: core_instructor core_instructor_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_instructor
    ADD CONSTRAINT core_instructor_pkey PRIMARY KEY (iid);


--
-- Name: core_instructor core_instructor_user_id_key; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_instructor
    ADD CONSTRAINT core_instructor_user_id_key UNIQUE (user_id);


--
-- Name: core_leadsource core_leadsource_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_leadsource
    ADD CONSTRAINT core_leadsource_pkey PRIMARY KEY (lsid);


--
-- Name: core_major core_major_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_major
    ADD CONSTRAINT core_major_pkey PRIMARY KEY (mid);


--
-- Name: core_modulecourse core_modulecourse_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_modulecourse
    ADD CONSTRAINT core_modulecourse_pkey PRIMARY KEY (mcid);


--
-- Name: core_offering core_offering_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_offering
    ADD CONSTRAINT core_offering_pkey PRIMARY KEY (oid);


--
-- Name: core_offering core_offering_slug_key; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_offering
    ADD CONSTRAINT core_offering_slug_key UNIQUE (slug);


--
-- Name: core_offeringmodule core_offeringmodule_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_offeringmodule
    ADD CONSTRAINT core_offeringmodule_pkey PRIMARY KEY (omid);


--
-- Name: core_order_benefits core_order_benefits_order_id_benefit_id_907b3dcd_uniq; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_order_benefits
    ADD CONSTRAINT core_order_benefits_order_id_benefit_id_907b3dcd_uniq UNIQUE (order_id, benefit_id);


--
-- Name: core_order_benefits core_order_benefits_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_order_benefits
    ADD CONSTRAINT core_order_benefits_pkey PRIMARY KEY (id);


--
-- Name: core_order_lead_sources core_order_lead_sources_order_id_leadsource_id_90207e2e_uniq; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_order_lead_sources
    ADD CONSTRAINT core_order_lead_sources_order_id_leadsource_id_90207e2e_uniq UNIQUE (order_id, leadsource_id);


--
-- Name: core_order_lead_sources core_order_lead_sources_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_order_lead_sources
    ADD CONSTRAINT core_order_lead_sources_pkey PRIMARY KEY (id);


--
-- Name: core_order core_order_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_order
    ADD CONSTRAINT core_order_pkey PRIMARY KEY (oid);


--
-- Name: core_orderitem core_orderitem_order_id_offering_id_15a63c90_uniq; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_orderitem
    ADD CONSTRAINT core_orderitem_order_id_offering_id_15a63c90_uniq UNIQUE (order_id, offering_id);


--
-- Name: core_orderitem core_orderitem_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_orderitem
    ADD CONSTRAINT core_orderitem_pkey PRIMARY KEY (id);


--
-- Name: core_partnership core_partnership_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_partnership
    ADD CONSTRAINT core_partnership_pkey PRIMARY KEY (pid);


--
-- Name: core_payment core_payment_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_payment
    ADD CONSTRAINT core_payment_pkey PRIMARY KEY (pid);


--
-- Name: core_paymentmethod core_paymentmethod_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_paymentmethod
    ADD CONSTRAINT core_paymentmethod_pkey PRIMARY KEY (pmid);


--
-- Name: core_session core_session_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_session
    ADD CONSTRAINT core_session_pkey PRIMARY KEY (sid);


--
-- Name: core_sessionresource core_sessionresource_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_sessionresource
    ADD CONSTRAINT core_sessionresource_pkey PRIMARY KEY (rid);


--
-- Name: core_template core_template_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_template
    ADD CONSTRAINT core_template_pkey PRIMARY KEY (tid);


--
-- Name: core_term core_term_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_term
    ADD CONSTRAINT core_term_pkey PRIMARY KEY (tid);


--
-- Name: core_testimonial core_testimonial_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_testimonial
    ADD CONSTRAINT core_testimonial_pkey PRIMARY KEY (tid);


--
-- Name: core_topic core_topic_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_topic
    ADD CONSTRAINT core_topic_pkey PRIMARY KEY (tid);


--
-- Name: core_user core_user_email_key; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_user
    ADD CONSTRAINT core_user_email_key UNIQUE (email);


--
-- Name: core_user_groups core_user_groups_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_user_groups
    ADD CONSTRAINT core_user_groups_pkey PRIMARY KEY (id);


--
-- Name: core_user_groups core_user_groups_user_id_group_id_c82fcad1_uniq; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_user_groups
    ADD CONSTRAINT core_user_groups_user_id_group_id_c82fcad1_uniq UNIQUE (user_id, group_id);


--
-- Name: core_user core_user_phone_number_a06324e5_uniq; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_user
    ADD CONSTRAINT core_user_phone_number_a06324e5_uniq UNIQUE (phone_number);


--
-- Name: core_user core_user_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_user
    ADD CONSTRAINT core_user_pkey PRIMARY KEY (uid);


--
-- Name: core_user_user_permissions core_user_user_permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_user_user_permissions
    ADD CONSTRAINT core_user_user_permissions_pkey PRIMARY KEY (id);


--
-- Name: core_user_user_permissions core_user_user_permissions_user_id_permission_id_73ea0daa_uniq; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_user_user_permissions
    ADD CONSTRAINT core_user_user_permissions_user_id_permission_id_73ea0daa_uniq UNIQUE (user_id, permission_id);


--
-- Name: core_user core_user_username_key; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_user
    ADD CONSTRAINT core_user_username_key UNIQUE (username);


--
-- Name: django_admin_log django_admin_log_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.django_admin_log
    ADD CONSTRAINT django_admin_log_pkey PRIMARY KEY (id);


--
-- Name: django_celery_results_chordcounter django_celery_results_chordcounter_group_id_key; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.django_celery_results_chordcounter
    ADD CONSTRAINT django_celery_results_chordcounter_group_id_key UNIQUE (group_id);


--
-- Name: django_celery_results_chordcounter django_celery_results_chordcounter_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.django_celery_results_chordcounter
    ADD CONSTRAINT django_celery_results_chordcounter_pkey PRIMARY KEY (id);


--
-- Name: django_celery_results_groupresult django_celery_results_groupresult_group_id_key; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.django_celery_results_groupresult
    ADD CONSTRAINT django_celery_results_groupresult_group_id_key UNIQUE (group_id);


--
-- Name: django_celery_results_groupresult django_celery_results_groupresult_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.django_celery_results_groupresult
    ADD CONSTRAINT django_celery_results_groupresult_pkey PRIMARY KEY (id);


--
-- Name: django_celery_results_taskresult django_celery_results_taskresult_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.django_celery_results_taskresult
    ADD CONSTRAINT django_celery_results_taskresult_pkey PRIMARY KEY (id);


--
-- Name: django_celery_results_taskresult django_celery_results_taskresult_task_id_key; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.django_celery_results_taskresult
    ADD CONSTRAINT django_celery_results_taskresult_task_id_key UNIQUE (task_id);


--
-- Name: django_content_type django_content_type_app_label_model_76bd3d3b_uniq; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.django_content_type
    ADD CONSTRAINT django_content_type_app_label_model_76bd3d3b_uniq UNIQUE (app_label, model);


--
-- Name: django_content_type django_content_type_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.django_content_type
    ADD CONSTRAINT django_content_type_pkey PRIMARY KEY (id);


--
-- Name: django_migrations django_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.django_migrations
    ADD CONSTRAINT django_migrations_pkey PRIMARY KEY (id);


--
-- Name: django_session django_session_pkey; Type: CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.django_session
    ADD CONSTRAINT django_session_pkey PRIMARY KEY (session_key);


--
-- Name: auth_group_name_a6ea08ec_like; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX auth_group_name_a6ea08ec_like ON public.auth_group USING btree (name varchar_pattern_ops);


--
-- Name: auth_group_permissions_group_id_b120cbf9; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX auth_group_permissions_group_id_b120cbf9 ON public.auth_group_permissions USING btree (group_id);


--
-- Name: auth_group_permissions_permission_id_84c5c92e; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX auth_group_permissions_permission_id_84c5c92e ON public.auth_group_permissions USING btree (permission_id);


--
-- Name: auth_permission_content_type_id_2f476e4b; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX auth_permission_content_type_id_2f476e4b ON public.auth_permission USING btree (content_type_id);


--
-- Name: authtoken_token_key_10f0b77e_like; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX authtoken_token_key_10f0b77e_like ON public.authtoken_token USING btree (key varchar_pattern_ops);


--
-- Name: broadcast_message_deleted_by_id_1638b102; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX broadcast_message_deleted_by_id_1638b102 ON public.broadcast_message USING btree (deleted_by_id);


--
-- Name: broadcast_message_template_id_id_2b763b5a; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX broadcast_message_template_id_id_2b763b5a ON public.broadcast_message USING btree (template_id_id);


--
-- Name: core_attachment_deleted_by_id_1b525820; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_attachment_deleted_by_id_1b525820 ON public.core_attachment USING btree (deleted_by_id);


--
-- Name: core_attachment_instructor_id_90e3b766; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_attachment_instructor_id_90e3b766 ON public.core_attachment USING btree (instructor_id);


--
-- Name: core_attachment_offering_id_4f9cc9a3; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_attachment_offering_id_4f9cc9a3 ON public.core_attachment USING btree (offering_id);


--
-- Name: core_benefit_deleted_by_id_3972a8bf; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_benefit_deleted_by_id_3972a8bf ON public.core_benefit USING btree (deleted_by_id);


--
-- Name: core_blogcategory_deleted_by_id_83dcebc9; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_blogcategory_deleted_by_id_83dcebc9 ON public.core_blogcategory USING btree (deleted_by_id);


--
-- Name: core_blogcategory_parent_id_1f2ead91; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_blogcategory_parent_id_1f2ead91 ON public.core_blogcategory USING btree (parent_id);


--
-- Name: core_blogcategory_slug_f0e64699_like; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_blogcategory_slug_f0e64699_like ON public.core_blogcategory USING btree (slug varchar_pattern_ops);


--
-- Name: core_blogpost_authors_blogpost_id_e210d20d; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_blogpost_authors_blogpost_id_e210d20d ON public.core_blogpost_authors USING btree (blogpost_id);


--
-- Name: core_blogpost_authors_instructor_id_d8d110ae; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_blogpost_authors_instructor_id_d8d110ae ON public.core_blogpost_authors USING btree (instructor_id);


--
-- Name: core_blogpost_categories_blogcategory_id_5d8ad2c9; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_blogpost_categories_blogcategory_id_5d8ad2c9 ON public.core_blogpost_categories USING btree (blogcategory_id);


--
-- Name: core_blogpost_categories_blogpost_id_6aff4e54; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_blogpost_categories_blogpost_id_6aff4e54 ON public.core_blogpost_categories USING btree (blogpost_id);


--
-- Name: core_blogpost_cover_image_id_2ed1c6a7; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_blogpost_cover_image_id_2ed1c6a7 ON public.core_blogpost USING btree (cover_image_id);


--
-- Name: core_blogpost_created_by_id_1c9957b8; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_blogpost_created_by_id_1c9957b8 ON public.core_blogpost USING btree (created_by_id);


--
-- Name: core_blogpost_deleted_by_id_c868df69; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_blogpost_deleted_by_id_c868df69 ON public.core_blogpost USING btree (deleted_by_id);


--
-- Name: core_blogpost_slug_8c9d3e74_like; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_blogpost_slug_8c9d3e74_like ON public.core_blogpost USING btree (slug varchar_pattern_ops);


--
-- Name: core_blogpost_tags_blogpost_id_c521bb8e; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_blogpost_tags_blogpost_id_c521bb8e ON public.core_blogpost_tags USING btree (blogpost_id);


--
-- Name: core_blogpost_tags_blogtag_id_12a1a2b1; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_blogpost_tags_blogtag_id_12a1a2b1 ON public.core_blogpost_tags USING btree (blogtag_id);


--
-- Name: core_blogpost_thumbnail_id_73180868; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_blogpost_thumbnail_id_73180868 ON public.core_blogpost USING btree (thumbnail_id);


--
-- Name: core_blogtag_deleted_by_id_977c7778; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_blogtag_deleted_by_id_977c7778 ON public.core_blogtag USING btree (deleted_by_id);


--
-- Name: core_blogtag_slug_1a4b3b5a_like; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_blogtag_slug_1a4b3b5a_like ON public.core_blogtag USING btree (slug varchar_pattern_ops);


--
-- Name: core_educationalinstitution_deleted_by_id_115748f0; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_educationalinstitution_deleted_by_id_115748f0 ON public.core_educationalinstitution USING btree (deleted_by_id);


--
-- Name: core_educationalinstitution_name_cef19aa5_like; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_educationalinstitution_name_cef19aa5_like ON public.core_educationalinstitution USING btree (name varchar_pattern_ops);


--
-- Name: core_enrollment_deleted_by_id_055762ea; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_enrollment_deleted_by_id_055762ea ON public.core_enrollment USING btree (deleted_by_id);


--
-- Name: core_enrollment_offering_id_3bc3e4fc; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_enrollment_offering_id_3bc3e4fc ON public.core_enrollment USING btree (offering_id);


--
-- Name: core_enrollment_order_id_ecd69031; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_enrollment_order_id_ecd69031 ON public.core_enrollment USING btree (order_id);


--
-- Name: core_enrollment_user_id_676c05fa; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_enrollment_user_id_676c05fa ON public.core_enrollment USING btree (user_id);


--
-- Name: core_event_cover_image_id_2bb35b27; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_event_cover_image_id_2bb35b27 ON public.core_event USING btree (cover_image_id);


--
-- Name: core_event_deleted_by_id_8f761968; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_event_deleted_by_id_8f761968 ON public.core_event USING btree (deleted_by_id);


--
-- Name: core_event_instructor_id_157a4fe7; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_event_instructor_id_157a4fe7 ON public.core_event USING btree (instructor_id);


--
-- Name: core_event_product_id_7ee7ca48; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_event_product_id_7ee7ca48 ON public.core_event USING btree (offering_id);


--
-- Name: core_event_thumbnail_id_d94afaee; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_event_thumbnail_id_d94afaee ON public.core_event USING btree (thumbnail_id);


--
-- Name: core_eventreminder_deleted_by_id_1bd10832; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_eventreminder_deleted_by_id_1bd10832 ON public.core_eventreminder USING btree (deleted_by_id);


--
-- Name: core_eventreminder_template_id_id_8765dea5; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_eventreminder_template_id_id_8765dea5 ON public.core_eventreminder USING btree (template_id_id);


--
-- Name: core_eventschedule_cover_image_id_dc8516e0; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_eventschedule_cover_image_id_dc8516e0 ON public.core_eventschedule USING btree (cover_image_id);


--
-- Name: core_eventschedule_deleted_by_id_6bb2f552; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_eventschedule_deleted_by_id_6bb2f552 ON public.core_eventschedule USING btree (deleted_by_id);


--
-- Name: core_eventschedule_event_id_d246ea39; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_eventschedule_event_id_d246ea39 ON public.core_eventschedule USING btree (event_id);


--
-- Name: core_eventschedule_instructor_id_85bcc90d; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_eventschedule_instructor_id_85bcc90d ON public.core_eventschedule USING btree (instructor_id);


--
-- Name: core_eventschedule_partnerships_eventschedule_id_fa7e41ec; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_eventschedule_partnerships_eventschedule_id_fa7e41ec ON public.core_eventschedule_partnerships USING btree (eventschedule_id);


--
-- Name: core_eventschedule_partnerships_partnership_id_2bce068a; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_eventschedule_partnerships_partnership_id_2bce068a ON public.core_eventschedule_partnerships USING btree (partnership_id);


--
-- Name: core_eventschedule_thumbnail_id_7b5517fb; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_eventschedule_thumbnail_id_7b5517fb ON public.core_eventschedule USING btree (thumbnail_id);


--
-- Name: core_eventscheduleenrollment_deleted_by_id_8aadeff7; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_eventscheduleenrollment_deleted_by_id_8aadeff7 ON public.core_eventscheduleenrollment USING btree (deleted_by_id);


--
-- Name: core_eventscheduleenrollment_event_schedule_id_da18982c; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_eventscheduleenrollment_event_schedule_id_da18982c ON public.core_eventscheduleenrollment USING btree (event_schedule_id);


--
-- Name: core_eventscheduleenrollment_user_id_3c096d96; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_eventscheduleenrollment_user_id_3c096d96 ON public.core_eventscheduleenrollment USING btree (user_id);


--
-- Name: core_file_deleted_by_id_49511de1; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_file_deleted_by_id_49511de1 ON public.core_file USING btree (deleted_by_id);


--
-- Name: core_instructor_deleted_by_id_5d6dba7b; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_instructor_deleted_by_id_5d6dba7b ON public.core_instructor USING btree (deleted_by_id);


--
-- Name: core_instructor_profile_photo_id_d2303352; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_instructor_profile_photo_id_d2303352 ON public.core_instructor USING btree (profile_photo_id);


--
-- Name: core_leadsource_deleted_by_id_f67c1938; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_leadsource_deleted_by_id_f67c1938 ON public.core_leadsource USING btree (deleted_by_id);


--
-- Name: core_major_deleted_by_id_72dce651; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_major_deleted_by_id_72dce651 ON public.core_major USING btree (deleted_by_id);


--
-- Name: core_modulecourse_deleted_by_id_a953cc49; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_modulecourse_deleted_by_id_a953cc49 ON public.core_modulecourse USING btree (deleted_by_id);


--
-- Name: core_modulecourse_module_id_13c478df; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_modulecourse_module_id_13c478df ON public.core_modulecourse USING btree (module_id);


--
-- Name: core_offering_deleted_by_id_d64c9bba; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_offering_deleted_by_id_d64c9bba ON public.core_offering USING btree (deleted_by_id);


--
-- Name: core_offering_slug_d5ca298d_like; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_offering_slug_d5ca298d_like ON public.core_offering USING btree (slug varchar_pattern_ops);


--
-- Name: core_offering_thumbnail_id_6fb4fda8; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_offering_thumbnail_id_6fb4fda8 ON public.core_offering USING btree (thumbnail_id);


--
-- Name: core_offeringmodule_deleted_by_id_c50337bc; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_offeringmodule_deleted_by_id_c50337bc ON public.core_offeringmodule USING btree (deleted_by_id);


--
-- Name: core_offeringmodule_offering_id_92b6b2b7; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_offeringmodule_offering_id_92b6b2b7 ON public.core_offeringmodule USING btree (offering_id);


--
-- Name: core_order_benefits_benefit_id_b862fdfe; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_order_benefits_benefit_id_b862fdfe ON public.core_order_benefits USING btree (benefit_id);


--
-- Name: core_order_benefits_order_id_9ef24b13; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_order_benefits_order_id_9ef24b13 ON public.core_order_benefits USING btree (order_id);


--
-- Name: core_order_deleted_by_id_259ce6cd; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_order_deleted_by_id_259ce6cd ON public.core_order USING btree (deleted_by_id);


--
-- Name: core_order_lead_sources_leadsource_id_d7720b6d; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_order_lead_sources_leadsource_id_d7720b6d ON public.core_order_lead_sources USING btree (leadsource_id);


--
-- Name: core_order_lead_sources_order_id_2a9a5a59; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_order_lead_sources_order_id_2a9a5a59 ON public.core_order_lead_sources USING btree (order_id);


--
-- Name: core_order_owner_id_0e1dfc34; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_order_owner_id_0e1dfc34 ON public.core_order USING btree (owner_id);


--
-- Name: core_order_sales_agent_id_30d46a18; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_order_sales_agent_id_30d46a18 ON public.core_order USING btree (sales_agent_id);


--
-- Name: core_orderitem_deleted_by_id_8a191abe; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_orderitem_deleted_by_id_8a191abe ON public.core_orderitem USING btree (deleted_by_id);


--
-- Name: core_orderitem_offering_id_55c7accf; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_orderitem_offering_id_55c7accf ON public.core_orderitem USING btree (offering_id);


--
-- Name: core_orderitem_order_id_30929c10; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_orderitem_order_id_30929c10 ON public.core_orderitem USING btree (order_id);


--
-- Name: core_partnership_delegate_id_1b709901; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_partnership_delegate_id_1b709901 ON public.core_partnership USING btree (delegate_id);


--
-- Name: core_partnership_deleted_by_id_98b99cbd; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_partnership_deleted_by_id_98b99cbd ON public.core_partnership USING btree (deleted_by_id);


--
-- Name: core_partnership_institution_id_3665b600; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_partnership_institution_id_3665b600 ON public.core_partnership USING btree (institution_id);


--
-- Name: core_payment_deleted_by_id_70e44045; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_payment_deleted_by_id_70e44045 ON public.core_payment USING btree (deleted_by_id);


--
-- Name: core_payment_order_id_71322fb0; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_payment_order_id_71322fb0 ON public.core_payment USING btree (order_id);


--
-- Name: core_payment_payment_method_id_a89d92f1; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_payment_payment_method_id_a89d92f1 ON public.core_payment USING btree (payment_method_id);


--
-- Name: core_payment_voucher_id_a28e94e8; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_payment_voucher_id_a28e94e8 ON public.core_payment USING btree (voucher_id);


--
-- Name: core_paymentmethod_deleted_by_id_6ab4b7ed; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_paymentmethod_deleted_by_id_6ab4b7ed ON public.core_paymentmethod USING btree (deleted_by_id);


--
-- Name: core_session_deleted_by_id_6cb1bc77; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_session_deleted_by_id_6cb1bc77 ON public.core_session USING btree (deleted_by_id);


--
-- Name: core_session_topic_id_ce292a6e; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_session_topic_id_ce292a6e ON public.core_session USING btree (topic_id);


--
-- Name: core_sessionresource_deleted_by_id_648549b0; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_sessionresource_deleted_by_id_648549b0 ON public.core_sessionresource USING btree (deleted_by_id);


--
-- Name: core_sessionresource_file_id_2017a612; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_sessionresource_file_id_2017a612 ON public.core_sessionresource USING btree (file_id);


--
-- Name: core_sessionresource_session_id_d5ce659e; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_sessionresource_session_id_d5ce659e ON public.core_sessionresource USING btree (session_id);


--
-- Name: core_template_deleted_by_id_ae34875c; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_template_deleted_by_id_ae34875c ON public.core_template USING btree (deleted_by_id);


--
-- Name: core_template_header_image_id_8b8e3ca0; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_template_header_image_id_8b8e3ca0 ON public.core_template USING btree (header_image_id);


--
-- Name: core_term_deleted_by_id_1dc8f3a6; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_term_deleted_by_id_1dc8f3a6 ON public.core_term USING btree (deleted_by_id);


--
-- Name: core_testimonial_author_photo_id_43466c87; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_testimonial_author_photo_id_43466c87 ON public.core_testimonial USING btree (author_photo_id);


--
-- Name: core_testimonial_deleted_by_id_a0c7b091; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_testimonial_deleted_by_id_a0c7b091 ON public.core_testimonial USING btree (deleted_by_id);


--
-- Name: core_topic_course_id_a74000d8; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_topic_course_id_a74000d8 ON public.core_topic USING btree (course_id);


--
-- Name: core_topic_deleted_by_id_e8ae89d2; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_topic_deleted_by_id_e8ae89d2 ON public.core_topic USING btree (deleted_by_id);


--
-- Name: core_user_deleted_by_id_d1ddc487; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_user_deleted_by_id_d1ddc487 ON public.core_user USING btree (deleted_by_id);


--
-- Name: core_user_educational_institution_id_fcb524d4; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_user_educational_institution_id_fcb524d4 ON public.core_user USING btree (educational_institution_id);


--
-- Name: core_user_email_92a71487_like; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_user_email_92a71487_like ON public.core_user USING btree (email varchar_pattern_ops);


--
-- Name: core_user_groups_group_id_fe8c697f; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_user_groups_group_id_fe8c697f ON public.core_user_groups USING btree (group_id);


--
-- Name: core_user_groups_user_id_70b4d9b8; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_user_groups_user_id_70b4d9b8 ON public.core_user_groups USING btree (user_id);


--
-- Name: core_user_major_id_a07d37f5; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_user_major_id_a07d37f5 ON public.core_user USING btree (major_id);


--
-- Name: core_user_phone_number_a06324e5_like; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_user_phone_number_a06324e5_like ON public.core_user USING btree (phone_number varchar_pattern_ops);


--
-- Name: core_user_profile_photo_id_d4518ec7; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_user_profile_photo_id_d4518ec7 ON public.core_user USING btree (profile_photo_id);


--
-- Name: core_user_term_id_ad6bdf2a; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_user_term_id_ad6bdf2a ON public.core_user USING btree (term_id);


--
-- Name: core_user_user_permissions_permission_id_35ccf601; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_user_user_permissions_permission_id_35ccf601 ON public.core_user_user_permissions USING btree (permission_id);


--
-- Name: core_user_user_permissions_user_id_085123d3; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_user_user_permissions_user_id_085123d3 ON public.core_user_user_permissions USING btree (user_id);


--
-- Name: core_user_username_36e4f7f7_like; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX core_user_username_36e4f7f7_like ON public.core_user USING btree (username varchar_pattern_ops);


--
-- Name: django_admin_log_content_type_id_c4bce8eb; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX django_admin_log_content_type_id_c4bce8eb ON public.django_admin_log USING btree (content_type_id);


--
-- Name: django_admin_log_user_id_c564eba6; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX django_admin_log_user_id_c564eba6 ON public.django_admin_log USING btree (user_id);


--
-- Name: django_cele_date_cr_bd6c1d_idx; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX django_cele_date_cr_bd6c1d_idx ON public.django_celery_results_groupresult USING btree (date_created);


--
-- Name: django_cele_date_cr_f04a50_idx; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX django_cele_date_cr_f04a50_idx ON public.django_celery_results_taskresult USING btree (date_created);


--
-- Name: django_cele_date_do_caae0e_idx; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX django_cele_date_do_caae0e_idx ON public.django_celery_results_groupresult USING btree (date_done);


--
-- Name: django_cele_date_do_f59aad_idx; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX django_cele_date_do_f59aad_idx ON public.django_celery_results_taskresult USING btree (date_done);


--
-- Name: django_cele_status_9b6201_idx; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX django_cele_status_9b6201_idx ON public.django_celery_results_taskresult USING btree (status);


--
-- Name: django_cele_task_na_08aec9_idx; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX django_cele_task_na_08aec9_idx ON public.django_celery_results_taskresult USING btree (task_name);


--
-- Name: django_cele_worker_d54dd8_idx; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX django_cele_worker_d54dd8_idx ON public.django_celery_results_taskresult USING btree (worker);


--
-- Name: django_celery_results_chordcounter_group_id_1f70858c_like; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX django_celery_results_chordcounter_group_id_1f70858c_like ON public.django_celery_results_chordcounter USING btree (group_id varchar_pattern_ops);


--
-- Name: django_celery_results_groupresult_group_id_a085f1a9_like; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX django_celery_results_groupresult_group_id_a085f1a9_like ON public.django_celery_results_groupresult USING btree (group_id varchar_pattern_ops);


--
-- Name: django_celery_results_taskresult_task_id_de0d95bf_like; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX django_celery_results_taskresult_task_id_de0d95bf_like ON public.django_celery_results_taskresult USING btree (task_id varchar_pattern_ops);


--
-- Name: django_session_expire_date_a5c62663; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX django_session_expire_date_a5c62663 ON public.django_session USING btree (expire_date);


--
-- Name: django_session_session_key_c0390e0f_like; Type: INDEX; Schema: public; Owner: ceu-admin
--

CREATE INDEX django_session_session_key_c0390e0f_like ON public.django_session USING btree (session_key varchar_pattern_ops);


--
-- Name: auth_group_permissions auth_group_permissio_permission_id_84c5c92e_fk_auth_perm; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.auth_group_permissions
    ADD CONSTRAINT auth_group_permissio_permission_id_84c5c92e_fk_auth_perm FOREIGN KEY (permission_id) REFERENCES public.auth_permission(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: auth_group_permissions auth_group_permissions_group_id_b120cbf9_fk_auth_group_id; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.auth_group_permissions
    ADD CONSTRAINT auth_group_permissions_group_id_b120cbf9_fk_auth_group_id FOREIGN KEY (group_id) REFERENCES public.auth_group(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: auth_permission auth_permission_content_type_id_2f476e4b_fk_django_co; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.auth_permission
    ADD CONSTRAINT auth_permission_content_type_id_2f476e4b_fk_django_co FOREIGN KEY (content_type_id) REFERENCES public.django_content_type(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: authtoken_token authtoken_token_user_id_35299eff_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.authtoken_token
    ADD CONSTRAINT authtoken_token_user_id_35299eff_fk_core_user_uid FOREIGN KEY (user_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: broadcast_message broadcast_message_deleted_by_id_1638b102_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.broadcast_message
    ADD CONSTRAINT broadcast_message_deleted_by_id_1638b102_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: broadcast_message broadcast_message_template_id_id_2b763b5a_fk_core_template_tid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.broadcast_message
    ADD CONSTRAINT broadcast_message_template_id_id_2b763b5a_fk_core_template_tid FOREIGN KEY (template_id_id) REFERENCES public.core_template(tid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_attachment core_attachment_deleted_by_id_1b525820_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_attachment
    ADD CONSTRAINT core_attachment_deleted_by_id_1b525820_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_attachment core_attachment_instructor_id_90e3b766_fk_core_instructor_iid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_attachment
    ADD CONSTRAINT core_attachment_instructor_id_90e3b766_fk_core_instructor_iid FOREIGN KEY (instructor_id) REFERENCES public.core_instructor(iid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_attachment core_attachment_offering_id_4f9cc9a3_fk_core_offering_oid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_attachment
    ADD CONSTRAINT core_attachment_offering_id_4f9cc9a3_fk_core_offering_oid FOREIGN KEY (offering_id) REFERENCES public.core_offering(oid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_benefit core_benefit_deleted_by_id_3972a8bf_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_benefit
    ADD CONSTRAINT core_benefit_deleted_by_id_3972a8bf_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_blogcategory core_blogcategory_deleted_by_id_83dcebc9_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_blogcategory
    ADD CONSTRAINT core_blogcategory_deleted_by_id_83dcebc9_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_blogcategory core_blogcategory_parent_id_1f2ead91_fk_core_blogcategory_bcid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_blogcategory
    ADD CONSTRAINT core_blogcategory_parent_id_1f2ead91_fk_core_blogcategory_bcid FOREIGN KEY (parent_id) REFERENCES public.core_blogcategory(bcid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_blogpost_authors core_blogpost_author_instructor_id_d8d110ae_fk_core_inst; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_blogpost_authors
    ADD CONSTRAINT core_blogpost_author_instructor_id_d8d110ae_fk_core_inst FOREIGN KEY (instructor_id) REFERENCES public.core_instructor(iid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_blogpost_authors core_blogpost_authors_blogpost_id_e210d20d_fk_core_blogpost_bid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_blogpost_authors
    ADD CONSTRAINT core_blogpost_authors_blogpost_id_e210d20d_fk_core_blogpost_bid FOREIGN KEY (blogpost_id) REFERENCES public.core_blogpost(bid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_blogpost_categories core_blogpost_catego_blogcategory_id_5d8ad2c9_fk_core_blog; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_blogpost_categories
    ADD CONSTRAINT core_blogpost_catego_blogcategory_id_5d8ad2c9_fk_core_blog FOREIGN KEY (blogcategory_id) REFERENCES public.core_blogcategory(bcid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_blogpost_categories core_blogpost_catego_blogpost_id_6aff4e54_fk_core_blog; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_blogpost_categories
    ADD CONSTRAINT core_blogpost_catego_blogpost_id_6aff4e54_fk_core_blog FOREIGN KEY (blogpost_id) REFERENCES public.core_blogpost(bid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_blogpost core_blogpost_cover_image_id_2ed1c6a7_fk_core_file_fid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_blogpost
    ADD CONSTRAINT core_blogpost_cover_image_id_2ed1c6a7_fk_core_file_fid FOREIGN KEY (cover_image_id) REFERENCES public.core_file(fid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_blogpost core_blogpost_created_by_id_1c9957b8_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_blogpost
    ADD CONSTRAINT core_blogpost_created_by_id_1c9957b8_fk_core_user_uid FOREIGN KEY (created_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_blogpost core_blogpost_deleted_by_id_c868df69_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_blogpost
    ADD CONSTRAINT core_blogpost_deleted_by_id_c868df69_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_blogpost_tags core_blogpost_tags_blogpost_id_c521bb8e_fk_core_blogpost_bid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_blogpost_tags
    ADD CONSTRAINT core_blogpost_tags_blogpost_id_c521bb8e_fk_core_blogpost_bid FOREIGN KEY (blogpost_id) REFERENCES public.core_blogpost(bid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_blogpost_tags core_blogpost_tags_blogtag_id_12a1a2b1_fk_core_blogtag_btid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_blogpost_tags
    ADD CONSTRAINT core_blogpost_tags_blogtag_id_12a1a2b1_fk_core_blogtag_btid FOREIGN KEY (blogtag_id) REFERENCES public.core_blogtag(btid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_blogpost core_blogpost_thumbnail_id_73180868_fk_core_file_fid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_blogpost
    ADD CONSTRAINT core_blogpost_thumbnail_id_73180868_fk_core_file_fid FOREIGN KEY (thumbnail_id) REFERENCES public.core_file(fid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_blogtag core_blogtag_deleted_by_id_977c7778_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_blogtag
    ADD CONSTRAINT core_blogtag_deleted_by_id_977c7778_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_educationalinstitution core_educationalinst_deleted_by_id_115748f0_fk_core_user; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_educationalinstitution
    ADD CONSTRAINT core_educationalinst_deleted_by_id_115748f0_fk_core_user FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_enrollment core_enrollment_deleted_by_id_055762ea_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_enrollment
    ADD CONSTRAINT core_enrollment_deleted_by_id_055762ea_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_enrollment core_enrollment_offering_id_3bc3e4fc_fk_core_offering_oid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_enrollment
    ADD CONSTRAINT core_enrollment_offering_id_3bc3e4fc_fk_core_offering_oid FOREIGN KEY (offering_id) REFERENCES public.core_offering(oid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_enrollment core_enrollment_order_id_ecd69031_fk_core_order_oid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_enrollment
    ADD CONSTRAINT core_enrollment_order_id_ecd69031_fk_core_order_oid FOREIGN KEY (order_id) REFERENCES public.core_order(oid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_enrollment core_enrollment_user_id_676c05fa_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_enrollment
    ADD CONSTRAINT core_enrollment_user_id_676c05fa_fk_core_user_uid FOREIGN KEY (user_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_event core_event_cover_image_id_2bb35b27_fk_core_file_fid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_event
    ADD CONSTRAINT core_event_cover_image_id_2bb35b27_fk_core_file_fid FOREIGN KEY (cover_image_id) REFERENCES public.core_file(fid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_event core_event_deleted_by_id_8f761968_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_event
    ADD CONSTRAINT core_event_deleted_by_id_8f761968_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_event core_event_instructor_id_157a4fe7_fk_core_instructor_iid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_event
    ADD CONSTRAINT core_event_instructor_id_157a4fe7_fk_core_instructor_iid FOREIGN KEY (instructor_id) REFERENCES public.core_instructor(iid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_event core_event_offering_id_f4a51829_fk_core_offering_oid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_event
    ADD CONSTRAINT core_event_offering_id_f4a51829_fk_core_offering_oid FOREIGN KEY (offering_id) REFERENCES public.core_offering(oid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_event core_event_thumbnail_id_d94afaee_fk_core_file_fid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_event
    ADD CONSTRAINT core_event_thumbnail_id_d94afaee_fk_core_file_fid FOREIGN KEY (thumbnail_id) REFERENCES public.core_file(fid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_eventreminder core_eventreminder_deleted_by_id_1bd10832_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_eventreminder
    ADD CONSTRAINT core_eventreminder_deleted_by_id_1bd10832_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_eventreminder core_eventreminder_template_id_id_8765dea5_fk_core_template_tid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_eventreminder
    ADD CONSTRAINT core_eventreminder_template_id_id_8765dea5_fk_core_template_tid FOREIGN KEY (template_id_id) REFERENCES public.core_template(tid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_eventschedule core_eventschedule_cover_image_id_dc8516e0_fk_core_file_fid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_eventschedule
    ADD CONSTRAINT core_eventschedule_cover_image_id_dc8516e0_fk_core_file_fid FOREIGN KEY (cover_image_id) REFERENCES public.core_file(fid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_eventschedule core_eventschedule_deleted_by_id_6bb2f552_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_eventschedule
    ADD CONSTRAINT core_eventschedule_deleted_by_id_6bb2f552_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_eventschedule core_eventschedule_event_id_d246ea39_fk_core_event_eid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_eventschedule
    ADD CONSTRAINT core_eventschedule_event_id_d246ea39_fk_core_event_eid FOREIGN KEY (event_id) REFERENCES public.core_event(eid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_eventschedule core_eventschedule_instructor_id_85bcc90d_fk_core_inst; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_eventschedule
    ADD CONSTRAINT core_eventschedule_instructor_id_85bcc90d_fk_core_inst FOREIGN KEY (instructor_id) REFERENCES public.core_instructor(iid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_eventschedule_partnerships core_eventschedule_p_eventschedule_id_fa7e41ec_fk_core_even; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_eventschedule_partnerships
    ADD CONSTRAINT core_eventschedule_p_eventschedule_id_fa7e41ec_fk_core_even FOREIGN KEY (eventschedule_id) REFERENCES public.core_eventschedule(esid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_eventschedule_partnerships core_eventschedule_p_partnership_id_2bce068a_fk_core_part; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_eventschedule_partnerships
    ADD CONSTRAINT core_eventschedule_p_partnership_id_2bce068a_fk_core_part FOREIGN KEY (partnership_id) REFERENCES public.core_partnership(pid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_eventschedule core_eventschedule_thumbnail_id_7b5517fb_fk_core_file_fid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_eventschedule
    ADD CONSTRAINT core_eventschedule_thumbnail_id_7b5517fb_fk_core_file_fid FOREIGN KEY (thumbnail_id) REFERENCES public.core_file(fid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_eventscheduleenrollment core_eventscheduleen_deleted_by_id_8aadeff7_fk_core_user; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_eventscheduleenrollment
    ADD CONSTRAINT core_eventscheduleen_deleted_by_id_8aadeff7_fk_core_user FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_eventscheduleenrollment core_eventscheduleen_event_schedule_id_da18982c_fk_core_even; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_eventscheduleenrollment
    ADD CONSTRAINT core_eventscheduleen_event_schedule_id_da18982c_fk_core_even FOREIGN KEY (event_schedule_id) REFERENCES public.core_eventschedule(esid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_eventscheduleenrollment core_eventscheduleenrollment_user_id_3c096d96_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_eventscheduleenrollment
    ADD CONSTRAINT core_eventscheduleenrollment_user_id_3c096d96_fk_core_user_uid FOREIGN KEY (user_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_file core_file_deleted_by_id_49511de1_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_file
    ADD CONSTRAINT core_file_deleted_by_id_49511de1_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_instructor core_instructor_deleted_by_id_5d6dba7b_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_instructor
    ADD CONSTRAINT core_instructor_deleted_by_id_5d6dba7b_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_instructor core_instructor_profile_photo_id_d2303352_fk_core_file_fid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_instructor
    ADD CONSTRAINT core_instructor_profile_photo_id_d2303352_fk_core_file_fid FOREIGN KEY (profile_photo_id) REFERENCES public.core_file(fid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_instructor core_instructor_user_id_df102bfc_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_instructor
    ADD CONSTRAINT core_instructor_user_id_df102bfc_fk_core_user_uid FOREIGN KEY (user_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_leadsource core_leadsource_deleted_by_id_f67c1938_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_leadsource
    ADD CONSTRAINT core_leadsource_deleted_by_id_f67c1938_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_major core_major_deleted_by_id_72dce651_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_major
    ADD CONSTRAINT core_major_deleted_by_id_72dce651_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_modulecourse core_modulecourse_deleted_by_id_a953cc49_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_modulecourse
    ADD CONSTRAINT core_modulecourse_deleted_by_id_a953cc49_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_modulecourse core_modulecourse_module_id_13c478df_fk_core_offe; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_modulecourse
    ADD CONSTRAINT core_modulecourse_module_id_13c478df_fk_core_offe FOREIGN KEY (module_id) REFERENCES public.core_offeringmodule(omid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_offering core_offering_deleted_by_id_d64c9bba_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_offering
    ADD CONSTRAINT core_offering_deleted_by_id_d64c9bba_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_offering core_offering_thumbnail_id_6fb4fda8_fk_core_file_fid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_offering
    ADD CONSTRAINT core_offering_thumbnail_id_6fb4fda8_fk_core_file_fid FOREIGN KEY (thumbnail_id) REFERENCES public.core_file(fid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_offeringmodule core_offeringmodule_deleted_by_id_c50337bc_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_offeringmodule
    ADD CONSTRAINT core_offeringmodule_deleted_by_id_c50337bc_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_offeringmodule core_offeringmodule_offering_id_92b6b2b7_fk_core_offering_oid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_offeringmodule
    ADD CONSTRAINT core_offeringmodule_offering_id_92b6b2b7_fk_core_offering_oid FOREIGN KEY (offering_id) REFERENCES public.core_offering(oid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_order_benefits core_order_benefits_benefit_id_b862fdfe_fk_core_benefit_bid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_order_benefits
    ADD CONSTRAINT core_order_benefits_benefit_id_b862fdfe_fk_core_benefit_bid FOREIGN KEY (benefit_id) REFERENCES public.core_benefit(bid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_order_benefits core_order_benefits_order_id_9ef24b13_fk_core_order_oid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_order_benefits
    ADD CONSTRAINT core_order_benefits_order_id_9ef24b13_fk_core_order_oid FOREIGN KEY (order_id) REFERENCES public.core_order(oid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_order core_order_deleted_by_id_259ce6cd_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_order
    ADD CONSTRAINT core_order_deleted_by_id_259ce6cd_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_order_lead_sources core_order_lead_sour_leadsource_id_d7720b6d_fk_core_lead; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_order_lead_sources
    ADD CONSTRAINT core_order_lead_sour_leadsource_id_d7720b6d_fk_core_lead FOREIGN KEY (leadsource_id) REFERENCES public.core_leadsource(lsid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_order_lead_sources core_order_lead_sources_order_id_2a9a5a59_fk_core_order_oid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_order_lead_sources
    ADD CONSTRAINT core_order_lead_sources_order_id_2a9a5a59_fk_core_order_oid FOREIGN KEY (order_id) REFERENCES public.core_order(oid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_order core_order_owner_id_0e1dfc34_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_order
    ADD CONSTRAINT core_order_owner_id_0e1dfc34_fk_core_user_uid FOREIGN KEY (owner_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_order core_order_sales_agent_id_30d46a18_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_order
    ADD CONSTRAINT core_order_sales_agent_id_30d46a18_fk_core_user_uid FOREIGN KEY (sales_agent_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_orderitem core_orderitem_deleted_by_id_8a191abe_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_orderitem
    ADD CONSTRAINT core_orderitem_deleted_by_id_8a191abe_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_orderitem core_orderitem_offering_id_55c7accf_fk_core_offering_oid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_orderitem
    ADD CONSTRAINT core_orderitem_offering_id_55c7accf_fk_core_offering_oid FOREIGN KEY (offering_id) REFERENCES public.core_offering(oid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_orderitem core_orderitem_order_id_30929c10_fk_core_order_oid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_orderitem
    ADD CONSTRAINT core_orderitem_order_id_30929c10_fk_core_order_oid FOREIGN KEY (order_id) REFERENCES public.core_order(oid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_partnership core_partnership_delegate_id_1b709901_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_partnership
    ADD CONSTRAINT core_partnership_delegate_id_1b709901_fk_core_user_uid FOREIGN KEY (delegate_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_partnership core_partnership_deleted_by_id_98b99cbd_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_partnership
    ADD CONSTRAINT core_partnership_deleted_by_id_98b99cbd_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_partnership core_partnership_institution_id_3665b600_fk_core_educ; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_partnership
    ADD CONSTRAINT core_partnership_institution_id_3665b600_fk_core_educ FOREIGN KEY (institution_id) REFERENCES public.core_educationalinstitution(eiid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_payment core_payment_deleted_by_id_70e44045_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_payment
    ADD CONSTRAINT core_payment_deleted_by_id_70e44045_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_payment core_payment_order_id_71322fb0_fk_core_order_oid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_payment
    ADD CONSTRAINT core_payment_order_id_71322fb0_fk_core_order_oid FOREIGN KEY (order_id) REFERENCES public.core_order(oid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_payment core_payment_payment_method_id_a89d92f1_fk_core_paym; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_payment
    ADD CONSTRAINT core_payment_payment_method_id_a89d92f1_fk_core_paym FOREIGN KEY (payment_method_id) REFERENCES public.core_paymentmethod(pmid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_payment core_payment_voucher_id_a28e94e8_fk_core_file_fid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_payment
    ADD CONSTRAINT core_payment_voucher_id_a28e94e8_fk_core_file_fid FOREIGN KEY (voucher_id) REFERENCES public.core_file(fid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_paymentmethod core_paymentmethod_deleted_by_id_6ab4b7ed_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_paymentmethod
    ADD CONSTRAINT core_paymentmethod_deleted_by_id_6ab4b7ed_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_session core_session_deleted_by_id_6cb1bc77_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_session
    ADD CONSTRAINT core_session_deleted_by_id_6cb1bc77_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_session core_session_topic_id_ce292a6e_fk_core_topic_tid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_session
    ADD CONSTRAINT core_session_topic_id_ce292a6e_fk_core_topic_tid FOREIGN KEY (topic_id) REFERENCES public.core_topic(tid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_sessionresource core_sessionresource_deleted_by_id_648549b0_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_sessionresource
    ADD CONSTRAINT core_sessionresource_deleted_by_id_648549b0_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_sessionresource core_sessionresource_file_id_2017a612_fk_core_file_fid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_sessionresource
    ADD CONSTRAINT core_sessionresource_file_id_2017a612_fk_core_file_fid FOREIGN KEY (file_id) REFERENCES public.core_file(fid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_sessionresource core_sessionresource_session_id_d5ce659e_fk_core_session_sid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_sessionresource
    ADD CONSTRAINT core_sessionresource_session_id_d5ce659e_fk_core_session_sid FOREIGN KEY (session_id) REFERENCES public.core_session(sid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_template core_template_deleted_by_id_ae34875c_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_template
    ADD CONSTRAINT core_template_deleted_by_id_ae34875c_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_template core_template_header_image_id_8b8e3ca0_fk_core_file_fid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_template
    ADD CONSTRAINT core_template_header_image_id_8b8e3ca0_fk_core_file_fid FOREIGN KEY (header_image_id) REFERENCES public.core_file(fid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_term core_term_deleted_by_id_1dc8f3a6_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_term
    ADD CONSTRAINT core_term_deleted_by_id_1dc8f3a6_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_testimonial core_testimonial_author_photo_id_43466c87_fk_core_file_fid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_testimonial
    ADD CONSTRAINT core_testimonial_author_photo_id_43466c87_fk_core_file_fid FOREIGN KEY (author_photo_id) REFERENCES public.core_file(fid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_testimonial core_testimonial_deleted_by_id_a0c7b091_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_testimonial
    ADD CONSTRAINT core_testimonial_deleted_by_id_a0c7b091_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_topic core_topic_course_id_a74000d8_fk_core_modulecourse_mcid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_topic
    ADD CONSTRAINT core_topic_course_id_a74000d8_fk_core_modulecourse_mcid FOREIGN KEY (course_id) REFERENCES public.core_modulecourse(mcid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_topic core_topic_deleted_by_id_e8ae89d2_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_topic
    ADD CONSTRAINT core_topic_deleted_by_id_e8ae89d2_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_user core_user_deleted_by_id_d1ddc487_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_user
    ADD CONSTRAINT core_user_deleted_by_id_d1ddc487_fk_core_user_uid FOREIGN KEY (deleted_by_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_user core_user_educational_institut_fcb524d4_fk_core_educ; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_user
    ADD CONSTRAINT core_user_educational_institut_fcb524d4_fk_core_educ FOREIGN KEY (educational_institution_id) REFERENCES public.core_educationalinstitution(eiid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_user_groups core_user_groups_group_id_fe8c697f_fk_auth_group_id; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_user_groups
    ADD CONSTRAINT core_user_groups_group_id_fe8c697f_fk_auth_group_id FOREIGN KEY (group_id) REFERENCES public.auth_group(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_user_groups core_user_groups_user_id_70b4d9b8_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_user_groups
    ADD CONSTRAINT core_user_groups_user_id_70b4d9b8_fk_core_user_uid FOREIGN KEY (user_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_user core_user_major_id_a07d37f5_fk_core_major_mid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_user
    ADD CONSTRAINT core_user_major_id_a07d37f5_fk_core_major_mid FOREIGN KEY (major_id) REFERENCES public.core_major(mid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_user core_user_profile_photo_id_d4518ec7_fk_core_file_fid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_user
    ADD CONSTRAINT core_user_profile_photo_id_d4518ec7_fk_core_file_fid FOREIGN KEY (profile_photo_id) REFERENCES public.core_file(fid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_user core_user_term_id_ad6bdf2a_fk_core_term_tid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_user
    ADD CONSTRAINT core_user_term_id_ad6bdf2a_fk_core_term_tid FOREIGN KEY (term_id) REFERENCES public.core_term(tid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_user_user_permissions core_user_user_permi_permission_id_35ccf601_fk_auth_perm; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_user_user_permissions
    ADD CONSTRAINT core_user_user_permi_permission_id_35ccf601_fk_auth_perm FOREIGN KEY (permission_id) REFERENCES public.auth_permission(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: core_user_user_permissions core_user_user_permissions_user_id_085123d3_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.core_user_user_permissions
    ADD CONSTRAINT core_user_user_permissions_user_id_085123d3_fk_core_user_uid FOREIGN KEY (user_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: django_admin_log django_admin_log_content_type_id_c4bce8eb_fk_django_co; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.django_admin_log
    ADD CONSTRAINT django_admin_log_content_type_id_c4bce8eb_fk_django_co FOREIGN KEY (content_type_id) REFERENCES public.django_content_type(id) DEFERRABLE INITIALLY DEFERRED;


--
-- Name: django_admin_log django_admin_log_user_id_c564eba6_fk_core_user_uid; Type: FK CONSTRAINT; Schema: public; Owner: ceu-admin
--

ALTER TABLE ONLY public.django_admin_log
    ADD CONSTRAINT django_admin_log_user_id_c564eba6_fk_core_user_uid FOREIGN KEY (user_id) REFERENCES public.core_user(uid) DEFERRABLE INITIALLY DEFERRED;


--
-- PostgreSQL database dump complete
--

