"""
Email invitation services with Google Calendar integration
"""

import logging
from typing import Dict, Any
from django.conf import settings
from django.template.loader import render_to_string
from services.google.event import GoogleEventsManager
from .base import (
    InvitationServiceBase,
    InvitationResult,
    ServiceUnavailableError,
    InvalidTemplateError,
)
from core.models import EventScheduleEnrollment

logger = logging.getLogger(__name__)


class EmailInvitationService(InvitationServiceBase):
    """
    Email invitation service using Google Calendar integration
    this must work only adding an attendee to the event and automatically send the email by google
    """

    def __init__(self):
        self.google_calendar = GoogleEventsManager()

    def get_service_name(self) -> str:
        return "Email (Google Calendar)"

    def is_available(self) -> bool:
        """Check if Google Calendar service is available"""
        try:
            # Basic check for Google Calendar credentials and configuration
            return bool(
                self.google_calendar and hasattr(self.google_calendar, "service")
            )
        except Exception:
            return False

    def send_invitation(self, enrollment: EventScheduleEnrollment) -> InvitationResult:
        """
        Send email invitation with Google Calendar integration, the attendee role is only to see the meeting
        """
        try:
            # Get email address
            email = enrollment.email or (
                enrollment.user.email if enrollment.user else None
            )

            if not email:
                return InvitationResult(
                    success=False,
                    message="No email address available for invitation",
                    error_details={"error_type": "missing_email"},
                )

            # Si el email no es un email de google, no se envia la invitacion
            if not email.endswith("@gmail.com"):
                return InvitationResult(
                    success=False,
                    message="Invalid email address for invitation, not a Google email",
                    error_details={"error_type": "invalid_email"},
                )

            # Use event ext_event_reference as calendar event id
            event_id = enrollment.event_schedule.ext_event_id

            if not event_id:
                return InvitationResult(
                    success=False,
                    message="No Google Calendar event ID found for this event schedule",
                    error_details={"error_type": "missing_event_id"},
                )

            # Add attendee to existing Google Calendar event
            response = self.google_calendar.add_attendee_to_event(
                event_id=event_id,
                attendee_email=email,
                calendar_id=getattr(settings, "GOOGLE_CALENDAR_ID", "primary"),
            )

            if not response:
                return InvitationResult(
                    success=False,
                    message="Failed to add attendee to Google Calendar event",
                    error_details={"error_type": "calendar_error"},
                )

            return InvitationResult(
                success=True,
                message="Email invitation sent successfully via Google Calendar",
                external_id=response.get("id"),
            )

        except ServiceUnavailableError:
            return InvitationResult(
                success=False,
                message="Email service is currently unavailable",
                error_details={"error_type": "service_unavailable"},
                retry_after=60,  # Retry after 1 minute
            )

        except Exception as e:
            logger.error(f"Email invitation failed: {e}")
            return InvitationResult(
                success=False,
                message=f"Failed to send email invitation: {str(e)}",
                error_details={"error_type": "email_error", "details": str(e)},
            )
