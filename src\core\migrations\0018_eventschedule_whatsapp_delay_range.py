# Generated by Django 5.0.6 on 2025-07-31 23:15

import django.contrib.postgres.fields.ranges
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0017_remove_eventreminder_core_eventr_status__f30f49_idx_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="eventschedule",
            name="whatsapp_delay_range",
            field=django.contrib.postgres.fields.ranges.IntegerRangeField(
                blank=True,
                help_text="Range of delay before sending WhatsApp notifications [min-max]",
                null=True,
                verbose_name="WhatsApp Delay Range (in seconds)",
            ),
        ),
    ]
