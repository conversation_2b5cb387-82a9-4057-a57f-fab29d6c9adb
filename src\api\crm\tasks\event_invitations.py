"""
Celery tasks for event invitation management
"""

import logging
import random
from celery import shared_task
from django.utils import timezone
from django.db import transaction
from core.models import EventReminder, EventScheduleEnrollment
from api.crm.services.invitations import (
    WhatsAppInvitationService,
    EmailInvitationService,
)

logger = logging.getLogger(__name__)


def get_random_delay(min, max):
    """
    Generate a random delay between event schedule min and max seconds to prevent Meta bans

    Returns:
        int: Random delay in seconds between min and max
    """
    return random.randint(min, max)


@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def send_whatsapp_invitation(self, reminder_id: str):
    """
    Send WhatsApp invitation for a specific reminder

    Args:
        reminder_id: EventReminder UUID
    """
    try:
        local_tz = timezone.get_current_timezone()
        now = timezone.now().astimezone(local_tz)
        reminder = EventReminder.objects.select_related(
            "enrollment__event_schedule",
            "enrollment__user",
            "enrollment__event_schedule__whatsapp_template",
        ).get(rid=reminder_id)

        # Check if already sent or cancelled
        if reminder.status_whatsapp not in [
            EventReminder.PENDING,
            EventReminder.RETRYING,
        ]:
            logger.info(
                f"WhatsApp invitation {reminder_id} already processed with status: {reminder.status_whatsapp}"
            )
            return

        # Update status to retrying
        reminder.status_whatsapp = EventReminder.RETRYING
        reminder.save(update_fields=["status_whatsapp"])

        # Initialize service
        whatsapp_service = WhatsAppInvitationService()

        # Send invitation
        result = whatsapp_service.send_invitation(
            enrollment=reminder.enrollment,
            template=reminder.enrollment.event_schedule.whatsapp_template,
            variables=None,  # Variables are no longer stored in reminder
        )

        # Update reminder based on result
        with transaction.atomic():
            reminder.refresh_from_db()

            if result.success:
                reminder.status_whatsapp = EventReminder.SENT
                reminder.sent_at_whatsapp = now
                reminder.last_error_whatsapp = None
                logger.info(f"WhatsApp invitation {reminder_id} sent successfully")
            else:
                reminder.status_whatsapp = EventReminder.FAILED
                reminder.last_error_whatsapp = result.message
                reminder.retry_count_whatsapp += 1

                # Schedule retry if appropriate
                if result.retry_after and reminder.retry_count_whatsapp < 3:
                    logger.info(
                        f"Scheduling WhatsApp retry for {reminder_id} in {result.retry_after} seconds"
                    )
                    send_whatsapp_invitation.apply_async(
                        args=[reminder_id], countdown=result.retry_after
                    )
                    reminder.status_whatsapp = EventReminder.RETRYING

                logger.error(
                    f"WhatsApp invitation {reminder_id} failed: {result.message}"
                )

            reminder.save()

        return {
            "success": result.success,
            "message": result.message,
            "external_id": result.external_id,
        }

    except EventReminder.DoesNotExist:
        logger.error(f"EventReminder {reminder_id} not found")
        return {"success": False, "message": "Reminder not found"}

    except Exception as e:
        logger.error(f"Unexpected error in WhatsApp invitation {reminder_id}: {e}")

        # Update reminder with error
        try:
            reminder = EventReminder.objects.get(rid=reminder_id)
            reminder.status_whatsapp = EventReminder.FAILED
            reminder.last_error_whatsapp = "Error inesperado"
            reminder.retry_count_whatsapp += 1
            reminder.save()
        except:
            pass

        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2**self.request.retries))

        return {"success": False, "message": str(e)}


@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def send_email_invitation(self, reminder_id: str):
    """
    Send email invitation for a specific reminder

    Args:
        reminder_id: EventReminder UUID
    """
    try:
        local_tz = timezone.get_current_timezone()
        now = timezone.now().astimezone(local_tz)

        reminder = EventReminder.objects.select_related(
            "enrollment__event_schedule", "enrollment__user"
        ).get(rid=reminder_id)

        # Check if already sent or cancelled
        if reminder.status_email not in [EventReminder.PENDING, EventReminder.RETRYING]:
            logger.info(
                f"Email invitation {reminder_id} already processed with status: {reminder.status_email}"
            )
            return

        # Update status to retrying
        reminder.status_email = EventReminder.RETRYING
        reminder.save(update_fields=["status_email"])

        # Initialize service
        email_service = EmailInvitationService()

        # Send invitation
        result = email_service.send_invitation(
            enrollment=reminder.enrollment,
        )

        # Update reminder based on result
        with transaction.atomic():
            reminder.refresh_from_db()

            if result.success:
                reminder.status_email = EventReminder.SENT
                reminder.sent_at_email = now
                reminder.last_error_email = None
                logger.info(f"Email invitation {reminder_id} sent successfully")
            else:
                reminder.status_email = EventReminder.FAILED
                reminder.last_error_email = result.message
                reminder.retry_count_email += 1

                # Schedule retry if appropriate
                if result.retry_after and reminder.retry_count_email < 3:
                    logger.info(
                        f"Scheduling email retry for {reminder_id} in {result.retry_after} seconds"
                    )
                    send_email_invitation.apply_async(
                        args=[reminder_id], countdown=result.retry_after
                    )
                    reminder.status_email = EventReminder.RETRYING

                logger.error(f"Email invitation {reminder_id} failed: {result.message}")

            reminder.save()

        return {
            "success": result.success,
            "message": result.message,
            "external_id": result.external_id,
        }

    except EventReminder.DoesNotExist:
        logger.error(f"EventReminder {reminder_id} not found")
        return {"success": False, "message": "Reminder not found"}

    except Exception as e:
        logger.error(f"Unexpected error in email invitation {reminder_id}: {e}")

        # Update reminder with error
        try:
            reminder = EventReminder.objects.get(rid=reminder_id)
            reminder.status_email = EventReminder.FAILED
            reminder.last_error_email = "Error inesperado"
            reminder.retry_count_email += 1
            reminder.save()
        except:
            pass

        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2**self.request.retries))

        return {"success": False, "message": str(e)}


@shared_task
def schedule_pending_invitations():
    """
    Find and schedule pending invitations that are ready to be sent
    Uses random delays to prevent Meta bans when multiple invitations are scheduled
    """
    local_tz = timezone.get_current_timezone()
    now = timezone.now().astimezone(local_tz)
    scheduled_count = 0

    # Find WhatsApp invitations ready to send
    whatsapp_reminders = EventReminder.objects.filter(
        status_whatsapp=EventReminder.PENDING,
        enrollment__event_schedule__scheduled_datetime_whatsapp__lte=now,
        enrollment__event_schedule__whatsapp_template__isnull=False,
    ).select_related("enrollment__event_schedule")

    for reminder in whatsapp_reminders:
        if reminder.should_send_now_whatsapp() or reminder.is_ready_for_whatsapp_send():
            # Apply random delay to prevent Meta bans (min, max)
            delay_seconds = get_random_delay(
                reminder.enrollment.event_schedule.whatsapp_delay_range.lower,
                reminder.enrollment.event_schedule.whatsapp_delay_range.upper,
            )

            send_whatsapp_invitation.apply_async(
                args=[str(reminder.rid)], countdown=delay_seconds
            )

            scheduled_count += 1
            logger.info(
                f"Scheduled WhatsApp invitation for reminder {reminder.rid} "
                f"with {delay_seconds}s delay"
            )

    # Find email invitations ready to send
    email_reminders = EventReminder.objects.filter(
        status_email=EventReminder.PENDING,
        enrollment__event_schedule__scheduled_datetime_email__lte=now,
        enrollment__event_schedule__scheduled_datetime_email__isnull=False,
    ).select_related("enrollment__event_schedule")

    for reminder in email_reminders:
        if reminder.should_send_now_email() or reminder.is_ready_for_email_send():
            send_email_invitation.delay(str(reminder.rid))
            scheduled_count += 1
            logger.info(f"Scheduled email invitation for reminder {reminder.rid} ")

    logger.info(f"Scheduled {scheduled_count} pending invitations with random delays")
    return f"Scheduled {scheduled_count} invitations"


@shared_task
def process_new_enrollment_invitations(enrollment_id: int):
    """
    Process invitations for a newly created enrollment

    This task will:
    1. Check if EventReminder already exists for this enrollment
    2. If not, create one (with duplicate phone validation)
    3. Apply send_now logic based on event schedule configuration
    4. Schedule immediate invitations if criteria is met

    Args:
        enrollment_id: EventScheduleEnrollment ID
    """
    try:
        logger.info(
            f"Processing new enrollment invitations for enrollment {enrollment_id}"
        )
        enrollment = EventScheduleEnrollment.objects.select_related(
            "event_schedule", "event_schedule__whatsapp_template"
        ).get(id=enrollment_id)

        event_schedule = enrollment.event_schedule
        local_tz = timezone.get_current_timezone()
        now = timezone.now().astimezone(local_tz)

        # Check if event has already ended - no invitations should be sent
        if event_schedule.end_date < now:
            logger.info(
                f"Event {event_schedule.name} has already ended. No invitations will be sent."
            )
            return (
                f"Event has ended. No invitations sent for enrollment {enrollment_id}"
            )

        # Check for existing reminder for this enrollment
        existing_reminder = EventReminder.objects.filter(
            enrollment=enrollment, deleted=False
        ).first()

        if existing_reminder:
            logger.info(f"EventReminder already exists for enrollment {enrollment_id}")
            reminder = existing_reminder
        else:
            # Check for duplicate phone number in the same event schedule
            if enrollment.phone_number:
                duplicate_reminder = EventReminder.objects.filter(
                    enrollment__event_schedule=event_schedule,
                    enrollment__phone_number=enrollment.phone_number,
                    deleted=False,
                ).first()

                if duplicate_reminder:
                    logger.info(
                        f"Phone number {enrollment.phone_number} already has a reminder for event {event_schedule.name}. "
                        f"Skipping to avoid duplicate WhatsApp messages."
                    )
                    return f"Duplicate phone number detected. No reminder created for enrollment {enrollment_id}"

            # Create new EventReminder
            with transaction.atomic():
                reminder = EventReminder.objects.create(
                    enrollment=enrollment,
                    status_whatsapp=EventReminder.PENDING,
                    status_email=EventReminder.PENDING,
                )
                logger.info(
                    f"Created new EventReminder {reminder.rid} for enrollment {enrollment_id}"
                )

        scheduled_count = 0

        # Check if there are multiple pending reminders for the same event schedule
        # to determine if we need to apply delays to prevent Meta bans
        pending_reminders_count = EventReminder.objects.filter(
            enrollment__event_schedule=event_schedule,
            status_whatsapp=EventReminder.PENDING,
            deleted=False,
        ).count()

        use_delay = pending_reminders_count > 1

        # Check WhatsApp invitation
        if (
            event_schedule.whatsapp_template
            and reminder.status_whatsapp == EventReminder.PENDING
            and reminder.should_send_now_whatsapp()
        ):
            if use_delay:
                # Apply random delay to prevent Meta bans when multiple enrollments exist
                delay_seconds = get_random_delay(
                    event_schedule.whatsapp_delay_range.lower,
                    event_schedule.whatsapp_delay_range.upper,
                )
                send_whatsapp_invitation.apply_async(
                    args=[str(reminder.rid)], countdown=delay_seconds
                )
                logger.info(
                    f"Scheduled WhatsApp invitation for enrollment {enrollment_id} "
                    f"with {delay_seconds}s delay (multiple pending reminders detected)"
                )
            else:
                # Single enrollment - send immediately as before
                send_whatsapp_invitation.delay(str(reminder.rid))
                logger.info(
                    f"Scheduled immediate WhatsApp invitation for enrollment {enrollment_id}"
                )
            scheduled_count += 1

        # Check email invitation
        if (
            event_schedule.scheduled_datetime_email
            and reminder.status_email == EventReminder.PENDING
            and reminder.should_send_now_email()
        ):
            if use_delay:
                send_email_invitation.delay(str(reminder.rid))
                logger.info(
                    f"Scheduled email invitation for enrollment {enrollment_id} "
                )
            else:
                # Single enrollment - send immediately as before
                send_email_invitation.delay(str(reminder.rid))
                logger.info(
                    f"Scheduled immediate email invitation for enrollment {enrollment_id}"
                )
            scheduled_count += 1

        if scheduled_count == 0:
            logger.info(
                f"No immediate invitations scheduled for enrollment {enrollment_id}. "
                f"Reminders will be sent according to schedule."
            )

        return f"Processed {scheduled_count} immediate invitations for enrollment {enrollment_id}"

    except EventScheduleEnrollment.DoesNotExist:
        logger.error(f"EventScheduleEnrollment {enrollment_id} not found")
        return f"Enrollment {enrollment_id} not found"
    except Exception as e:
        logger.error(f"Error processing enrollment {enrollment_id}: {e}")
        return f"Error: {str(e)}"


@shared_task
def retry_failed_invitations(
    event_schedule_id: str = None, invitation_type: str = None
):
    """
    Retry failed invitations for a specific event schedule or all failed invitations
    Uses random delays to prevent Meta bans when multiple retries are scheduled

    Args:
        event_schedule_id: Optional EventSchedule UUID to filter by
        invitation_type: Optional type ('whatsapp' or 'email') to filter by
    """
    filters = {}

    if event_schedule_id:
        filters["enrollment__event_schedule__esid"] = event_schedule_id

    retry_count = 0

    # Retry WhatsApp invitations
    if not invitation_type or invitation_type == "whatsapp":
        whatsapp_filters = {
            **filters,
            "status_whatsapp": EventReminder.FAILED,
            "retry_count_whatsapp__lt": 3,
            "enrollment__event_schedule__whatsapp_template__isnull": False,
        }

        failed_whatsapp = EventReminder.objects.filter(**whatsapp_filters)

        for reminder in failed_whatsapp:
            # reset status before retry
            reminder.status_whatsapp = EventReminder.PENDING
            reminder.save(update_fields=["status_whatsapp"])

            # Apply random delay to prevent Meta bans during bulk retries
            delay_seconds = get_random_delay(
                reminder.enrollment.event_schedule.whatsapp_delay_range.lower,
                reminder.enrollment.event_schedule.whatsapp_delay_range.upper,
            )

            send_whatsapp_invitation.apply_async(
                args=[str(reminder.rid)], countdown=delay_seconds
            )
            retry_count += 1
            logger.info(
                f"Scheduled WhatsApp retry for reminder {reminder.rid} "
                f"with {delay_seconds}s delay"
            )

    # Retry email invitations
    if not invitation_type or invitation_type == "email":
        email_filters = {
            **filters,
            "status_email": EventReminder.FAILED,
            "retry_count_email__lt": 3,
            "enrollment__event_schedule__scheduled_datetime_email__isnull": False,
        }

        failed_email = EventReminder.objects.filter(**email_filters)

        for reminder in failed_email:
            # reset status before retry
            reminder.status_email = EventReminder.PENDING
            reminder.save(update_fields=["status_email"])

            send_email_invitation.delay(str(reminder.rid))
            retry_count += 1
            logger.info(f"Scheduled email retry for reminder {reminder.rid} ")

    logger.info(f"Scheduled {retry_count} failed invitation retries with random delays")
    return f"Scheduled {retry_count} retries"
