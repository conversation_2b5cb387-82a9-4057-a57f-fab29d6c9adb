services:
  portals-api:
    build:
      context: ..
      dockerfile: src/Dockerfile
    ports:
      - "8004:8000"
    environment:
      - DEBUG=${DEBUG}
      - STAGE=${STAGE:-PRODUCTION}
      - SECRET_KEY=${SECRET_KEY}
      - ALLOWED_HOSTS=${ALLOWED_HOSTS}
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_HOST=${POSTGRES_HOST}
      - POSTGRES_PORT=${POSTGRES_PORT}
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS}
      - MINIO_ENDPOINT=${MINIO_ENDPOINT}
      - MINIO_PUBLIC_ENDPOINT=${MINIO_PUBLIC_ENDPOINT}
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - MINIO_SECURE=${MINIO_SECURE}
      - MINIO_REGION=${MINIO_REGION}
      - PYTHONPATH=/home/<USER>/app
      - DJANGO_STATIC_ROOT=/home/<USER>/app/staticfiles
      - DJANGO_SUPERUSER_USERNAME=${DJANGO_SUPERUSER_USERNAME}
      - DJANGO_SUPERUSER_EMAIL=${DJANGO_SUPERUSER_EMAIL}
      - DJANGO_SUPERUSER_PASSWORD=${DJANGO_SUPERUSER_PASSWORD}
      - CSRF_TRUSTED_ORIGINS=${CSRF_TRUSTED_ORIGINS}
      - CELERY_BROKER_URL=${CELERY_BROKER_URL}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
      - EMAIL_HOST=${EMAIL_HOST}
      - EMAIL_PORT=${EMAIL_PORT}
      - EMAIL_USE_TLS=${EMAIL_USE_TLS}
      - EMAIL_HOST_USER=${EMAIL_HOST_USER}
      - EMAIL_HOST_PASSWORD=${EMAIL_HOST_PASSWORD}
      - DEFAULT_FROM_EMAIL=${DEFAULT_FROM_EMAIL}
      - APP_HOST=${APP_HOST}
      - MP_ACCESS_TOKEN=${MP_ACCESS_TOKEN}
      - MP_PUBLIC_KEY=${MP_PUBLIC_KEY}
      - PAYPAL_BASE_URL=${PAYPAL_BASE_URL}
      - PAYPAL_CLIENT_ID=${PAYPAL_CLIENT_ID}
      - PAYPAL_CLIENT_SECRET=${PAYPAL_CLIENT_SECRET}
      - EXCHANGE_RATE_API_KEY=${EXCHANGE_RATE_API_KEY}
      - MONTHLY_SALES_TARGET=${MONTHLY_SALES_TARGET}
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - REDIS_DB=${REDIS_DB}
      - REDIS_CACHE_TIMEOUT=${REDIS_CACHE_TIMEOUT}
      - REDIS_MAX_CONNECTIONS=${REDIS_MAX_CONNECTIONS}
      
    volumes:
      - app_data:/home/<USER>/app
      - static_volume:/home/<USER>/app/staticfiles
      - /home/<USER>/apps/portals/secrets:/home/<USER>/app/secrets
    networks:
      - ceu-network

volumes:
  app_data:
  static_volume:

networks:
  ceu-network:
    external: true