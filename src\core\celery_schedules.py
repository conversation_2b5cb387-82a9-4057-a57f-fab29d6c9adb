from celery.schedules import crontab

CELERYBEAT_SCHEDULE = {
    "hello-world": {
        "task": "api.website.tasks.notification.hello_world",
        "schedule": crontab(minute="*/1"),  # Every 1 minute
    },
    "schedule_pending_invitations": {
        "task": "api.crm.tasks.event_invitations.schedule_pending_invitations",
        "schedule": crontab(minute="*/2"),  # Every 2 minutes
    },
    "check_tokechat_availability": {
        "task": "api.crm.tasks.tokechat.check_tokechat_availability",
        "schedule": crontab(minute="*/5"),  # Every 5 minutes
    },
    "sync_tokechat_flows": {
        "task": "api.crm.tasks.tokechat.sync_tokechat_flows",
        "schedule": crontab(minute="*/1"),  # Every 1 minutes
    },
}
