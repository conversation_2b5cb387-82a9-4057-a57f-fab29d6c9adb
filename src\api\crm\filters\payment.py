from django_filters import rest_framework as filters
from core.models import Payment


class CrmPaymentFilter(filters.FilterSet):
    status = filters.CharFilter(method="filter_by_status")
    order = filters.CharFilter(method="filter_by_order")
    owner = filters.CharFilter(method="filter_by_owner")

    def filter_by_order(self, queryset, name, value):
        return queryset.filter(order__oid=value)

    def filter_by_status(self, queryset, name, value):
        if value == "paid":
            return queryset.filter(is_paid=True)
        elif value == "pending":
            return queryset.filter(is_paid=False)
        return queryset

    def filter_by_owner(self, queryset, name, value):
        return queryset.filter(order__owner=value)

    class Meta:
        model = Payment
        fields = ["status", "order", "owner"]
