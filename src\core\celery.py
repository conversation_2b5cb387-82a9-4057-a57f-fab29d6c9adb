from __future__ import absolute_import, unicode_literals
import os
from celery import Celery
from .celery_schedules import CELERYBEAT_SCHEDULE


os.environ.setdefault("DJANGO_SETTINGS_MODULE", "core.settings")

app = Celery("core")
app.config_from_object("django.conf:settings", namespace="CELERY")
app.conf.beat_schedule = CELERYBEAT_SCHEDULE

# Autodiscover tasks from Django apps
app.autodiscover_tasks()

# Explicitly include our custom task modules
app.autodiscover_tasks([
    'api.crm.tasks.classroom',
    'api.website.tasks.notification',
])


@app.task(bind=True, ignore_result=True)
def debug_task(self):
    print(f"Request: {self.request!r}")
